# @vue-mini 框架使用指南

基于官方文档的正确使用方式和最佳实践。

## 安装和配置

### 1. 安装依赖

```bash
pnpm install @vue-mini/core @vue-mini/pinia
```

### 2. 构建工具配置

本项目使用 weapp-vite 作为构建工具，支持：
- TypeScript 编译
- Tailwind CSS v4
- 自动化构建流程
- 热重载开发

## 应用入口 (app.ts)

```typescript
import { createApp } from '@vue-mini/core'
import { createPinia } from '@vue-mini/pinia'

const pinia = createPinia()

createApp({
  globalData: {
    version: '1.0.0'
  },
  
  setup(option: WechatMiniprogram.App.LaunchShowOption) {
    // 手动安装 Pinia（必须）
    pinia.install()
    
    onLaunch((launchOption) => {
      console.log('应用启动', launchOption)
    })
    
    onShow((showOption) => {
      console.log('应用显示', showOption)
    })
    
    onHide(() => {
      console.log('应用隐藏')
    })
    
    return {}
  }
})
```

## 页面定义 (definePage)

### 基础用法

```typescript
import { definePage, ref, onLoad, onShow } from '@vue-mini/core'

definePage({
  setup() {
    // 响应式数据
    const count = ref(0)
    const loading = ref(false)
    
    // 生命周期钩子
    onLoad((query) => {
      console.log('页面加载', query)
    })
    
    onShow(() => {
      console.log('页面显示')
    })
    
    // 方法
    const increment = () => {
      count.value++
    }
    
    // 返回数据和方法
    return {
      count,
      loading,
      increment
    }
  }
})
```

### 带查询参数类型转换

```typescript
import { definePage, ref, onLoad } from '@vue-mini/core'

type User = {
  id: number
  name: string
}

definePage({
  queries: {
    id: Number,
    name: String,
    active: Boolean,
    user: Object as PropType<User>
  },
  
  setup(query) {
    // query 已经自动转换类型
    console.log('查询参数:', query)
    
    const data = ref(null)
    
    onLoad(() => {
      // 使用转换后的查询参数
      if (query.id) {
        loadData(query.id)
      }
    })
    
    const loadData = async (id: number) => {
      // 加载数据逻辑
    }
    
    return {
      data,
      loadData
    }
  }
})
```

## 组件定义 (defineComponent)

### 基础组件

```typescript
import { defineComponent, ref } from '@vue-mini/core'

type User = {
  id: number
  name: string
}

defineComponent({
  properties: {
    title: String,
    count: {
      type: Number,
      value: 0
    },
    user: Object as PropType<User>
  },
  
  emits: {
    change: (value: number) => true,
    click: () => true
  },
  
  setup(props, { emit }) {
    const localCount = ref(props.count)
    
    const handleClick = () => {
      localCount.value++
      emit('change', localCount.value)
      emit('click')
    }
    
    return {
      localCount,
      handleClick
    }
  }
})
```

## 状态管理 (Pinia)

### 定义 Store

```typescript
import { ref } from '@vue-mini/core'
import { defineStore } from '@vue-mini/pinia'

export const useCounterStore = defineStore('counter', () => {
  const count = ref(0)
  
  function increment() {
    count.value++
  }
  
  function decrement() {
    count.value--
  }
  
  return {
    count,
    increment,
    decrement
  }
}, {
  persist: ['count'] // 持久化存储
})
```

### 在页面中使用 Store

```typescript
import { definePage } from '@vue-mini/core'
import { storeToRefs } from '@vue-mini/pinia'
import { useCounterStore } from '../../store/counter'

definePage({
  setup() {
    const counterStore = useCounterStore()
    const { count } = storeToRefs(counterStore)
    
    return {
      count,
      increment: counterStore.increment,
      decrement: counterStore.decrement
    }
  }
})
```

## 路由导航

```typescript
import { definePage } from '@vue-mini/core'

definePage({
  setup() {
    const router = useRouter()
    
    const navigateToDetail = () => {
      // 方式1：对象形式
      router.push({
        path: '/pages/detail/detail',
        query: { id: 1, name: 'test' }
      })
      
      // 方式2：URL 字符串
      router.push('/pages/detail/detail?id=1&name=test')
    }
    
    return {
      navigateToDetail
    }
  }
})
```

## 请求处理

```typescript
import { definePage } from '@vue-mini/core'

definePage({
  setup() {
    const fetchData = async () => {
      try {
        const response = await request.get('/api/data')
        console.log('数据:', response.data)
      } catch (error) {
        console.error('请求失败:', error)
      }
    }
    
    return {
      fetchData
    }
  }
})
```

## 重要注意事项

### 1. 响应式数据
- 使用 `ref()` 创建响应式数据
- 修改数据时使用 `.value`
- 不要使用传统的 `data` 和 `setData()`

### 2. 生命周期
- 使用组合式 API 的生命周期钩子
- 在 `setup()` 函数中调用

### 3. Pinia 集成
- 使用 @vue-mini/pinia 包
- 在 store 中使用 defineStore 定义状态
- 支持持久化配置

### 4. 类型安全
- 充分利用 TypeScript 类型系统
- 使用 `PropType` 定义复杂类型
- 为查询参数定义正确的类型

### 5. 组件通信
- 使用 `emits` 定义事件
- 使用 `emit()` 触发事件
- 使用 `props` 接收父组件数据

这个指南基于 @vue-mini 官方文档，确保了正确的使用方式和最佳实践。
