class Qs {
  constructor(e, t) {
    this._sigmaWords = [1634760805, 857760878, 2036477234, 1797285236], this._block = new Uint8Array(64), this._blockUsed = 64, this._x = new Uint32Array(16);
    const r = new Uint32Array(16);
    r[0] = this._sigmaWords[0], r[1] = this._sigmaWords[1], r[2] = this._sigmaWords[2], r[3] = this._sigmaWords[3], r[4] = tt(e, 0), r[5] = tt(e, 4), r[6] = tt(e, 8), r[7] = tt(e, 12), r[8] = tt(e, 16), r[9] = tt(e, 20), r[10] = tt(e, 24), r[11] = tt(e, 28), r[12] = 0, t.length === 12 ? (r[13] = tt(t, 0), r[14] = tt(t, 4), r[15] = tt(t, 8)) : (r[13] = 0, r[14] = tt(t, 0), r[15] = tt(t, 4)), this._input = r;
  }
  getBytes(e) {
    const t = new Uint8Array(e);
    for (let r = 0; r < e; r++)
      this._blockUsed === 64 && (this.generateBlock(), this._blockUsed = 0), t[r] = this._block[this._blockUsed], this._blockUsed++;
    return t;
  }
  generateBlock() {
    const e = this._input, t = this._x, r = this._block;
    t.set(e);
    for (let s = 20; s > 0; s -= 2)
      Tt(t, 0, 4, 8, 12), Tt(t, 1, 5, 9, 13), Tt(t, 2, 6, 10, 14), Tt(t, 3, 7, 11, 15), Tt(t, 0, 5, 10, 15), Tt(t, 1, 6, 11, 12), Tt(t, 2, 7, 8, 13), Tt(t, 3, 4, 9, 14);
    for (let s = 16; s--; )
      t[s] += e[s];
    for (let s = 16; s--; )
      no(r, 4 * s, t[s]);
    e[12] += 1, e[12] || (e[13] += 1);
  }
  encrypt(e) {
    const t = e.length, r = new Uint8Array(t);
    let s = 0;
    const n = this._block;
    for (; s < t; ) {
      this.generateBlock();
      const u = Math.min(t - s, 64);
      for (let o = 0; o < u; o++)
        r[s] = e[s] ^ n[o], s++;
    }
    return r;
  }
}
function Tt(i, e, t, r, s) {
  i[e] += i[t], i[s] = vr(i[s] ^ i[e], 16), i[r] += i[s], i[t] = vr(i[t] ^ i[r], 12), i[e] += i[t], i[s] = vr(i[s] ^ i[e], 8), i[r] += i[s], i[t] = vr(i[t] ^ i[r], 7);
}
function tt(i, e) {
  return i[e] | i[e + 1] << 8 | i[e + 2] << 16 | i[e + 3] << 24;
}
function no(i, e, t) {
  i[e] = t, t >>>= 8, i[e + 1] = t, t >>>= 8, i[e + 2] = t, t >>>= 8, i[e + 3] = t;
}
function vr(i, e) {
  return i << e | i >>> 32 - e;
}
var ea = {}, Wr = {};
Wr.byteLength = oo;
Wr.toByteArray = uo;
Wr.fromByteArray = lo;
var xt = [], ut = [], so = typeof Uint8Array < "u" ? Uint8Array : Array, Qr = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
for (var Jt = 0, ao = Qr.length; Jt < ao; ++Jt)
  xt[Jt] = Qr[Jt], ut[Qr.charCodeAt(Jt)] = Jt;
ut[45] = 62;
ut[95] = 63;
function ta(i) {
  var e = i.length;
  if (e % 4 > 0)
    throw new Error("Invalid string. Length must be a multiple of 4");
  var t = i.indexOf("=");
  t === -1 && (t = e);
  var r = t === e ? 0 : 4 - t % 4;
  return [t, r];
}
function oo(i) {
  var e = ta(i), t = e[0], r = e[1];
  return (t + r) * 3 / 4 - r;
}
function ho(i, e, t) {
  return (e + t) * 3 / 4 - t;
}
function uo(i) {
  var e, t = ta(i), r = t[0], s = t[1], n = new so(ho(i, r, s)), u = 0, o = s > 0 ? r - 4 : r, p;
  for (p = 0; p < o; p += 4)
    e = ut[i.charCodeAt(p)] << 18 | ut[i.charCodeAt(p + 1)] << 12 | ut[i.charCodeAt(p + 2)] << 6 | ut[i.charCodeAt(p + 3)], n[u++] = e >> 16 & 255, n[u++] = e >> 8 & 255, n[u++] = e & 255;
  return s === 2 && (e = ut[i.charCodeAt(p)] << 2 | ut[i.charCodeAt(p + 1)] >> 4, n[u++] = e & 255), s === 1 && (e = ut[i.charCodeAt(p)] << 10 | ut[i.charCodeAt(p + 1)] << 4 | ut[i.charCodeAt(p + 2)] >> 2, n[u++] = e >> 8 & 255, n[u++] = e & 255), n;
}
function fo(i) {
  return xt[i >> 18 & 63] + xt[i >> 12 & 63] + xt[i >> 6 & 63] + xt[i & 63];
}
function co(i, e, t) {
  for (var r, s = [], n = e; n < t; n += 3)
    r = (i[n] << 16 & 16711680) + (i[n + 1] << 8 & 65280) + (i[n + 2] & 255), s.push(fo(r));
  return s.join("");
}
function lo(i) {
  for (var e, t = i.length, r = t % 3, s = [], n = 16383, u = 0, o = t - r; u < o; u += n)
    s.push(co(i, u, u + n > o ? o : u + n));
  return r === 1 ? (e = i[t - 1], s.push(
    xt[e >> 2] + xt[e << 4 & 63] + "=="
  )) : r === 2 && (e = (i[t - 2] << 8) + i[t - 1], s.push(
    xt[e >> 10] + xt[e >> 4 & 63] + xt[e << 2 & 63] + "="
  )), s.join("");
}
var mn = {};
/*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> */
mn.read = function(i, e, t, r, s) {
  var n, u, o = s * 8 - r - 1, p = (1 << o) - 1, y = p >> 1, f = -7, v = t ? s - 1 : 0, m = t ? -1 : 1, w = i[e + v];
  for (v += m, n = w & (1 << -f) - 1, w >>= -f, f += o; f > 0; n = n * 256 + i[e + v], v += m, f -= 8)
    ;
  for (u = n & (1 << -f) - 1, n >>= -f, f += r; f > 0; u = u * 256 + i[e + v], v += m, f -= 8)
    ;
  if (n === 0)
    n = 1 - y;
  else {
    if (n === p)
      return u ? NaN : (w ? -1 : 1) * (1 / 0);
    u = u + Math.pow(2, r), n = n - y;
  }
  return (w ? -1 : 1) * u * Math.pow(2, n - r);
};
mn.write = function(i, e, t, r, s, n) {
  var u, o, p, y = n * 8 - s - 1, f = (1 << y) - 1, v = f >> 1, m = s === 23 ? Math.pow(2, -24) - Math.pow(2, -77) : 0, w = r ? 0 : n - 1, b = r ? 1 : -1, x = e < 0 || e === 0 && 1 / e < 0 ? 1 : 0;
  for (e = Math.abs(e), isNaN(e) || e === 1 / 0 ? (o = isNaN(e) ? 1 : 0, u = f) : (u = Math.floor(Math.log(e) / Math.LN2), e * (p = Math.pow(2, -u)) < 1 && (u--, p *= 2), u + v >= 1 ? e += m / p : e += m * Math.pow(2, 1 - v), e * p >= 2 && (u++, p /= 2), u + v >= f ? (o = 0, u = f) : u + v >= 1 ? (o = (e * p - 1) * Math.pow(2, s), u = u + v) : (o = e * Math.pow(2, v - 1) * Math.pow(2, s), u = 0)); s >= 8; i[t + w] = o & 255, w += b, o /= 256, s -= 8)
    ;
  for (u = u << s | o, y += s; y > 0; i[t + w] = u & 255, w += b, u /= 256, y -= 8)
    ;
  i[t + w - b] |= x * 128;
};
/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> Aboukhadijeh <https://feross.org>
 * @license  MIT
 */
(function(i) {
  const e = Wr, t = mn, r = typeof Symbol == "function" && typeof Symbol.for == "function" ? Symbol.for("nodejs.util.inspect.custom") : null;
  i.Buffer = f, i.SlowBuffer = T, i.INSPECT_MAX_BYTES = 50;
  const s = **********;
  i.kMaxLength = s;
  const { Uint8Array: n, ArrayBuffer: u, SharedArrayBuffer: o } = globalThis;
  f.TYPED_ARRAY_SUPPORT = p(), !f.TYPED_ARRAY_SUPPORT && typeof console < "u" && typeof console.error == "function" && console.error(
    "This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."
  );
  function p() {
    try {
      const c = new n(1), a = { foo: function() {
        return 42;
      } };
      return Object.setPrototypeOf(a, n.prototype), Object.setPrototypeOf(c, a), c.foo() === 42;
    } catch {
      return !1;
    }
  }
  Object.defineProperty(f.prototype, "parent", {
    enumerable: !0,
    get: function() {
      if (f.isBuffer(this))
        return this.buffer;
    }
  }), Object.defineProperty(f.prototype, "offset", {
    enumerable: !0,
    get: function() {
      if (f.isBuffer(this))
        return this.byteOffset;
    }
  });
  function y(c) {
    if (c > s)
      throw new RangeError('The value "' + c + '" is invalid for option "size"');
    const a = new n(c);
    return Object.setPrototypeOf(a, f.prototype), a;
  }
  function f(c, a, h) {
    if (typeof c == "number") {
      if (typeof a == "string")
        throw new TypeError(
          'The "string" argument must be of type string. Received type number'
        );
      return b(c);
    }
    return v(c, a, h);
  }
  f.poolSize = 8192;
  function v(c, a, h) {
    if (typeof c == "string")
      return x(c, a);
    if (u.isView(c))
      return E(c);
    if (c == null)
      throw new TypeError(
        "The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type " + typeof c
      );
    if (Fe(c, u) || c && Fe(c.buffer, u) || typeof o < "u" && (Fe(c, o) || c && Fe(c.buffer, o)))
      return B(c, a, h);
    if (typeof c == "number")
      throw new TypeError(
        'The "value" argument must not be of type number. Received type number'
      );
    const g = c.valueOf && c.valueOf();
    if (g != null && g !== c)
      return f.from(g, a, h);
    const F = A(c);
    if (F) return F;
    if (typeof Symbol < "u" && Symbol.toPrimitive != null && typeof c[Symbol.toPrimitive] == "function")
      return f.from(c[Symbol.toPrimitive]("string"), a, h);
    throw new TypeError(
      "The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type " + typeof c
    );
  }
  f.from = function(c, a, h) {
    return v(c, a, h);
  }, Object.setPrototypeOf(f.prototype, n.prototype), Object.setPrototypeOf(f, n);
  function m(c) {
    if (typeof c != "number")
      throw new TypeError('"size" argument must be of type number');
    if (c < 0)
      throw new RangeError('The value "' + c + '" is invalid for option "size"');
  }
  function w(c, a, h) {
    return m(c), c <= 0 ? y(c) : a !== void 0 ? typeof h == "string" ? y(c).fill(a, h) : y(c).fill(a) : y(c);
  }
  f.alloc = function(c, a, h) {
    return w(c, a, h);
  };
  function b(c) {
    return m(c), y(c < 0 ? 0 : k(c) | 0);
  }
  f.allocUnsafe = function(c) {
    return b(c);
  }, f.allocUnsafeSlow = function(c) {
    return b(c);
  };
  function x(c, a) {
    if ((typeof a != "string" || a === "") && (a = "utf8"), !f.isEncoding(a))
      throw new TypeError("Unknown encoding: " + a);
    const h = D(c, a) | 0;
    let g = y(h);
    const F = g.write(c, a);
    return F !== h && (g = g.slice(0, F)), g;
  }
  function S(c) {
    const a = c.length < 0 ? 0 : k(c.length) | 0, h = y(a);
    for (let g = 0; g < a; g += 1)
      h[g] = c[g] & 255;
    return h;
  }
  function E(c) {
    if (Fe(c, n)) {
      const a = new n(c);
      return B(a.buffer, a.byteOffset, a.byteLength);
    }
    return S(c);
  }
  function B(c, a, h) {
    if (a < 0 || c.byteLength < a)
      throw new RangeError('"offset" is outside of buffer bounds');
    if (c.byteLength < a + (h || 0))
      throw new RangeError('"length" is outside of buffer bounds');
    let g;
    return a === void 0 && h === void 0 ? g = new n(c) : h === void 0 ? g = new n(c, a) : g = new n(c, a, h), Object.setPrototypeOf(g, f.prototype), g;
  }
  function A(c) {
    if (f.isBuffer(c)) {
      const a = k(c.length) | 0, h = y(a);
      return h.length === 0 || c.copy(h, 0, 0, a), h;
    }
    if (c.length !== void 0)
      return typeof c.length != "number" || dt(c.length) ? y(0) : S(c);
    if (c.type === "Buffer" && Array.isArray(c.data))
      return S(c.data);
  }
  function k(c) {
    if (c >= s)
      throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x" + s.toString(16) + " bytes");
    return c | 0;
  }
  function T(c) {
    return +c != c && (c = 0), f.alloc(+c);
  }
  f.isBuffer = function(a) {
    return a != null && a._isBuffer === !0 && a !== f.prototype;
  }, f.compare = function(a, h) {
    if (Fe(a, n) && (a = f.from(a, a.offset, a.byteLength)), Fe(h, n) && (h = f.from(h, h.offset, h.byteLength)), !f.isBuffer(a) || !f.isBuffer(h))
      throw new TypeError(
        'The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array'
      );
    if (a === h) return 0;
    let g = a.length, F = h.length;
    for (let M = 0, O = Math.min(g, F); M < O; ++M)
      if (a[M] !== h[M]) {
        g = a[M], F = h[M];
        break;
      }
    return g < F ? -1 : F < g ? 1 : 0;
  }, f.isEncoding = function(a) {
    switch (String(a).toLowerCase()) {
      case "hex":
      case "utf8":
      case "utf-8":
      case "ascii":
      case "latin1":
      case "binary":
      case "base64":
      case "ucs2":
      case "ucs-2":
      case "utf16le":
      case "utf-16le":
        return !0;
      default:
        return !1;
    }
  }, f.concat = function(a, h) {
    if (!Array.isArray(a))
      throw new TypeError('"list" argument must be an Array of Buffers');
    if (a.length === 0)
      return f.alloc(0);
    let g;
    if (h === void 0)
      for (h = 0, g = 0; g < a.length; ++g)
        h += a[g].length;
    const F = f.allocUnsafe(h);
    let M = 0;
    for (g = 0; g < a.length; ++g) {
      let O = a[g];
      if (Fe(O, n))
        M + O.length > F.length ? (f.isBuffer(O) || (O = f.from(O)), O.copy(F, M)) : n.prototype.set.call(
          F,
          O,
          M
        );
      else if (f.isBuffer(O))
        O.copy(F, M);
      else
        throw new TypeError('"list" argument must be an Array of Buffers');
      M += O.length;
    }
    return F;
  };
  function D(c, a) {
    if (f.isBuffer(c))
      return c.length;
    if (u.isView(c) || Fe(c, u))
      return c.byteLength;
    if (typeof c != "string")
      throw new TypeError(
        'The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type ' + typeof c
      );
    const h = c.length, g = arguments.length > 2 && arguments[2] === !0;
    if (!g && h === 0) return 0;
    let F = !1;
    for (; ; )
      switch (a) {
        case "ascii":
        case "latin1":
        case "binary":
          return h;
        case "utf8":
        case "utf-8":
          return st(c).length;
        case "ucs2":
        case "ucs-2":
        case "utf16le":
        case "utf-16le":
          return h * 2;
        case "hex":
          return h >>> 1;
        case "base64":
          return et(c).length;
        default:
          if (F)
            return g ? -1 : st(c).length;
          a = ("" + a).toLowerCase(), F = !0;
      }
  }
  f.byteLength = D;
  function q(c, a, h) {
    let g = !1;
    if ((a === void 0 || a < 0) && (a = 0), a > this.length || ((h === void 0 || h > this.length) && (h = this.length), h <= 0) || (h >>>= 0, a >>>= 0, h <= a))
      return "";
    for (c || (c = "utf8"); ; )
      switch (c) {
        case "hex":
          return le(this, a, h);
        case "utf8":
        case "utf-8":
          return N(this, a, h);
        case "ascii":
          return Me(this, a, h);
        case "latin1":
        case "binary":
          return ue(this, a, h);
        case "base64":
          return ie(this, a, h);
        case "ucs2":
        case "ucs-2":
        case "utf16le":
        case "utf-16le":
          return Ee(this, a, h);
        default:
          if (g) throw new TypeError("Unknown encoding: " + c);
          c = (c + "").toLowerCase(), g = !0;
      }
  }
  f.prototype._isBuffer = !0;
  function R(c, a, h) {
    const g = c[a];
    c[a] = c[h], c[h] = g;
  }
  f.prototype.swap16 = function() {
    const a = this.length;
    if (a % 2 !== 0)
      throw new RangeError("Buffer size must be a multiple of 16-bits");
    for (let h = 0; h < a; h += 2)
      R(this, h, h + 1);
    return this;
  }, f.prototype.swap32 = function() {
    const a = this.length;
    if (a % 4 !== 0)
      throw new RangeError("Buffer size must be a multiple of 32-bits");
    for (let h = 0; h < a; h += 4)
      R(this, h, h + 3), R(this, h + 1, h + 2);
    return this;
  }, f.prototype.swap64 = function() {
    const a = this.length;
    if (a % 8 !== 0)
      throw new RangeError("Buffer size must be a multiple of 64-bits");
    for (let h = 0; h < a; h += 8)
      R(this, h, h + 7), R(this, h + 1, h + 6), R(this, h + 2, h + 5), R(this, h + 3, h + 4);
    return this;
  }, f.prototype.toString = function() {
    const a = this.length;
    return a === 0 ? "" : arguments.length === 0 ? N(this, 0, a) : q.apply(this, arguments);
  }, f.prototype.toLocaleString = f.prototype.toString, f.prototype.equals = function(a) {
    if (!f.isBuffer(a)) throw new TypeError("Argument must be a Buffer");
    return this === a ? !0 : f.compare(this, a) === 0;
  }, f.prototype.inspect = function() {
    let a = "";
    const h = i.INSPECT_MAX_BYTES;
    return a = this.toString("hex", 0, h).replace(/(.{2})/g, "$1 ").trim(), this.length > h && (a += " ... "), "<Buffer " + a + ">";
  }, r && (f.prototype[r] = f.prototype.inspect), f.prototype.compare = function(a, h, g, F, M) {
    if (Fe(a, n) && (a = f.from(a, a.offset, a.byteLength)), !f.isBuffer(a))
      throw new TypeError(
        'The "target" argument must be one of type Buffer or Uint8Array. Received type ' + typeof a
      );
    if (h === void 0 && (h = 0), g === void 0 && (g = a ? a.length : 0), F === void 0 && (F = 0), M === void 0 && (M = this.length), h < 0 || g > a.length || F < 0 || M > this.length)
      throw new RangeError("out of range index");
    if (F >= M && h >= g)
      return 0;
    if (F >= M)
      return -1;
    if (h >= g)
      return 1;
    if (h >>>= 0, g >>>= 0, F >>>= 0, M >>>= 0, this === a) return 0;
    let O = M - F, Y = g - h;
    const te = Math.min(O, Y), Ae = this.slice(F, M), Re = a.slice(h, g);
    for (let be = 0; be < te; ++be)
      if (Ae[be] !== Re[be]) {
        O = Ae[be], Y = Re[be];
        break;
      }
    return O < Y ? -1 : Y < O ? 1 : 0;
  };
  function G(c, a, h, g, F) {
    if (c.length === 0) return -1;
    if (typeof h == "string" ? (g = h, h = 0) : h > ********** ? h = ********** : h < -2147483648 && (h = -2147483648), h = +h, dt(h) && (h = F ? 0 : c.length - 1), h < 0 && (h = c.length + h), h >= c.length) {
      if (F) return -1;
      h = c.length - 1;
    } else if (h < 0)
      if (F) h = 0;
      else return -1;
    if (typeof a == "string" && (a = f.from(a, g)), f.isBuffer(a))
      return a.length === 0 ? -1 : J(c, a, h, g, F);
    if (typeof a == "number")
      return a = a & 255, typeof n.prototype.indexOf == "function" ? F ? n.prototype.indexOf.call(c, a, h) : n.prototype.lastIndexOf.call(c, a, h) : J(c, [a], h, g, F);
    throw new TypeError("val must be string, number or Buffer");
  }
  function J(c, a, h, g, F) {
    let M = 1, O = c.length, Y = a.length;
    if (g !== void 0 && (g = String(g).toLowerCase(), g === "ucs2" || g === "ucs-2" || g === "utf16le" || g === "utf-16le")) {
      if (c.length < 2 || a.length < 2)
        return -1;
      M = 2, O /= 2, Y /= 2, h /= 2;
    }
    function te(Re, be) {
      return M === 1 ? Re[be] : Re.readUInt16BE(be * M);
    }
    let Ae;
    if (F) {
      let Re = -1;
      for (Ae = h; Ae < O; Ae++)
        if (te(c, Ae) === te(a, Re === -1 ? 0 : Ae - Re)) {
          if (Re === -1 && (Re = Ae), Ae - Re + 1 === Y) return Re * M;
        } else
          Re !== -1 && (Ae -= Ae - Re), Re = -1;
    } else
      for (h + Y > O && (h = O - Y), Ae = h; Ae >= 0; Ae--) {
        let Re = !0;
        for (let be = 0; be < Y; be++)
          if (te(c, Ae + be) !== te(a, be)) {
            Re = !1;
            break;
          }
        if (Re) return Ae;
      }
    return -1;
  }
  f.prototype.includes = function(a, h, g) {
    return this.indexOf(a, h, g) !== -1;
  }, f.prototype.indexOf = function(a, h, g) {
    return G(this, a, h, g, !0);
  }, f.prototype.lastIndexOf = function(a, h, g) {
    return G(this, a, h, g, !1);
  };
  function se(c, a, h, g) {
    h = Number(h) || 0;
    const F = c.length - h;
    g ? (g = Number(g), g > F && (g = F)) : g = F;
    const M = a.length;
    g > M / 2 && (g = M / 2);
    let O;
    for (O = 0; O < g; ++O) {
      const Y = parseInt(a.substr(O * 2, 2), 16);
      if (dt(Y)) return O;
      c[h + O] = Y;
    }
    return O;
  }
  function Z(c, a, h, g) {
    return at(st(a, c.length - h), c, h, g);
  }
  function X(c, a, h, g) {
    return at(Ue(a), c, h, g);
  }
  function ee(c, a, h, g) {
    return at(et(a), c, h, g);
  }
  function re(c, a, h, g) {
    return at(Ce(a, c.length - h), c, h, g);
  }
  f.prototype.write = function(a, h, g, F) {
    if (h === void 0)
      F = "utf8", g = this.length, h = 0;
    else if (g === void 0 && typeof h == "string")
      F = h, g = this.length, h = 0;
    else if (isFinite(h))
      h = h >>> 0, isFinite(g) ? (g = g >>> 0, F === void 0 && (F = "utf8")) : (F = g, g = void 0);
    else
      throw new Error(
        "Buffer.write(string, encoding, offset[, length]) is no longer supported"
      );
    const M = this.length - h;
    if ((g === void 0 || g > M) && (g = M), a.length > 0 && (g < 0 || h < 0) || h > this.length)
      throw new RangeError("Attempt to write outside buffer bounds");
    F || (F = "utf8");
    let O = !1;
    for (; ; )
      switch (F) {
        case "hex":
          return se(this, a, h, g);
        case "utf8":
        case "utf-8":
          return Z(this, a, h, g);
        case "ascii":
        case "latin1":
        case "binary":
          return X(this, a, h, g);
        case "base64":
          return ee(this, a, h, g);
        case "ucs2":
        case "ucs-2":
        case "utf16le":
        case "utf-16le":
          return re(this, a, h, g);
        default:
          if (O) throw new TypeError("Unknown encoding: " + F);
          F = ("" + F).toLowerCase(), O = !0;
      }
  }, f.prototype.toJSON = function() {
    return {
      type: "Buffer",
      data: Array.prototype.slice.call(this._arr || this, 0)
    };
  };
  function ie(c, a, h) {
    return a === 0 && h === c.length ? e.fromByteArray(c) : e.fromByteArray(c.slice(a, h));
  }
  function N(c, a, h) {
    h = Math.min(c.length, h);
    const g = [];
    let F = a;
    for (; F < h; ) {
      const M = c[F];
      let O = null, Y = M > 239 ? 4 : M > 223 ? 3 : M > 191 ? 2 : 1;
      if (F + Y <= h) {
        let te, Ae, Re, be;
        switch (Y) {
          case 1:
            M < 128 && (O = M);
            break;
          case 2:
            te = c[F + 1], (te & 192) === 128 && (be = (M & 31) << 6 | te & 63, be > 127 && (O = be));
            break;
          case 3:
            te = c[F + 1], Ae = c[F + 2], (te & 192) === 128 && (Ae & 192) === 128 && (be = (M & 15) << 12 | (te & 63) << 6 | Ae & 63, be > 2047 && (be < 55296 || be > 57343) && (O = be));
            break;
          case 4:
            te = c[F + 1], Ae = c[F + 2], Re = c[F + 3], (te & 192) === 128 && (Ae & 192) === 128 && (Re & 192) === 128 && (be = (M & 15) << 18 | (te & 63) << 12 | (Ae & 63) << 6 | Re & 63, be > 65535 && be < 1114112 && (O = be));
        }
      }
      O === null ? (O = 65533, Y = 1) : O > 65535 && (O -= 65536, g.push(O >>> 10 & 1023 | 55296), O = 56320 | O & 1023), g.push(O), F += Y;
    }
    return ge(g);
  }
  const ye = 4096;
  function ge(c) {
    const a = c.length;
    if (a <= ye)
      return String.fromCharCode.apply(String, c);
    let h = "", g = 0;
    for (; g < a; )
      h += String.fromCharCode.apply(
        String,
        c.slice(g, g += ye)
      );
    return h;
  }
  function Me(c, a, h) {
    let g = "";
    h = Math.min(c.length, h);
    for (let F = a; F < h; ++F)
      g += String.fromCharCode(c[F] & 127);
    return g;
  }
  function ue(c, a, h) {
    let g = "";
    h = Math.min(c.length, h);
    for (let F = a; F < h; ++F)
      g += String.fromCharCode(c[F]);
    return g;
  }
  function le(c, a, h) {
    const g = c.length;
    (!a || a < 0) && (a = 0), (!h || h < 0 || h > g) && (h = g);
    let F = "";
    for (let M = a; M < h; ++M)
      F += _[c[M]];
    return F;
  }
  function Ee(c, a, h) {
    const g = c.slice(a, h);
    let F = "";
    for (let M = 0; M < g.length - 1; M += 2)
      F += String.fromCharCode(g[M] + g[M + 1] * 256);
    return F;
  }
  f.prototype.slice = function(a, h) {
    const g = this.length;
    a = ~~a, h = h === void 0 ? g : ~~h, a < 0 ? (a += g, a < 0 && (a = 0)) : a > g && (a = g), h < 0 ? (h += g, h < 0 && (h = 0)) : h > g && (h = g), h < a && (h = a);
    const F = this.subarray(a, h);
    return Object.setPrototypeOf(F, f.prototype), F;
  };
  function he(c, a, h) {
    if (c % 1 !== 0 || c < 0) throw new RangeError("offset is not uint");
    if (c + a > h) throw new RangeError("Trying to access beyond buffer length");
  }
  f.prototype.readUintLE = f.prototype.readUIntLE = function(a, h, g) {
    a = a >>> 0, h = h >>> 0, g || he(a, h, this.length);
    let F = this[a], M = 1, O = 0;
    for (; ++O < h && (M *= 256); )
      F += this[a + O] * M;
    return F;
  }, f.prototype.readUintBE = f.prototype.readUIntBE = function(a, h, g) {
    a = a >>> 0, h = h >>> 0, g || he(a, h, this.length);
    let F = this[a + --h], M = 1;
    for (; h > 0 && (M *= 256); )
      F += this[a + --h] * M;
    return F;
  }, f.prototype.readUint8 = f.prototype.readUInt8 = function(a, h) {
    return a = a >>> 0, h || he(a, 1, this.length), this[a];
  }, f.prototype.readUint16LE = f.prototype.readUInt16LE = function(a, h) {
    return a = a >>> 0, h || he(a, 2, this.length), this[a] | this[a + 1] << 8;
  }, f.prototype.readUint16BE = f.prototype.readUInt16BE = function(a, h) {
    return a = a >>> 0, h || he(a, 2, this.length), this[a] << 8 | this[a + 1];
  }, f.prototype.readUint32LE = f.prototype.readUInt32LE = function(a, h) {
    return a = a >>> 0, h || he(a, 4, this.length), (this[a] | this[a + 1] << 8 | this[a + 2] << 16) + this[a + 3] * 16777216;
  }, f.prototype.readUint32BE = f.prototype.readUInt32BE = function(a, h) {
    return a = a >>> 0, h || he(a, 4, this.length), this[a] * 16777216 + (this[a + 1] << 16 | this[a + 2] << 8 | this[a + 3]);
  }, f.prototype.readBigUInt64LE = l(function(a) {
    a = a >>> 0, ne(a, "offset");
    const h = this[a], g = this[a + 7];
    (h === void 0 || g === void 0) && ce(a, this.length - 8);
    const F = h + this[++a] * 2 ** 8 + this[++a] * 2 ** 16 + this[++a] * 2 ** 24, M = this[++a] + this[++a] * 2 ** 8 + this[++a] * 2 ** 16 + g * 2 ** 24;
    return BigInt(F) + (BigInt(M) << BigInt(32));
  }), f.prototype.readBigUInt64BE = l(function(a) {
    a = a >>> 0, ne(a, "offset");
    const h = this[a], g = this[a + 7];
    (h === void 0 || g === void 0) && ce(a, this.length - 8);
    const F = h * 2 ** 24 + this[++a] * 2 ** 16 + this[++a] * 2 ** 8 + this[++a], M = this[++a] * 2 ** 24 + this[++a] * 2 ** 16 + this[++a] * 2 ** 8 + g;
    return (BigInt(F) << BigInt(32)) + BigInt(M);
  }), f.prototype.readIntLE = function(a, h, g) {
    a = a >>> 0, h = h >>> 0, g || he(a, h, this.length);
    let F = this[a], M = 1, O = 0;
    for (; ++O < h && (M *= 256); )
      F += this[a + O] * M;
    return M *= 128, F >= M && (F -= Math.pow(2, 8 * h)), F;
  }, f.prototype.readIntBE = function(a, h, g) {
    a = a >>> 0, h = h >>> 0, g || he(a, h, this.length);
    let F = h, M = 1, O = this[a + --F];
    for (; F > 0 && (M *= 256); )
      O += this[a + --F] * M;
    return M *= 128, O >= M && (O -= Math.pow(2, 8 * h)), O;
  }, f.prototype.readInt8 = function(a, h) {
    return a = a >>> 0, h || he(a, 1, this.length), this[a] & 128 ? (255 - this[a] + 1) * -1 : this[a];
  }, f.prototype.readInt16LE = function(a, h) {
    a = a >>> 0, h || he(a, 2, this.length);
    const g = this[a] | this[a + 1] << 8;
    return g & 32768 ? g | 4294901760 : g;
  }, f.prototype.readInt16BE = function(a, h) {
    a = a >>> 0, h || he(a, 2, this.length);
    const g = this[a + 1] | this[a] << 8;
    return g & 32768 ? g | 4294901760 : g;
  }, f.prototype.readInt32LE = function(a, h) {
    return a = a >>> 0, h || he(a, 4, this.length), this[a] | this[a + 1] << 8 | this[a + 2] << 16 | this[a + 3] << 24;
  }, f.prototype.readInt32BE = function(a, h) {
    return a = a >>> 0, h || he(a, 4, this.length), this[a] << 24 | this[a + 1] << 16 | this[a + 2] << 8 | this[a + 3];
  }, f.prototype.readBigInt64LE = l(function(a) {
    a = a >>> 0, ne(a, "offset");
    const h = this[a], g = this[a + 7];
    (h === void 0 || g === void 0) && ce(a, this.length - 8);
    const F = this[a + 4] + this[a + 5] * 2 ** 8 + this[a + 6] * 2 ** 16 + (g << 24);
    return (BigInt(F) << BigInt(32)) + BigInt(h + this[++a] * 2 ** 8 + this[++a] * 2 ** 16 + this[++a] * 2 ** 24);
  }), f.prototype.readBigInt64BE = l(function(a) {
    a = a >>> 0, ne(a, "offset");
    const h = this[a], g = this[a + 7];
    (h === void 0 || g === void 0) && ce(a, this.length - 8);
    const F = (h << 24) + // Overflow
    this[++a] * 2 ** 16 + this[++a] * 2 ** 8 + this[++a];
    return (BigInt(F) << BigInt(32)) + BigInt(this[++a] * 2 ** 24 + this[++a] * 2 ** 16 + this[++a] * 2 ** 8 + g);
  }), f.prototype.readFloatLE = function(a, h) {
    return a = a >>> 0, h || he(a, 4, this.length), t.read(this, a, !0, 23, 4);
  }, f.prototype.readFloatBE = function(a, h) {
    return a = a >>> 0, h || he(a, 4, this.length), t.read(this, a, !1, 23, 4);
  }, f.prototype.readDoubleLE = function(a, h) {
    return a = a >>> 0, h || he(a, 8, this.length), t.read(this, a, !0, 52, 8);
  }, f.prototype.readDoubleBE = function(a, h) {
    return a = a >>> 0, h || he(a, 8, this.length), t.read(this, a, !1, 52, 8);
  };
  function fe(c, a, h, g, F, M) {
    if (!f.isBuffer(c)) throw new TypeError('"buffer" argument must be a Buffer instance');
    if (a > F || a < M) throw new RangeError('"value" argument is out of bounds');
    if (h + g > c.length) throw new RangeError("Index out of range");
  }
  f.prototype.writeUintLE = f.prototype.writeUIntLE = function(a, h, g, F) {
    if (a = +a, h = h >>> 0, g = g >>> 0, !F) {
      const Y = Math.pow(2, 8 * g) - 1;
      fe(this, a, h, g, Y, 0);
    }
    let M = 1, O = 0;
    for (this[h] = a & 255; ++O < g && (M *= 256); )
      this[h + O] = a / M & 255;
    return h + g;
  }, f.prototype.writeUintBE = f.prototype.writeUIntBE = function(a, h, g, F) {
    if (a = +a, h = h >>> 0, g = g >>> 0, !F) {
      const Y = Math.pow(2, 8 * g) - 1;
      fe(this, a, h, g, Y, 0);
    }
    let M = g - 1, O = 1;
    for (this[h + M] = a & 255; --M >= 0 && (O *= 256); )
      this[h + M] = a / O & 255;
    return h + g;
  }, f.prototype.writeUint8 = f.prototype.writeUInt8 = function(a, h, g) {
    return a = +a, h = h >>> 0, g || fe(this, a, h, 1, 255, 0), this[h] = a & 255, h + 1;
  }, f.prototype.writeUint16LE = f.prototype.writeUInt16LE = function(a, h, g) {
    return a = +a, h = h >>> 0, g || fe(this, a, h, 2, 65535, 0), this[h] = a & 255, this[h + 1] = a >>> 8, h + 2;
  }, f.prototype.writeUint16BE = f.prototype.writeUInt16BE = function(a, h, g) {
    return a = +a, h = h >>> 0, g || fe(this, a, h, 2, 65535, 0), this[h] = a >>> 8, this[h + 1] = a & 255, h + 2;
  }, f.prototype.writeUint32LE = f.prototype.writeUInt32LE = function(a, h, g) {
    return a = +a, h = h >>> 0, g || fe(this, a, h, 4, 4294967295, 0), this[h + 3] = a >>> 24, this[h + 2] = a >>> 16, this[h + 1] = a >>> 8, this[h] = a & 255, h + 4;
  }, f.prototype.writeUint32BE = f.prototype.writeUInt32BE = function(a, h, g) {
    return a = +a, h = h >>> 0, g || fe(this, a, h, 4, 4294967295, 0), this[h] = a >>> 24, this[h + 1] = a >>> 16, this[h + 2] = a >>> 8, this[h + 3] = a & 255, h + 4;
  };
  function L(c, a, h, g, F) {
    we(a, g, F, c, h, 7);
    let M = Number(a & BigInt(4294967295));
    c[h++] = M, M = M >> 8, c[h++] = M, M = M >> 8, c[h++] = M, M = M >> 8, c[h++] = M;
    let O = Number(a >> BigInt(32) & BigInt(4294967295));
    return c[h++] = O, O = O >> 8, c[h++] = O, O = O >> 8, c[h++] = O, O = O >> 8, c[h++] = O, h;
  }
  function P(c, a, h, g, F) {
    we(a, g, F, c, h, 7);
    let M = Number(a & BigInt(4294967295));
    c[h + 7] = M, M = M >> 8, c[h + 6] = M, M = M >> 8, c[h + 5] = M, M = M >> 8, c[h + 4] = M;
    let O = Number(a >> BigInt(32) & BigInt(4294967295));
    return c[h + 3] = O, O = O >> 8, c[h + 2] = O, O = O >> 8, c[h + 1] = O, O = O >> 8, c[h] = O, h + 8;
  }
  f.prototype.writeBigUInt64LE = l(function(a, h = 0) {
    return L(this, a, h, BigInt(0), BigInt("0xffffffffffffffff"));
  }), f.prototype.writeBigUInt64BE = l(function(a, h = 0) {
    return P(this, a, h, BigInt(0), BigInt("0xffffffffffffffff"));
  }), f.prototype.writeIntLE = function(a, h, g, F) {
    if (a = +a, h = h >>> 0, !F) {
      const te = Math.pow(2, 8 * g - 1);
      fe(this, a, h, g, te - 1, -te);
    }
    let M = 0, O = 1, Y = 0;
    for (this[h] = a & 255; ++M < g && (O *= 256); )
      a < 0 && Y === 0 && this[h + M - 1] !== 0 && (Y = 1), this[h + M] = (a / O >> 0) - Y & 255;
    return h + g;
  }, f.prototype.writeIntBE = function(a, h, g, F) {
    if (a = +a, h = h >>> 0, !F) {
      const te = Math.pow(2, 8 * g - 1);
      fe(this, a, h, g, te - 1, -te);
    }
    let M = g - 1, O = 1, Y = 0;
    for (this[h + M] = a & 255; --M >= 0 && (O *= 256); )
      a < 0 && Y === 0 && this[h + M + 1] !== 0 && (Y = 1), this[h + M] = (a / O >> 0) - Y & 255;
    return h + g;
  }, f.prototype.writeInt8 = function(a, h, g) {
    return a = +a, h = h >>> 0, g || fe(this, a, h, 1, 127, -128), a < 0 && (a = 255 + a + 1), this[h] = a & 255, h + 1;
  }, f.prototype.writeInt16LE = function(a, h, g) {
    return a = +a, h = h >>> 0, g || fe(this, a, h, 2, 32767, -32768), this[h] = a & 255, this[h + 1] = a >>> 8, h + 2;
  }, f.prototype.writeInt16BE = function(a, h, g) {
    return a = +a, h = h >>> 0, g || fe(this, a, h, 2, 32767, -32768), this[h] = a >>> 8, this[h + 1] = a & 255, h + 2;
  }, f.prototype.writeInt32LE = function(a, h, g) {
    return a = +a, h = h >>> 0, g || fe(this, a, h, 4, **********, -2147483648), this[h] = a & 255, this[h + 1] = a >>> 8, this[h + 2] = a >>> 16, this[h + 3] = a >>> 24, h + 4;
  }, f.prototype.writeInt32BE = function(a, h, g) {
    return a = +a, h = h >>> 0, g || fe(this, a, h, 4, **********, -2147483648), a < 0 && (a = 4294967295 + a + 1), this[h] = a >>> 24, this[h + 1] = a >>> 16, this[h + 2] = a >>> 8, this[h + 3] = a & 255, h + 4;
  }, f.prototype.writeBigInt64LE = l(function(a, h = 0) {
    return L(this, a, h, -BigInt("0x8000000000000000"), BigInt("0x7fffffffffffffff"));
  }), f.prototype.writeBigInt64BE = l(function(a, h = 0) {
    return P(this, a, h, -BigInt("0x8000000000000000"), BigInt("0x7fffffffffffffff"));
  });
  function V(c, a, h, g, F, M) {
    if (h + g > c.length) throw new RangeError("Index out of range");
    if (h < 0) throw new RangeError("Index out of range");
  }
  function z(c, a, h, g, F) {
    return a = +a, h = h >>> 0, F || V(c, a, h, 4), t.write(c, a, h, g, 23, 4), h + 4;
  }
  f.prototype.writeFloatLE = function(a, h, g) {
    return z(this, a, h, !0, g);
  }, f.prototype.writeFloatBE = function(a, h, g) {
    return z(this, a, h, !1, g);
  };
  function Q(c, a, h, g, F) {
    return a = +a, h = h >>> 0, F || V(c, a, h, 8), t.write(c, a, h, g, 52, 8), h + 8;
  }
  f.prototype.writeDoubleLE = function(a, h, g) {
    return Q(this, a, h, !0, g);
  }, f.prototype.writeDoubleBE = function(a, h, g) {
    return Q(this, a, h, !1, g);
  }, f.prototype.copy = function(a, h, g, F) {
    if (!f.isBuffer(a)) throw new TypeError("argument should be a Buffer");
    if (g || (g = 0), !F && F !== 0 && (F = this.length), h >= a.length && (h = a.length), h || (h = 0), F > 0 && F < g && (F = g), F === g || a.length === 0 || this.length === 0) return 0;
    if (h < 0)
      throw new RangeError("targetStart out of bounds");
    if (g < 0 || g >= this.length) throw new RangeError("Index out of range");
    if (F < 0) throw new RangeError("sourceEnd out of bounds");
    F > this.length && (F = this.length), a.length - h < F - g && (F = a.length - h + g);
    const M = F - g;
    return this === a && typeof n.prototype.copyWithin == "function" ? this.copyWithin(h, g, F) : n.prototype.set.call(
      a,
      this.subarray(g, F),
      h
    ), M;
  }, f.prototype.fill = function(a, h, g, F) {
    if (typeof a == "string") {
      if (typeof h == "string" ? (F = h, h = 0, g = this.length) : typeof g == "string" && (F = g, g = this.length), F !== void 0 && typeof F != "string")
        throw new TypeError("encoding must be a string");
      if (typeof F == "string" && !f.isEncoding(F))
        throw new TypeError("Unknown encoding: " + F);
      if (a.length === 1) {
        const O = a.charCodeAt(0);
        (F === "utf8" && O < 128 || F === "latin1") && (a = O);
      }
    } else typeof a == "number" ? a = a & 255 : typeof a == "boolean" && (a = Number(a));
    if (h < 0 || this.length < h || this.length < g)
      throw new RangeError("Out of range index");
    if (g <= h)
      return this;
    h = h >>> 0, g = g === void 0 ? this.length : g >>> 0, a || (a = 0);
    let M;
    if (typeof a == "number")
      for (M = h; M < g; ++M)
        this[M] = a;
    else {
      const O = f.isBuffer(a) ? a : f.from(a, F), Y = O.length;
      if (Y === 0)
        throw new TypeError('The value "' + a + '" is invalid for argument "value"');
      for (M = 0; M < g - h; ++M)
        this[M + h] = O[M % Y];
    }
    return this;
  };
  const I = {};
  function C(c, a, h) {
    I[c] = class extends h {
      constructor() {
        super(), Object.defineProperty(this, "message", {
          value: a.apply(this, arguments),
          writable: !0,
          configurable: !0
        }), this.name = `${this.name} [${c}]`, this.stack, delete this.name;
      }
      get code() {
        return c;
      }
      set code(F) {
        Object.defineProperty(this, "code", {
          configurable: !0,
          enumerable: !0,
          value: F,
          writable: !0
        });
      }
      toString() {
        return `${this.name} [${c}]: ${this.message}`;
      }
    };
  }
  C(
    "ERR_BUFFER_OUT_OF_BOUNDS",
    function(c) {
      return c ? `${c} is outside of buffer bounds` : "Attempt to access memory outside buffer bounds";
    },
    RangeError
  ), C(
    "ERR_INVALID_ARG_TYPE",
    function(c, a) {
      return `The "${c}" argument must be of type number. Received type ${typeof a}`;
    },
    TypeError
  ), C(
    "ERR_OUT_OF_RANGE",
    function(c, a, h) {
      let g = `The value of "${c}" is out of range.`, F = h;
      return Number.isInteger(h) && Math.abs(h) > 2 ** 32 ? F = K(String(h)) : typeof h == "bigint" && (F = String(h), (h > BigInt(2) ** BigInt(32) || h < -(BigInt(2) ** BigInt(32))) && (F = K(F)), F += "n"), g += ` It must be ${a}. Received ${F}`, g;
    },
    RangeError
  );
  function K(c) {
    let a = "", h = c.length;
    const g = c[0] === "-" ? 1 : 0;
    for (; h >= g + 4; h -= 3)
      a = `_${c.slice(h - 3, h)}${a}`;
    return `${c.slice(0, h)}${a}`;
  }
  function $(c, a, h) {
    ne(a, "offset"), (c[a] === void 0 || c[a + h] === void 0) && ce(a, c.length - (h + 1));
  }
  function we(c, a, h, g, F, M) {
    if (c > h || c < a) {
      const O = typeof a == "bigint" ? "n" : "";
      let Y;
      throw a === 0 || a === BigInt(0) ? Y = `>= 0${O} and < 2${O} ** ${(M + 1) * 8}${O}` : Y = `>= -(2${O} ** ${(M + 1) * 8 - 1}${O}) and < 2 ** ${(M + 1) * 8 - 1}${O}`, new I.ERR_OUT_OF_RANGE("value", Y, c);
    }
    $(g, F, M);
  }
  function ne(c, a) {
    if (typeof c != "number")
      throw new I.ERR_INVALID_ARG_TYPE(a, "number", c);
  }
  function ce(c, a, h) {
    throw Math.floor(c) !== c ? (ne(c, h), new I.ERR_OUT_OF_RANGE("offset", "an integer", c)) : a < 0 ? new I.ERR_BUFFER_OUT_OF_BOUNDS() : new I.ERR_OUT_OF_RANGE(
      "offset",
      `>= 0 and <= ${a}`,
      c
    );
  }
  const Qe = /[^+/0-9A-Za-z-_]/g;
  function mt(c) {
    if (c = c.split("=")[0], c = c.trim().replace(Qe, ""), c.length < 2) return "";
    for (; c.length % 4 !== 0; )
      c = c + "=";
    return c;
  }
  function st(c, a) {
    a = a || 1 / 0;
    let h;
    const g = c.length;
    let F = null;
    const M = [];
    for (let O = 0; O < g; ++O) {
      if (h = c.charCodeAt(O), h > 55295 && h < 57344) {
        if (!F) {
          if (h > 56319) {
            (a -= 3) > -1 && M.push(239, 191, 189);
            continue;
          } else if (O + 1 === g) {
            (a -= 3) > -1 && M.push(239, 191, 189);
            continue;
          }
          F = h;
          continue;
        }
        if (h < 56320) {
          (a -= 3) > -1 && M.push(239, 191, 189), F = h;
          continue;
        }
        h = (F - 55296 << 10 | h - 56320) + 65536;
      } else F && (a -= 3) > -1 && M.push(239, 191, 189);
      if (F = null, h < 128) {
        if ((a -= 1) < 0) break;
        M.push(h);
      } else if (h < 2048) {
        if ((a -= 2) < 0) break;
        M.push(
          h >> 6 | 192,
          h & 63 | 128
        );
      } else if (h < 65536) {
        if ((a -= 3) < 0) break;
        M.push(
          h >> 12 | 224,
          h >> 6 & 63 | 128,
          h & 63 | 128
        );
      } else if (h < 1114112) {
        if ((a -= 4) < 0) break;
        M.push(
          h >> 18 | 240,
          h >> 12 & 63 | 128,
          h >> 6 & 63 | 128,
          h & 63 | 128
        );
      } else
        throw new Error("Invalid code point");
    }
    return M;
  }
  function Ue(c) {
    const a = [];
    for (let h = 0; h < c.length; ++h)
      a.push(c.charCodeAt(h) & 255);
    return a;
  }
  function Ce(c, a) {
    let h, g, F;
    const M = [];
    for (let O = 0; O < c.length && !((a -= 2) < 0); ++O)
      h = c.charCodeAt(O), g = h >> 8, F = h % 256, M.push(F), M.push(g);
    return M;
  }
  function et(c) {
    return e.toByteArray(mt(c));
  }
  function at(c, a, h, g) {
    let F;
    for (F = 0; F < g && !(F + h >= a.length || F >= c.length); ++F)
      a[F + h] = c[F];
    return F;
  }
  function Fe(c, a) {
    return c instanceof a || c != null && c.constructor != null && c.constructor.name != null && c.constructor.name === a.name;
  }
  function dt(c) {
    return c !== c;
  }
  const _ = function() {
    const c = "0123456789abcdef", a = new Array(256);
    for (let h = 0; h < 16; ++h) {
      const g = h * 16;
      for (let F = 0; F < 16; ++F)
        a[g + F] = c[h] + c[F];
    }
    return a;
  }();
  function l(c) {
    return typeof BigInt > "u" ? d : c;
  }
  function d() {
    throw new Error("BigInt not supported");
  }
})(ea);
const qe = ea.Buffer;
class H extends Error {
  constructor(e, t) {
    super("Error " + e + (t ? ": " + t : "")), this.name = "KdbxError", this.code = e;
  }
}
const lr = {
  FileMagic: 2594363651,
  Sig2Kdbx: 3041655655,
  Sig2Kdb: 3041655653
}, j = {
  NotImplemented: "NotImplemented",
  InvalidArg: "InvalidArg",
  BadSignature: "BadSignature",
  InvalidVersion: "InvalidVersion",
  Unsupported: "Unsupported",
  FileCorrupt: "FileCorrupt",
  InvalidKey: "InvalidKey",
  MergeError: "MergeError",
  InvalidState: "InvalidState"
}, Nt = {
  None: 0,
  GZip: 1
}, Qt = {
  Null: 0,
  ArcFourVariant: 1,
  Salsa20: 2,
  ChaCha20: 3
}, At = {
  Argon2: "72Nt34wpREuR96mkA+MKDA==",
  Argon2d: "72Nt34wpREuR96mkA+MKDA==",
  Argon2id: "nimLGVbbR3OyPfw+xvCh5g==",
  Aes: "ydnzmmKKRGC/dA0IwYpP6g=="
}, Ot = {
  Aes: "McHy5r9xQ1C+WAUhavxa/w==",
  ChaCha20: "1gOKK4tvTLWlJDOaMdu1mg=="
}, dr = {
  None: 0,
  UseClipboard: 1
}, Vt = {
  KeyEncryptionRounds: 3e5,
  MntncHistoryDays: 365,
  HistoryMaxItems: 10,
  HistoryMaxSize: 6 * 1024 * 1024,
  RecycleBinName: "Recycle Bin"
}, Gt = {
  Key: 0,
  World: 1,
  Warning: 2,
  NetworkServer: 3,
  MarkedDirectory: 4,
  UserCommunication: 5,
  Parts: 6,
  Notepad: 7,
  WorldSocket: 8,
  Identity: 9,
  PaperReady: 10,
  Digicam: 11,
  IRCommunication: 12,
  MultiKeys: 13,
  Energy: 14,
  Scanner: 15,
  WorldStar: 16,
  CDRom: 17,
  Monitor: 18,
  EMail: 19,
  Configuration: 20,
  ClipboardReady: 21,
  PaperNew: 22,
  Screen: 23,
  EnergyCareful: 24,
  EMailBox: 25,
  Disk: 26,
  Drive: 27,
  PaperQ: 28,
  TerminalEncrypted: 29,
  Console: 30,
  Printer: 31,
  ProgramIcons: 32,
  Run: 33,
  Settings: 34,
  WorldComputer: 35,
  Archive: 36,
  Homebanking: 37,
  DriveWindows: 38,
  Clock: 39,
  EMailSearch: 40,
  PaperFlag: 41,
  Memory: 42,
  TrashBin: 43,
  Note: 44,
  Expired: 45,
  Info: 46,
  Package: 47,
  Folder: 48,
  FolderOpen: 49,
  FolderPackage: 50,
  LockOpen: 51,
  PaperLocked: 52,
  Checked: 53,
  Pen: 54,
  Thumbnail: 55,
  Book: 56,
  List: 57,
  UserKey: 58,
  Tool: 59,
  Home: 60,
  Star: 61,
  Tux: 62,
  Feather: 63,
  Apple: 64,
  Wiki: 65,
  Money: 66,
  Certificate: 67,
  BlackBerry: 68
}, gu = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  AutoTypeObfuscationOptions: dr,
  CipherId: Ot,
  CompressionAlgorithm: Nt,
  CrsAlgorithm: Qt,
  Defaults: Vt,
  ErrorCodes: j,
  Icons: Gt,
  KdfId: At,
  Signatures: lr
}, Symbol.toStringTag, { value: "Module" })), ra = "utf-8";
function de(i, e = void 0) {
  if (i)
    throw TypeError("Decoder error");
  return e || 65533;
}
function wt(i) {
  throw TypeError("The code point " + i + " could not be encoded.");
}
function tn(i) {
  const e = String(i).trim().toLowerCase();
  return e in rn ? rn[e] : null;
}
const ia = [
  {
    encodings: [
      {
        labels: ["unicode-1-1-utf-8", "utf-8", "utf8"],
        name: "UTF-8"
      }
    ],
    heading: "The Encoding"
  },
  {
    encodings: [
      {
        labels: ["866", "cp866", "csibm866", "ibm866"],
        name: "IBM866"
      },
      {
        labels: [
          "csisolatin2",
          "iso-8859-2",
          "iso-ir-101",
          "iso8859-2",
          "iso88592",
          "iso_8859-2",
          "iso_8859-2:1987",
          "l2",
          "latin2"
        ],
        name: "ISO-8859-2"
      },
      {
        labels: [
          "csisolatin3",
          "iso-8859-3",
          "iso-ir-109",
          "iso8859-3",
          "iso88593",
          "iso_8859-3",
          "iso_8859-3:1988",
          "l3",
          "latin3"
        ],
        name: "ISO-8859-3"
      },
      {
        labels: [
          "csisolatin4",
          "iso-8859-4",
          "iso-ir-110",
          "iso8859-4",
          "iso88594",
          "iso_8859-4",
          "iso_8859-4:1988",
          "l4",
          "latin4"
        ],
        name: "ISO-8859-4"
      },
      {
        labels: [
          "csisolatincyrillic",
          "cyrillic",
          "iso-8859-5",
          "iso-ir-144",
          "iso8859-5",
          "iso88595",
          "iso_8859-5",
          "iso_8859-5:1988"
        ],
        name: "ISO-8859-5"
      },
      {
        labels: [
          "arabic",
          "asmo-708",
          "csiso88596e",
          "csiso88596i",
          "csisolatinarabic",
          "ecma-114",
          "iso-8859-6",
          "iso-8859-6-e",
          "iso-8859-6-i",
          "iso-ir-127",
          "iso8859-6",
          "iso88596",
          "iso_8859-6",
          "iso_8859-6:1987"
        ],
        name: "ISO-8859-6"
      },
      {
        labels: [
          "csisolatingreek",
          "ecma-118",
          "elot_928",
          "greek",
          "greek8",
          "iso-8859-7",
          "iso-ir-126",
          "iso8859-7",
          "iso88597",
          "iso_8859-7",
          "iso_8859-7:1987",
          "sun_eu_greek"
        ],
        name: "ISO-8859-7"
      },
      {
        labels: [
          "csiso88598e",
          "csisolatinhebrew",
          "hebrew",
          "iso-8859-8",
          "iso-8859-8-e",
          "iso-ir-138",
          "iso8859-8",
          "iso88598",
          "iso_8859-8",
          "iso_8859-8:1988",
          "visual"
        ],
        name: "ISO-8859-8"
      },
      {
        labels: ["csiso88598i", "iso-8859-8-i", "logical"],
        name: "ISO-8859-8-I"
      },
      {
        labels: [
          "csisolatin6",
          "iso-8859-10",
          "iso-ir-157",
          "iso8859-10",
          "iso885910",
          "l6",
          "latin6"
        ],
        name: "ISO-8859-10"
      },
      {
        labels: ["iso-8859-13", "iso8859-13", "iso885913"],
        name: "ISO-8859-13"
      },
      {
        labels: ["iso-8859-14", "iso8859-14", "iso885914"],
        name: "ISO-8859-14"
      },
      {
        labels: [
          "csisolatin9",
          "iso-8859-15",
          "iso8859-15",
          "iso885915",
          "iso_8859-15",
          "l9"
        ],
        name: "ISO-8859-15"
      },
      {
        labels: ["iso-8859-16"],
        name: "ISO-8859-16"
      },
      {
        labels: ["cskoi8r", "koi", "koi8", "koi8-r", "koi8_r"],
        name: "KOI8-R"
      },
      {
        labels: ["koi8-ru", "koi8-u"],
        name: "KOI8-U"
      },
      {
        labels: ["csmacintosh", "mac", "macintosh", "x-mac-roman"],
        name: "macintosh"
      },
      {
        labels: [
          "dos-874",
          "iso-8859-11",
          "iso8859-11",
          "iso885911",
          "tis-620",
          "windows-874"
        ],
        name: "windows-874"
      },
      {
        labels: ["cp1250", "windows-1250", "x-cp1250"],
        name: "windows-1250"
      },
      {
        labels: ["cp1251", "windows-1251", "x-cp1251"],
        name: "windows-1251"
      },
      {
        labels: [
          "ansi_x3.4-1968",
          "cp1252",
          "cp819",
          "ibm819",
          "iso-ir-100",
          "windows-1252",
          "x-cp1252"
        ],
        name: "windows-1252"
      },
      {
        labels: [
          "ascii",
          "us-ascii",
          "iso-8859-1",
          "iso8859-1",
          "iso88591",
          "iso_8859-1",
          "iso_8859-1:1987",
          "l1",
          "latin1",
          "csisolatin1"
        ],
        name: "iso-8859-1"
      },
      {
        labels: ["cp1253", "windows-1253", "x-cp1253"],
        name: "windows-1253"
      },
      {
        labels: [
          "cp1254",
          "csisolatin5",
          "iso-8859-9",
          "iso-ir-148",
          "iso8859-9",
          "iso88599",
          "iso_8859-9",
          "iso_8859-9:1989",
          "l5",
          "latin5",
          "windows-1254",
          "x-cp1254"
        ],
        name: "windows-1254"
      },
      {
        labels: ["cp1255", "windows-1255", "x-cp1255"],
        name: "windows-1255"
      },
      {
        labels: ["cp1256", "windows-1256", "x-cp1256"],
        name: "windows-1256"
      },
      {
        labels: ["cp1257", "windows-1257", "x-cp1257"],
        name: "windows-1257"
      },
      {
        labels: ["cp1258", "windows-1258", "x-cp1258"],
        name: "windows-1258"
      },
      {
        labels: ["x-mac-cyrillic", "x-mac-ukrainian"],
        name: "x-mac-cyrillic"
      }
    ],
    heading: "Legacy single-byte encodings"
  },
  {
    encodings: [
      {
        labels: [
          "chinese",
          "csgb2312",
          "csiso58gb231280",
          "gb2312",
          "gb_2312",
          "gb_2312-80",
          "gbk",
          "iso-ir-58",
          "x-gbk"
        ],
        name: "GBK"
      },
      {
        labels: ["gb18030"],
        name: "gb18030"
      }
    ],
    heading: "Legacy multi-byte Chinese (simplified) encodings"
  },
  {
    encodings: [
      {
        labels: ["big5", "big5-hkscs", "cn-big5", "csbig5", "x-x-big5"],
        name: "Big5"
      }
    ],
    heading: "Legacy multi-byte Chinese (traditional) encodings"
  },
  {
    encodings: [
      {
        labels: ["cseucpkdfmtjapanese", "euc-jp", "x-euc-jp"],
        name: "EUC-JP"
      },
      {
        labels: ["csiso2022jp", "iso-2022-jp"],
        name: "ISO-2022-JP"
      },
      {
        labels: [
          "csshiftjis",
          "ms932",
          "ms_kanji",
          "shift-jis",
          "shift_jis",
          "sjis",
          "windows-31j",
          "x-sjis"
        ],
        name: "Shift_JIS"
      }
    ],
    heading: "Legacy multi-byte Japanese encodings"
  },
  {
    encodings: [
      {
        labels: [
          "cseuckr",
          "csksc56011987",
          "euc-kr",
          "iso-ir-149",
          "korean",
          "ks_c_5601-1987",
          "ks_c_5601-1989",
          "ksc5601",
          "ksc_5601",
          "windows-949"
        ],
        name: "EUC-KR"
      }
    ],
    heading: "Legacy multi-byte Korean encodings"
  },
  {
    encodings: [
      {
        labels: [
          "csiso2022kr",
          "hz-gb-2312",
          "iso-2022-cn",
          "iso-2022-cn-ext",
          "iso-2022-kr"
        ],
        name: "replacement"
      },
      {
        labels: ["utf-16be"],
        name: "UTF-16BE"
      },
      {
        labels: ["utf-16", "utf-16le"],
        name: "UTF-16LE"
      },
      {
        labels: ["x-user-defined"],
        name: "x-user-defined"
      }
    ],
    heading: "Legacy miscellaneous encodings"
  }
], rn = {};
ia.forEach((i) => {
  i.encodings.forEach((e) => {
    e.labels.forEach((t) => {
      rn[t] = e;
    });
  });
});
const ve = -1;
function na(i) {
  return Array.isArray(i) ? i : [i];
}
function oe(i, e, t) {
  return e <= i && i <= t;
}
function po(i, e) {
  return i.indexOf(e) !== -1;
}
function jr(i) {
  if (i == null)
    return {};
  if (i === Object(i))
    return i;
  throw TypeError("Could not convert argument to dictionary");
}
function yo(i) {
  const e = String(i), t = e.length;
  let r = 0;
  const s = [];
  for (; r < t; ) {
    const n = e.charCodeAt(r);
    if (n < 55296 || n > 57343)
      s.push(n);
    else if (56320 <= n && n <= 57343)
      s.push(65533);
    else if (55296 <= n && n <= 56319)
      if (r === t - 1)
        s.push(65533);
      else {
        const u = e.charCodeAt(r + 1);
        if (56320 <= u && u <= 57343) {
          const o = n & 1023, p = u & 1023;
          s.push(65536 + (o << 10) + p), r += 1;
        } else
          s.push(65533);
      }
    r += 1;
  }
  return s;
}
function go(i) {
  let e = "";
  for (let t = 0; t < i.length; ++t) {
    let r = i[t];
    r <= 65535 ? e += String.fromCharCode(r) : (r -= 65536, e += String.fromCharCode((r >> 10) + 55296, (r & 1023) + 56320));
  }
  return e;
}
const Rt = globalThis || void 0 || self;
function sa() {
  if (typeof Rt < "u")
    return Rt;
  if (typeof window < "u")
    return window;
  if (typeof self < "u")
    return self;
}
let ei;
function wo() {
  if (typeof TextEncodingIndexes < "u")
    return TextEncodingIndexes.encodingIndexes;
  const i = sa();
  return i ? "TextEncodingIndexes" in i ? Rt.TextEncodingIndexes.encodingIndexes : "encoding-indexes" in i ? Rt.encodingIndexes : null : null;
}
function aa() {
  if (ei)
    return ei;
  const i = wo();
  return i ? (ei = i, i) : null;
}
function sr(i, e) {
  return e && e[i] || null;
}
function ar(i, e) {
  const t = e.indexOf(i);
  return t === -1 ? null : t;
}
function Xe(i) {
  const e = aa();
  if (!e)
    throw Error("Indexes missing. Did you forget to include encoding-indexes.js first?");
  return e[i];
}
function mo(i) {
  if (i > 39419 && i < 189e3 || i > 1237575)
    return null;
  if (i === 7457)
    return 59335;
  let e = 0, t = 0;
  const r = Xe("gb18030-ranges");
  for (let s = 0; s < r.length; ++s) {
    const n = na(r[s]);
    if (n[0] <= i)
      e = n[0], t = n[1];
    else
      break;
  }
  return t + i - e;
}
function _o(i) {
  if (i === 59335)
    return 7457;
  let e = 0, t = 0;
  const r = Xe("gb18030-ranges");
  for (let s = 0; s < r.length; ++s) {
    const n = r[s], u = na(n);
    if (u[1] <= i)
      e = u[1], t = u[0];
    else
      break;
  }
  return t + i - e;
}
function bo(i) {
  return ti = ti || Xe("jis0208").map(function(t, r) {
    return oe(r, 8272, 8835) ? null : t;
  }), ti.indexOf(i);
}
let ti;
function xo(i) {
  ri = ri || Xe("big5").map((t, r) => r < 32 * 157 ? null : t);
  const e = ri;
  return i === 9552 || i === 9566 || i === 9569 || i === 9578 || i === 21313 || i === 21317 ? e.lastIndexOf(i) : ar(i, e);
}
let ri;
function ct(i) {
  return 0 <= i && i <= 127;
}
const ft = ct, pe = -1;
class vo {
  constructor(e) {
    this.fatal = e.fatal, this.Big5_lead = 0;
  }
  /**
   * @param {Stream} stream The stream of bytes being decoded.
   * @param {number} bite The next byte read from the stream.
   * @return {?(number|!Array.<number>)} The next code point(s)
   *     decoded, or null if not enough data exists in the input
   *     stream to decode a complete code point.
   */
  handler(e, t) {
    if (t === pe && this.Big5_lead !== 0)
      return this.Big5_lead = 0, de(this.fatal);
    if (t === pe && this.Big5_lead === 0)
      return ve;
    if (this.Big5_lead !== 0) {
      const r = this.Big5_lead;
      let s = null;
      this.Big5_lead = 0;
      const n = t < 127 ? 64 : 98;
      switch ((oe(t, 64, 126) || oe(t, 161, 254)) && (s = (r - 129) * 157 + (t - n)), s) {
        case 1133:
          return [202, 772];
        case 1135:
          return [202, 780];
        case 1164:
          return [234, 772];
        case 1166:
          return [234, 780];
      }
      const u = s === null ? null : sr(s, Xe("big5"));
      return u === null && ct(t) && e.prepend(t), u === null ? de(this.fatal) : u;
    }
    return ct(t) ? t : oe(t, 129, 254) ? (this.Big5_lead = t, null) : de(this.fatal);
  }
}
class Eo {
  constructor(e) {
    this.fatal = e.fatal;
  }
  /**
   * @param {Stream} stream Input stream.
   * @param {number} code_point Next code point read from the stream.
   * @return {(number|!Array.<number>)} Byte(s) to emit.
   */
  handler(e, t) {
    if (t === pe)
      return ve;
    if (ft(t))
      return t;
    const r = xo(t);
    if (r === null)
      return wt(t);
    const s = Math.floor(r / 157) + 129;
    if (s < 161)
      return wt(t);
    const n = r % 157, u = n < 63 ? 64 : 98;
    return [s, n + u];
  }
}
class Bo {
  constructor(e) {
    this.fatal = e.fatal, this.eucjp_jis0212_flag = !1, /** @type {number} */
    this.eucjp_lead = 0;
  }
  /**
   * @param {Stream} stream The stream of bytes being decoded.
   * @param {number} bite The next byte read from the stream.
   * @return {?(number|!Array.<number>)} The next code point(s)
   *     decoded, or null if not enough data exists in the input
   *     stream to decode a complete code point.
   */
  handler(e, t) {
    if (t === pe && this.eucjp_lead !== 0)
      return this.eucjp_lead = 0, de(this.fatal);
    if (t === pe && this.eucjp_lead === 0)
      return ve;
    if (this.eucjp_lead === 142 && oe(t, 161, 223))
      return this.eucjp_lead = 0, 65216 + t;
    if (this.eucjp_lead === 143 && oe(t, 161, 254))
      return this.eucjp_jis0212_flag = !0, this.eucjp_lead = t, null;
    if (this.eucjp_lead !== 0) {
      const r = this.eucjp_lead;
      this.eucjp_lead = 0;
      let s = null;
      return oe(r, 161, 254) && oe(t, 161, 254) && (s = sr((r - 161) * 94 + (t - 161), Xe(this.eucjp_jis0212_flag ? "jis0212" : "jis0208"))), this.eucjp_jis0212_flag = !1, oe(t, 161, 254) || e.prepend(t), s === null ? de(this.fatal) : s;
    }
    return ct(t) ? t : t === 142 || t === 143 || oe(t, 161, 254) ? (this.eucjp_lead = t, null) : de(this.fatal);
  }
}
class So {
  constructor(e) {
    this.fatal = e.fatal;
  }
  /**
   * @param {Stream} stream Input stream.
   * @param {number} code_point Next code point read from the stream.
   * @return {(number|!Array.<number>)} Byte(s) to emit.
   */
  handler(e, t) {
    if (t === pe)
      return ve;
    if (ft(t))
      return t;
    if (t === 165)
      return 92;
    if (t === 8254)
      return 126;
    if (oe(t, 65377, 65439))
      return [142, t - 65377 + 161];
    t === 8722 && (t = 65293);
    const r = ar(t, Xe("jis0208"));
    if (r === null)
      return wt(t);
    const s = Math.floor(r / 94) + 161, n = r % 94 + 161;
    return [s, n];
  }
}
class Io {
  constructor(e) {
    this.fatal = e.fatal, this.euckr_lead = 0;
  }
  /**
   * @param {Stream} stream The stream of bytes being decoded.
   * @param {number} bite The next byte read from the stream.
   * @return {?(number|!Array.<number>)} The next code point(s)
   *     decoded, or null if not enough data exists in the input
   *     stream to decode a complete code point.
   */
  handler(e, t) {
    if (t === pe && this.euckr_lead !== 0)
      return this.euckr_lead = 0, de(this.fatal);
    if (t === pe && this.euckr_lead === 0)
      return ve;
    if (this.euckr_lead !== 0) {
      const r = this.euckr_lead;
      let s = null;
      this.euckr_lead = 0, oe(t, 65, 254) && (s = (r - 129) * 190 + (t - 65));
      const n = s === null ? null : sr(s, Xe("euc-kr"));
      return s === null && ct(t) && e.prepend(t), n === null ? de(this.fatal) : n;
    }
    return ct(t) ? t : oe(t, 129, 254) ? (this.euckr_lead = t, null) : de(this.fatal);
  }
}
class Co {
  constructor(e) {
    this.fatal = e.fatal;
  }
  /**
   * @param {Stream} stream Input stream.
   * @param {number} code_point Next code point read from the stream.
   * @return {(number|!Array.<number>)} Byte(s) to emit.
   */
  handler(e, t) {
    if (t === pe)
      return ve;
    if (ft(t))
      return t;
    const r = ar(t, Xe("euc-kr"));
    if (r === null)
      return wt(t);
    const s = Math.floor(r / 190) + 129, n = r % 190 + 65;
    return [s, n];
  }
}
class Dn {
  constructor(e) {
    this.fatal = e.fatal, this.gb18030_first = 0, /** @type {number} */
    this.gb18030_second = 0, /** @type {number} */
    this.gb18030_third = 0;
  }
  /**
   * @param {Stream} stream The stream of bytes being decoded.
   * @param {number} bite The next byte read from the stream.
   * @return {?(number|!Array.<number>)} The next code point(s)
   *     decoded, or null if not enough data exists in the input
   *     stream to decode a complete code point.
   */
  handler(e, t) {
    if (t === pe && this.gb18030_first === 0 && this.gb18030_second === 0 && this.gb18030_third === 0)
      return ve;
    t === pe && (this.gb18030_first !== 0 || this.gb18030_second !== 0 || this.gb18030_third !== 0) && (this.gb18030_first = 0, this.gb18030_second = 0, this.gb18030_third = 0, de(this.fatal));
    let r;
    if (this.gb18030_third !== 0) {
      r = null, oe(t, 48, 57) && (r = mo((((this.gb18030_first - 129) * 10 + this.gb18030_second - 48) * 126 + this.gb18030_third - 129) * 10 + t - 48));
      const s = [this.gb18030_second, this.gb18030_third, t];
      return this.gb18030_first = 0, this.gb18030_second = 0, this.gb18030_third = 0, r === null ? (e.prepend(s), de(this.fatal)) : r;
    }
    if (this.gb18030_second !== 0)
      return oe(t, 129, 254) ? (this.gb18030_third = t, null) : (e.prepend([this.gb18030_second, t]), this.gb18030_first = 0, this.gb18030_second = 0, de(this.fatal));
    if (this.gb18030_first !== 0) {
      if (oe(t, 48, 57))
        return this.gb18030_second = t, null;
      const s = this.gb18030_first;
      let n = null;
      this.gb18030_first = 0;
      const u = t < 127 ? 64 : 65;
      return (oe(t, 64, 126) || oe(t, 128, 254)) && (n = (s - 129) * 190 + (t - u)), r = n === null ? null : sr(n, Xe("gb18030")), r === null && ct(t) && e.prepend(t), r === null ? de(this.fatal) : r;
    }
    return ct(t) ? t : t === 128 ? 8364 : oe(t, 129, 254) ? (this.gb18030_first = t, null) : de(this.fatal);
  }
}
class Mn {
  constructor(e, t = void 0) {
    this.gbk_flag = t, this.fatal = e.fatal;
  }
  /**
   * @param {Stream} stream Input stream.
   * @param {number} code_point Next code point read from the stream.
   * @return {(number|!Array.<number>)} Byte(s) to emit.
   */
  handler(e, t) {
    if (t === pe)
      return ve;
    if (ft(t))
      return t;
    if (t === 58853)
      return wt(t);
    if (this.gbk_flag && t === 8364)
      return 128;
    let r = ar(t, Xe("gb18030"));
    if (r !== null) {
      const p = Math.floor(r / 190) + 129, y = r % 190, f = y < 63 ? 64 : 65;
      return [p, y + f];
    }
    if (this.gbk_flag)
      return wt(t);
    r = _o(t);
    const s = Math.floor(r / 10 / 126 / 10);
    r = r - s * 10 * 126 * 10;
    const n = Math.floor(r / 10 / 126);
    r = r - n * 10 * 126;
    const u = Math.floor(r / 10), o = r - u * 10;
    return [
      s + 129,
      n + 48,
      u + 129,
      o + 48
    ];
  }
}
var Be;
(function(i) {
  i[i.ASCII = 0] = "ASCII", i[i.Roman = 1] = "Roman", i[i.Katakana = 2] = "Katakana", i[i.LeadByte = 3] = "LeadByte", i[i.TrailByte = 4] = "TrailByte", i[i.EscapeStart = 5] = "EscapeStart", i[i.Escape = 6] = "Escape";
})(Be || (Be = {}));
class Ao {
  /**
   * @constructor
   * @implements {Decoder}
   * @param {{fatal: boolean}} options
   */
  constructor(e) {
    this.fatal = e.fatal, this.iso2022jp_decoder_state = Be.ASCII, /** @type {number} */
    this.iso2022jp_decoder_output_state = Be.ASCII, /** @type {number} */
    this.iso2022jp_lead = 0, /** @type {boolean} */
    this.iso2022jp_output_flag = !1;
  }
  /**
   * @param {Stream} stream The stream of bytes being decoded.
   * @param {number} bite The next byte read from the stream.
   * @return {?(number|!Array.<number>)} The next code point(s)
   *     decoded, or null if not enough data exists in the input
   *     stream to decode a complete code point.
   */
  handler(e, t) {
    switch (this.iso2022jp_decoder_state) {
      default:
      case Be.ASCII:
        return t === 27 ? (this.iso2022jp_decoder_state = Be.EscapeStart, null) : oe(t, 0, 127) && t !== 14 && t !== 15 && t !== 27 ? (this.iso2022jp_output_flag = !1, t) : t === pe ? ve : (this.iso2022jp_output_flag = !1, de(this.fatal));
      case Be.Roman:
        return t === 27 ? (this.iso2022jp_decoder_state = Be.EscapeStart, null) : t === 92 ? (this.iso2022jp_output_flag = !1, 165) : t === 126 ? (this.iso2022jp_output_flag = !1, 8254) : oe(t, 0, 127) && t !== 14 && t !== 15 && t !== 27 && t !== 92 && t !== 126 ? (this.iso2022jp_output_flag = !1, t) : t === pe ? ve : (this.iso2022jp_output_flag = !1, de(this.fatal));
      case Be.Katakana:
        return t === 27 ? (this.iso2022jp_decoder_state = Be.EscapeStart, null) : oe(t, 33, 95) ? (this.iso2022jp_output_flag = !1, 65344 + t) : t === pe ? ve : (this.iso2022jp_output_flag = !1, de(this.fatal));
      case Be.LeadByte:
        return t === 27 ? (this.iso2022jp_decoder_state = Be.EscapeStart, null) : oe(t, 33, 126) ? (this.iso2022jp_output_flag = !1, this.iso2022jp_lead = t, this.iso2022jp_decoder_state = Be.TrailByte, null) : t === pe ? ve : (this.iso2022jp_output_flag = !1, de(this.fatal));
      case Be.TrailByte:
        if (t === 27)
          return this.iso2022jp_decoder_state = Be.EscapeStart, de(this.fatal);
        if (oe(t, 33, 126)) {
          this.iso2022jp_decoder_state = Be.LeadByte;
          const n = (this.iso2022jp_lead - 33) * 94 + t - 33, u = sr(n, Xe("jis0208"));
          return u === null ? de(this.fatal) : u;
        }
        return t === pe ? (this.iso2022jp_decoder_state = Be.LeadByte, e.prepend(t), de(this.fatal)) : (this.iso2022jp_decoder_state = Be.LeadByte, de(this.fatal));
      case Be.EscapeStart:
        return t === 36 || t === 40 ? (this.iso2022jp_lead = t, this.iso2022jp_decoder_state = Be.Escape, null) : (e.prepend(t), this.iso2022jp_output_flag = !1, this.iso2022jp_decoder_state = this.iso2022jp_decoder_output_state, de(this.fatal));
      case Be.Escape:
        const r = this.iso2022jp_lead;
        this.iso2022jp_lead = 0;
        let s = null;
        if (r === 40 && t === 66 && (s = Be.ASCII), r === 40 && t === 74 && (s = Be.Roman), r === 40 && t === 73 && (s = Be.Katakana), r === 36 && (t === 64 || t === 66) && (s = Be.LeadByte), s !== null) {
          this.iso2022jp_decoder_state = this.iso2022jp_decoder_state = s;
          const n = this.iso2022jp_output_flag;
          return this.iso2022jp_output_flag = !0, n ? de(this.fatal) : null;
        }
        return e.prepend([r, t]), this.iso2022jp_output_flag = !1, this.iso2022jp_decoder_state = this.iso2022jp_decoder_output_state, de(this.fatal);
    }
  }
}
var je;
(function(i) {
  i[i.ASCII = 0] = "ASCII", i[i.Roman = 1] = "Roman", i[i.jis0208 = 2] = "jis0208";
})(je || (je = {}));
class ko {
  constructor(e) {
    this.fatal = e.fatal, this.iso2022jp_state = je.ASCII;
  }
  /**
   * @param {Stream} stream Input stream.
   * @param {number} code_point Next code point read from the stream.
   * @return {(number|!Array.<number>)} Byte(s) to emit.
   */
  handler(e, t) {
    if (t === pe && this.iso2022jp_state !== je.ASCII)
      return e.prepend(t), this.iso2022jp_state = je.ASCII, [27, 40, 66];
    if (t === pe && this.iso2022jp_state === je.ASCII)
      return ve;
    if ((this.iso2022jp_state === je.ASCII || this.iso2022jp_state === je.Roman) && (t === 14 || t === 15 || t === 27))
      return wt(65533);
    if (this.iso2022jp_state === je.ASCII && ft(t))
      return t;
    if (this.iso2022jp_state === je.Roman && (ft(t) && t !== 92 && t !== 126 || t == 165 || t == 8254)) {
      if (ft(t))
        return t;
      if (t === 165)
        return 92;
      if (t === 8254)
        return 126;
    }
    if (ft(t) && this.iso2022jp_state !== je.ASCII)
      return e.prepend(t), this.iso2022jp_state = je.ASCII, [27, 40, 66];
    if ((t === 165 || t === 8254) && this.iso2022jp_state !== je.Roman)
      return e.prepend(t), this.iso2022jp_state = je.Roman, [27, 40, 74];
    t === 8722 && (t = 65293);
    const r = ar(t, Xe("jis0208"));
    if (r === null)
      return wt(t);
    if (this.iso2022jp_state !== je.jis0208)
      return e.prepend(t), this.iso2022jp_state = je.jis0208, [27, 36, 66];
    const s = Math.floor(r / 94) + 33, n = r % 94 + 33;
    return [s, n];
  }
}
class Fo {
  constructor(e) {
    this.fatal = e.fatal, this.Shift_JIS_lead = 0;
  }
  /**
   * @param {Stream} stream The stream of bytes being decoded.
   * @param {number} bite The next byte read from the stream.
   * @return {?(number|!Array.<number>)} The next code point(s)
   *     decoded, or null if not enough data exists in the input
   *     stream to decode a complete code point.
   */
  handler(e, t) {
    if (t === pe && this.Shift_JIS_lead !== 0)
      return this.Shift_JIS_lead = 0, de(this.fatal);
    if (t === pe && this.Shift_JIS_lead === 0)
      return ve;
    if (this.Shift_JIS_lead !== 0) {
      const r = this.Shift_JIS_lead;
      let s = null;
      this.Shift_JIS_lead = 0;
      const n = t < 127 ? 64 : 65, u = r < 160 ? 129 : 193;
      if ((oe(t, 64, 126) || oe(t, 128, 252)) && (s = (r - u) * 188 + t - n), oe(s, 8836, 10715))
        return 48508 + s;
      const o = s === null ? null : sr(s, Xe("jis0208"));
      return o === null && ct(t) && e.prepend(t), o === null ? de(this.fatal) : o;
    }
    return ct(t) || t === 128 ? t : oe(t, 161, 223) ? 65216 + t : oe(t, 129, 159) || oe(t, 224, 252) ? (this.Shift_JIS_lead = t, null) : de(this.fatal);
  }
}
class To {
  constructor(e) {
    this.fatal = e.fatal;
  }
  /**
   * @param {Stream} stream Input stream.
   * @param {number} code_point Next code point read from the stream.
   * @return {(number|!Array.<number>)} Byte(s) to emit.
   */
  handler(e, t) {
    if (t === pe)
      return ve;
    if (ft(t) || t === 128)
      return t;
    if (t === 165)
      return 92;
    if (t === 8254)
      return 126;
    if (oe(t, 65377, 65439))
      return t - 65377 + 161;
    t === 8722 && (t = 65293);
    const r = bo(t);
    if (r === null)
      return wt(t);
    const s = Math.floor(r / 188), n = s < 31 ? 129 : 193, u = r % 188, o = u < 63 ? 64 : 65;
    return [s + n, u + o];
  }
}
class Uo {
  constructor(e, t) {
    this.index = e, this.fatal = t.fatal;
  }
  /**
   * @param {Stream} stream The stream of bytes being decoded.
   * @param {number} bite The next byte read from the stream.
   * @return {?(number|!Array.<number>)} The next code point(s)
   *     decoded, or null if not enough data exists in the input
   *     stream to decode a complete code point.
   */
  handler(e, t) {
    if (t === pe)
      return ve;
    if (ct(t))
      return t;
    const r = this.index[t - 128];
    return r || de(this.fatal);
  }
}
class Ro {
  constructor(e, t) {
    this.index = e, this.fatal = t.fatal;
  }
  /**
   * @param {Stream} stream Input stream.
   * @param {number} code_point Next code point read from the stream.
   * @return {(number|!Array.<number>)} Byte(s) to emit.
   */
  handler(e, t) {
    if (t === pe)
      return ve;
    if (ft(t))
      return t;
    const r = ar(t, this.index);
    return r === null && wt(t), r + 128;
  }
}
function Mr(i, e) {
  const t = i >> 8, r = i & 255;
  return e ? [t, r] : [r, t];
}
class Pn {
  constructor(e, t) {
    this.utf16_be = e, this.fatal = t.fatal, this.utf16_lead_byte = null, this.utf16_lead_surrogate = null;
  }
  /**
   * @param {Stream} stream The stream of bytes being decoded.
   * @param {number} bite The next byte read from the stream.
   * @return {?(number|!Array.<number>)} The next code point(s)
   *     decoded, or null if not enough data exists in the input
   *     stream to decode a complete code point.
   */
  handler(e, t) {
    if (t === pe && (this.utf16_lead_byte !== null || this.utf16_lead_surrogate !== null))
      return de(this.fatal);
    if (t === pe && this.utf16_lead_byte === null && this.utf16_lead_surrogate === null)
      return ve;
    if (this.utf16_lead_byte === null)
      return this.utf16_lead_byte = t, null;
    let r;
    if (this.utf16_be ? r = (this.utf16_lead_byte << 8) + t : r = (t << 8) + this.utf16_lead_byte, this.utf16_lead_byte = null, this.utf16_lead_surrogate !== null) {
      const s = this.utf16_lead_surrogate;
      return this.utf16_lead_surrogate = null, oe(r, 56320, 57343) ? 65536 + (s - 55296) * 1024 + (r - 56320) : (e.prepend(Mr(r, this.utf16_be)), de(this.fatal));
    }
    return oe(r, 55296, 56319) ? (this.utf16_lead_surrogate = r, null) : oe(r, 56320, 57343) ? de(this.fatal) : r;
  }
}
class Ln {
  constructor(e, t) {
    this.utf16_be = e, this.fatal = t.fatal;
  }
  /**
   * @param {Stream} stream Input stream.
   * @param {number} code_point Next code point read from the stream.
   * @return {(number|!Array.<number>)} Byte(s) to emit.
   */
  handler(e, t) {
    if (t === pe)
      return ve;
    if (oe(t, 0, 65535))
      return Mr(t, this.utf16_be);
    const r = Mr((t - 65536 >> 10) + 55296, this.utf16_be), s = Mr((t - 65536 & 1023) + 56320, this.utf16_be);
    return r.concat(s);
  }
}
class Do {
  constructor(e) {
    this.fatal = e.fatal, this.utf8_code_point = 0, /** @type {number} */
    this.utf8_bytes_seen = 0, /** @type {number} */
    this.utf8_bytes_needed = 0, /** @type {number} */
    this.utf8_lower_boundary = 128, /** @type {number} */
    this.utf8_upper_boundary = 191;
  }
  /**
   * @param {Stream} stream The stream of bytes being decoded.
   * @param {number} bite The next byte read from the stream.
   * @return {?(number|!Array.<number>)} The next code point(s)
   *     decoded, or null if not enough data exists in the input
   *     stream to decode a complete code point.
   */
  handler(e, t) {
    if (t === pe && this.utf8_bytes_needed !== 0)
      return this.utf8_bytes_needed = 0, de(this.fatal);
    if (t === pe)
      return ve;
    if (this.utf8_bytes_needed === 0) {
      if (oe(t, 0, 127))
        return t;
      if (oe(t, 194, 223))
        this.utf8_bytes_needed = 1, this.utf8_code_point = t & 31;
      else if (oe(t, 224, 239))
        t === 224 && (this.utf8_lower_boundary = 160), t === 237 && (this.utf8_upper_boundary = 159), this.utf8_bytes_needed = 2, this.utf8_code_point = t & 15;
      else if (oe(t, 240, 244))
        t === 240 && (this.utf8_lower_boundary = 144), t === 244 && (this.utf8_upper_boundary = 143), this.utf8_bytes_needed = 3, this.utf8_code_point = t & 7;
      else
        return de(this.fatal);
      return null;
    }
    if (!oe(t, this.utf8_lower_boundary, this.utf8_upper_boundary))
      return this.utf8_code_point = this.utf8_bytes_needed = this.utf8_bytes_seen = 0, this.utf8_lower_boundary = 128, this.utf8_upper_boundary = 191, e.prepend(t), de(this.fatal);
    if (this.utf8_lower_boundary = 128, this.utf8_upper_boundary = 191, this.utf8_code_point = this.utf8_code_point << 6 | t & 63, this.utf8_bytes_seen += 1, this.utf8_bytes_seen !== this.utf8_bytes_needed)
      return null;
    const r = this.utf8_code_point;
    return this.utf8_code_point = this.utf8_bytes_needed = this.utf8_bytes_seen = 0, r;
  }
}
class Mo {
  constructor(e) {
    this.fatal = e.fatal;
  }
  /**
   * @param {Stream} stream Input stream.
   * @param {number} code_point Next code point read from the stream.
   * @return {(number|!Array.<number>)} Byte(s) to emit.
   */
  handler(e, t) {
    if (t === pe)
      return ve;
    if (ft(t))
      return t;
    let r, s;
    oe(t, 128, 2047) ? (r = 1, s = 192) : oe(t, 2048, 65535) ? (r = 2, s = 224) : oe(t, 65536, 1114111) && (r = 3, s = 240);
    const n = [(t >> 6 * r) + s];
    for (; r > 0; ) {
      const u = t >> 6 * (r - 1);
      n.push(128 | u & 63), r -= 1;
    }
    return n;
  }
}
class Po {
  constructor(e) {
    this.fatal = e.fatal;
  }
  /**
   * @param {Stream} stream The stream of bytes being decoded.
   * @param {number} bite The next byte read from the stream.
   * @return {?(number|!Array.<number>)} The next code point(s)
   *     decoded, or null if not enough data exists in the input
   *     stream to decode a complete code point.
   */
  handler(e, t) {
    return t === pe ? ve : ct(t) ? t : 63360 + t - 128;
  }
}
class Lo {
  constructor(e) {
    this.fatal = e.fatal;
  }
  /**
   * @param {Stream} stream Input stream.
   * @param {number} code_point Next code point read from the stream.
   * @return {(number|!Array.<number>)} Byte(s) to emit.
   */
  handler(e, t) {
    return t === pe ? ve : ft(t) ? t : oe(t, 63360, 63487) ? t - 63360 + 128 : wt(t);
  }
}
const No = aa(), nn = {
  // 9.1 utf-8
  // 9.1.1 utf-8 decoder
  // 9.1.2 utf-8 encoder
  /** @param {{fatal: boolean}} options */
  "UTF-8": (i) => new Mo(i),
  //
  // 11. Legacy multi-byte Chinese (simplified) encodings
  //
  // 11.1 gbk
  // 11.1.1 gbk decoder
  // gbk's decoder is gb18030's decoder.
  // 11.1.2 gbk encoder
  // gbk's encoder is gb18030's encoder with its gbk flag set.
  /** @param {{fatal: boolean}} options */
  GBK: (i) => new Mn(i, !0),
  // 11.2 gb18030
  // 11.2.1 gb18030 decoder
  // 11.2.2 gb18030 encoder
  /** @param {{fatal: boolean}} options */
  gb18030: (i) => new Mn(i),
  //
  // 12. Legacy multi-byte Chinese (traditional) encodings
  //
  // 12.1 Big5
  // 12.1.1 Big5 decoder
  // 12.1.2 Big5 encoder
  /** @param {{fatal: boolean}} options */
  Big5: (i) => new Eo(i),
  //
  // 13. Legacy multi-byte Japanese encodings
  //
  // 13.1 euc-jp
  // 13.1.1 euc-jp decoder
  // 13.1.2 euc-jp encoder
  /** @param {{fatal: boolean}} options */
  "EUC-JP": (i) => new So(i),
  // 13.2 iso-2022-jp
  // 13.2.1 iso-2022-jp decoder
  // 13.2.2 iso-2022-jp encoder
  /** @param {{fatal: boolean}} options */
  "ISO-2022-JP": (i) => new ko(i),
  // 13.3 Shift_JIS
  // 13.3.1 Shift_JIS decoder
  // 13.3.2 Shift_JIS encoder
  /** @param {{fatal: boolean}} options */
  Shift_JIS: (i) => new To(i),
  //
  // 14. Legacy multi-byte Korean encodings
  //
  // 14.1 euc-kr
  // 14.1.1 euc-kr decoder
  // 14.1.2 euc-kr encoder
  /** @param {{fatal: boolean}} options */
  "EUC-KR": (i) => new Co(i),
  //
  // 15. Legacy miscellaneous encodings
  //
  // 15.1 replacement
  // Not needed - API throws RangeError
  // 15.2 Common infrastructure for utf-16be and utf-16le
  // 15.2.1 shared utf-16 decoder
  // 15.2.2 shared utf-16 encoder
  // 15.3 utf-16be
  // 15.3.1 utf-16be decoder
  /** @param {{fatal: boolean}} options */
  "UTF-16BE": (i) => new Ln(!0, i),
  // 15.3.2 utf-16be encoder
  // 15.4 utf-16le
  // 15.4.1 utf-16le decoder
  /** @param {{fatal: boolean}} options */
  "UTF-16LE": (i) => new Ln(!1, i),
  // 15.4.2 utf-16le encoder
  // 15.5 x-user-defined
  // 15.5.1 x-user-defined decoder
  // 15.5.2 x-user-defined encoder
  /** @param {{fatal: boolean}} options */
  "x-user-defined": (i) => new Lo(i)
}, sn = {
  /** @param {{fatal: boolean}} options */
  "UTF-8": (i) => new Do(i),
  /** @param {{fatal: boolean}} options */
  GBK: (i) => new Dn(i),
  /** @param {{fatal: boolean}} options */
  gb18030: (i) => new Dn(i),
  /** @param {{fatal: boolean}} options */
  Big5: (i) => new vo(i),
  /** @param {{fatal: boolean}} options */
  "EUC-JP": (i) => new Bo(i),
  /** @param {{fatal: boolean}} options */
  "ISO-2022-JP": (i) => new Ao(i),
  /** @param {{fatal: boolean}} options */
  Shift_JIS: (i) => new Fo(i),
  /** @param {{fatal: boolean}} options */
  "EUC-KR": (i) => new Io(i),
  /** @param {{fatal: boolean}} options */
  "UTF-16BE": (i) => new Pn(!0, i),
  /** @param {{fatal: boolean}} options */
  "UTF-16LE": (i) => new Pn(!1, i),
  /** @param {{fatal: boolean}} options */
  "x-user-defined": (i) => new Po(i)
};
No && ia.forEach(function(i) {
  i.heading === "Legacy single-byte encodings" && i.encodings.forEach(function(e) {
    const t = e.name, r = Xe(t.toLowerCase());
    sn[t] = function(s) {
      return new Uo(r, s);
    }, nn[t] = function(s) {
      return new Ro(r, s);
    };
  });
});
class oa {
  /**
   *
   * @constructor
   * @param {!(Array.<number>|Uint8Array)} tokens Array of tokens that provide
   * the stream.
   */
  constructor(e) {
    this.tokens = Array.from(e), this.tokens.reverse();
  }
  /**
   * @return {boolean} True if end-of-stream has been hit.
   */
  endOfStream() {
    return !this.tokens.length;
  }
  /**
   * When a token is read from a stream, the first token in the
   * stream must be returned and subsequently removed, and
   * end-of-stream must be returned otherwise.
   *
   * @return {number} Get the next token from the stream, or
   * end_of_stream.
   */
  read() {
    return this.tokens.length ? this.tokens.pop() : pe;
  }
  /**
   * When one or more tokens are prepended to a stream, those tokens
   * must be inserted, in given order, before the first token in the
   * stream.
   *
   * @param {(number|!Array.<number>)} token The token(s) to prepend to the
   * stream.
   */
  prepend(e) {
    if (Array.isArray(e)) {
      const t = e;
      for (; t.length; )
        this.tokens.push(t.pop());
    } else
      this.tokens.push(e);
  }
  /**
   * When one or more tokens are pushed to a stream, those tokens
   * must be inserted, in given order, after the last token in the
   * stream.
   *
   * @param {(number|!Array.<number>)} token The tokens(s) to push to the
   * stream.
   */
  push(e) {
    if (Array.isArray(e)) {
      const t = e;
      for (; t.length; )
        this.tokens.unshift(t.shift());
    } else
      this.tokens.unshift(e);
  }
}
class Hr {
  constructor(e, t) {
    e = e !== void 0 ? String(e) : ra;
    const r = jr(t);
    this._encoding = null, this._decoder = null, this._ignoreBOM = !1, this._BOMseen = !1, this._error_mode = "replacement", this._do_not_flush = !1;
    const s = tn(e);
    if (s === null || s.name === "replacement")
      throw RangeError("Unknown encoding: " + e);
    if (!sn[s.name])
      throw Error("Decoder not present. Did you forget to include encoding-indexes.js first?");
    this._encoding = s, r.fatal && (this._error_mode = "fatal"), r.ignoreBOM && (this._ignoreBOM = !0);
  }
  // if (Object.defineProperty) {
  // The encoding attribute's getter must return encoding's name.
  //   Object.defineProperty(TextDecoder.prototype, 'encoding', {
  //     /** @this {TextDecoder} */
  //     get: function () { return this._encoding.name.toLowerCase(); }
  //   });
  get encoding() {
    return this._encoding.name.toLowerCase();
  }
  // The fatal attribute's getter must return true if error mode
  // is fatal, and false otherwise.
  //   Object.defineProperty(TextDecoder.prototype, 'fatal', {
  //     /** @this {TextDecoder} */
  //     get: function () { return this._error_mode === 'fatal'; }
  //   });
  get fatal() {
    return this._error_mode === "fatal";
  }
  // The ignoreBOM attribute's getter must return true if ignore
  // BOM flag is set, and false otherwise.
  //   Object.defineProperty(TextDecoder.prototype, 'ignoreBOM', {
  //     /** @this {TextDecoder} */
  //     get: function () { return this._ignoreBOM; }
  //   });
  get ignoreBOM() {
    return this._ignoreBOM;
  }
  // }
  /**
   * @param {BufferSource=} input The buffer of bytes to decode.
   * @param {Object=} options
   * @return {string} The decoded string.
   */
  decode(e, t) {
    const r = Oo(e), s = jr(t);
    this._do_not_flush || (this._decoder = sn[this._encoding.name]({
      fatal: this._error_mode === "fatal"
    }), this._BOMseen = !1), this._do_not_flush = !!s.stream;
    const n = new oa(r), u = [];
    let o;
    for (; ; ) {
      const p = n.read();
      if (p === pe || (o = this._decoder.handler(n, p), o === ve))
        break;
      o !== null && (Array.isArray(o) ? u.push.apply(
        u,
        /**@type {!Array.<number>}*/
        o
      ) : u.push(o));
    }
    if (!this._do_not_flush) {
      do {
        if (o = this._decoder.handler(n, n.read()), o === ve)
          break;
        o && (Array.isArray(o) ? u.push.apply(
          u,
          /**@type {!Array.<number>}*/
          o
        ) : u.push(o));
      } while (!n.endOfStream());
      this._decoder = null;
    }
    return this.serializeStream(u);
  }
  // A TextDecoder object also has an associated serialize stream
  // algorithm...
  /**
   * @param {!Array.<number>} stream
   * @return {string}
   * @this {TextDecoder}
   */
  serializeStream(e) {
    return po(["UTF-8", "UTF-16LE", "UTF-16BE"], this._encoding.name) && !this._ignoreBOM && !this._BOMseen && (e.length > 0 && e[0] === 65279 ? (this._BOMseen = !0, e.shift()) : e.length > 0 && (this._BOMseen = !0)), go(e);
  }
}
function Nn(i) {
  try {
    return i instanceof ArrayBuffer;
  } catch (e) {
    return console.error(e), !1;
  }
}
function Oo(i) {
  return typeof i != "object" ? new Uint8Array(0) : Nn(i) ? new Uint8Array(i) : "buffer" in i && Nn(i.buffer) ? new Uint8Array(i.buffer, i.byteOffset, i.byteLength) : new Uint8Array(0);
}
class ha {
  constructor(e, t) {
    const r = jr(t);
    if (this._encoding = null, this._encoder = null, this._do_not_flush = !1, this._fatal = r.fatal ? "fatal" : "replacement", r.NONSTANDARD_allowLegacyEncoding) {
      e = e ? String(e) : ra;
      const s = tn(e);
      if (s === null || s.name === "replacement")
        throw RangeError("Unknown encoding: " + e);
      if (!nn[s.name])
        throw Error("Encoder not present. Did you forget to include encoding-indexes.js first?");
      this._encoding = s;
    } else {
      this._encoding = tn("utf-8");
      const s = sa() || {};
      e !== void 0 && "console" in s && console.warn("TextEncoder constructor called with encoding label, which is ignored.");
    }
  }
  // if(Object.defineProperty) {
  //  // The encoding attribute's getter must return encoding's name.
  //   Object.defineProperty(TextEncoder.prototype, 'encoding', {
  //     /** @this {TextEncoder} */
  //     get: function () { return this._encoding.name.toLowerCase(); }
  //   });
  // }
  get encoding() {
    return this._encoding.name.toLowerCase();
  }
  /**
   * @param {string=} opt_string The string to encode.
   * @param {Object=} options
   * @return {!Uint8Array} Encoded bytes, as a Uint8Array.
   */
  encode(e, t) {
    e = e === void 0 ? "" : String(e);
    const r = jr(t);
    this._do_not_flush || (this._encoder = nn[this._encoding.name]({
      fatal: this._fatal === "fatal"
    })), this._do_not_flush = !!r.stream;
    const s = new oa(yo(e)), n = [];
    let u;
    for (; ; ) {
      const o = s.read();
      if (o === pe || (u = this._encoder.handler(s, o), u === ve))
        break;
      Array.isArray(u) ? n.push.apply(
        n,
        /**@type {!Array.<number>}*/
        u
      ) : n.push(u);
    }
    if (!this._do_not_flush) {
      for (; u = this._encoder.handler(s, s.read()), u !== ve; )
        Array.isArray(u) ? n.push.apply(
          n,
          /**@type {!Array.<number>}*/
          u
        ) : n.push(u);
      this._encoder = null;
    }
    return new Uint8Array(n);
  }
}
if (typeof window < "u") {
  const i = (e) => !(e in window) || typeof window[e] > "u" || window[e] === null;
  i("TextDecoder") && (window.TextDecoder = Hr), i("TextEncoder") && (window.TextEncoder = ha);
}
const jo = new ha(), Ho = new Hr();
function jt(i, e) {
  if (i.byteLength !== e.byteLength)
    return !1;
  const t = new Uint8Array(i), r = new Uint8Array(e);
  for (let s = 0, n = t.length; s < n; s++)
    if (t[s] !== r[s])
      return !1;
  return !0;
}
function Wt(i) {
  return i instanceof ArrayBuffer && (i = new Uint8Array(i)), Ho.decode(i);
}
function Dt(i) {
  return jo.encode(i);
}
function lt(i) {
  if (typeof atob == "function") {
    const e = atob(i), t = new Uint8Array(e.length);
    for (let r = 0; r < e.length; r++)
      t[r] = e.charCodeAt(r);
    return t;
  } else {
    const e = qe.from(i, "base64");
    return new Uint8Array(e);
  }
}
function Mt(i) {
  const e = i instanceof ArrayBuffer ? new Uint8Array(i) : i;
  if (typeof btoa == "function") {
    let t = "";
    for (let r = 0; r < e.length; r++)
      t += String.fromCharCode(e[r]);
    return btoa(t);
  } else
    return qe.from(i).toString("base64");
}
function gr(i) {
  const e = new Uint8Array(Math.ceil(i.length / 2));
  for (let t = 0; t < e.length; t++)
    e[t] = parseInt(i.substr(t * 2, 2), 16);
  return e;
}
function pr(i) {
  const e = i instanceof ArrayBuffer ? new Uint8Array(i) : i;
  let t = "";
  for (let r = 0; r < e.length; r++) {
    const s = e[r].toString(16);
    s.length === 1 && (t += "0"), t += s;
  }
  return t;
}
function ae(i) {
  if (i instanceof ArrayBuffer)
    return i;
  const e = i.buffer;
  return i.byteOffset === 0 && i.byteLength === e.byteLength ? e : i.buffer.slice(i.byteOffset, i.byteOffset + i.byteLength);
}
function me(i) {
  (i instanceof ArrayBuffer ? new Uint8Array(i) : i).fill(0);
}
const wu = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  arrayBufferEquals: jt,
  arrayToBuffer: ae,
  base64ToBytes: lt,
  bytesToBase64: Mt,
  bytesToHex: pr,
  bytesToString: Wt,
  hexToBytes: gr,
  stringToBytes: Dt,
  zeroBuffer: me
}, Symbol.toStringTag, { value: "Module" }));
var ir = typeof globalThis < "u" ? globalThis : typeof window < "u" ? window : typeof global < "u" ? global : typeof self < "u" ? self : {};
function _n(i) {
  return i && i.__esModule && Object.prototype.hasOwnProperty.call(i, "default") ? i.default : i;
}
function qo(i) {
  if (Object.prototype.hasOwnProperty.call(i, "__esModule")) return i;
  var e = i.default;
  if (typeof e == "function") {
    var t = function r() {
      return this instanceof r ? Reflect.construct(e, arguments, this.constructor) : e.apply(this, arguments);
    };
    t.prototype = e.prototype;
  } else t = {};
  return Object.defineProperty(t, "__esModule", { value: !0 }), Object.keys(i).forEach(function(r) {
    var s = Object.getOwnPropertyDescriptor(i, r);
    Object.defineProperty(t, r, s.get ? s : {
      enumerable: !0,
      get: function() {
        return i[r];
      }
    });
  }), t;
}
var ot = {}, Er = {}, Br = {}, On;
function Vo() {
  return On || (On = 1, Br.encrypt = function(i, e) {
    return i._cipher.encryptBlock(e);
  }, Br.decrypt = function(i, e) {
    return i._cipher.decryptBlock(e);
  }), Br;
}
var Sr = {}, ii, jn;
function br() {
  return jn || (jn = 1, ii = function(e, t) {
    for (var r = Math.min(e.length, t.length), s = new qe(r), n = 0; n < r; ++n)
      s[n] = e[n] ^ t[n];
    return s;
  }), ii;
}
var Hn;
function Go() {
  if (Hn) return Sr;
  Hn = 1;
  var i = br();
  return Sr.encrypt = function(e, t) {
    var r = i(t, e._prev);
    return e._prev = e._cipher.encryptBlock(r), e._prev;
  }, Sr.decrypt = function(e, t) {
    var r = e._prev;
    e._prev = t;
    var s = e._cipher.decryptBlock(t);
    return i(s, r);
  }, Sr;
}
var ni = {}, Ir = { exports: {} }, si = {}, ur = {}, qn;
function Ko() {
  if (qn) return ur;
  qn = 1, ur.byteLength = o, ur.toByteArray = y, ur.fromByteArray = m;
  for (var i = [], e = [], t = typeof Uint8Array < "u" ? Uint8Array : Array, r = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/", s = 0, n = r.length; s < n; ++s)
    i[s] = r[s], e[r.charCodeAt(s)] = s;
  e[45] = 62, e[95] = 63;
  function u(w) {
    var b = w.length;
    if (b % 4 > 0)
      throw new Error("Invalid string. Length must be a multiple of 4");
    var x = w.indexOf("=");
    x === -1 && (x = b);
    var S = x === b ? 0 : 4 - x % 4;
    return [x, S];
  }
  function o(w) {
    var b = u(w), x = b[0], S = b[1];
    return (x + S) * 3 / 4 - S;
  }
  function p(w, b, x) {
    return (b + x) * 3 / 4 - x;
  }
  function y(w) {
    var b, x = u(w), S = x[0], E = x[1], B = new t(p(w, S, E)), A = 0, k = E > 0 ? S - 4 : S, T;
    for (T = 0; T < k; T += 4)
      b = e[w.charCodeAt(T)] << 18 | e[w.charCodeAt(T + 1)] << 12 | e[w.charCodeAt(T + 2)] << 6 | e[w.charCodeAt(T + 3)], B[A++] = b >> 16 & 255, B[A++] = b >> 8 & 255, B[A++] = b & 255;
    return E === 2 && (b = e[w.charCodeAt(T)] << 2 | e[w.charCodeAt(T + 1)] >> 4, B[A++] = b & 255), E === 1 && (b = e[w.charCodeAt(T)] << 10 | e[w.charCodeAt(T + 1)] << 4 | e[w.charCodeAt(T + 2)] >> 2, B[A++] = b >> 8 & 255, B[A++] = b & 255), B;
  }
  function f(w) {
    return i[w >> 18 & 63] + i[w >> 12 & 63] + i[w >> 6 & 63] + i[w & 63];
  }
  function v(w, b, x) {
    for (var S, E = [], B = b; B < x; B += 3)
      S = (w[B] << 16 & 16711680) + (w[B + 1] << 8 & 65280) + (w[B + 2] & 255), E.push(f(S));
    return E.join("");
  }
  function m(w) {
    for (var b, x = w.length, S = x % 3, E = [], B = 16383, A = 0, k = x - S; A < k; A += B)
      E.push(v(w, A, A + B > k ? k : A + B));
    return S === 1 ? (b = w[x - 1], E.push(
      i[b >> 2] + i[b << 4 & 63] + "=="
    )) : S === 2 && (b = (w[x - 2] << 8) + w[x - 1], E.push(
      i[b >> 10] + i[b >> 4 & 63] + i[b << 2 & 63] + "="
    )), E.join("");
  }
  return ur;
}
var Cr = {};
/*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> */
var Vn;
function Wo() {
  return Vn || (Vn = 1, Cr.read = function(i, e, t, r, s) {
    var n, u, o = s * 8 - r - 1, p = (1 << o) - 1, y = p >> 1, f = -7, v = t ? s - 1 : 0, m = t ? -1 : 1, w = i[e + v];
    for (v += m, n = w & (1 << -f) - 1, w >>= -f, f += o; f > 0; n = n * 256 + i[e + v], v += m, f -= 8)
      ;
    for (u = n & (1 << -f) - 1, n >>= -f, f += r; f > 0; u = u * 256 + i[e + v], v += m, f -= 8)
      ;
    if (n === 0)
      n = 1 - y;
    else {
      if (n === p)
        return u ? NaN : (w ? -1 : 1) * (1 / 0);
      u = u + Math.pow(2, r), n = n - y;
    }
    return (w ? -1 : 1) * u * Math.pow(2, n - r);
  }, Cr.write = function(i, e, t, r, s, n) {
    var u, o, p, y = n * 8 - s - 1, f = (1 << y) - 1, v = f >> 1, m = s === 23 ? Math.pow(2, -24) - Math.pow(2, -77) : 0, w = r ? 0 : n - 1, b = r ? 1 : -1, x = e < 0 || e === 0 && 1 / e < 0 ? 1 : 0;
    for (e = Math.abs(e), isNaN(e) || e === 1 / 0 ? (o = isNaN(e) ? 1 : 0, u = f) : (u = Math.floor(Math.log(e) / Math.LN2), e * (p = Math.pow(2, -u)) < 1 && (u--, p *= 2), u + v >= 1 ? e += m / p : e += m * Math.pow(2, 1 - v), e * p >= 2 && (u++, p /= 2), u + v >= f ? (o = 0, u = f) : u + v >= 1 ? (o = (e * p - 1) * Math.pow(2, s), u = u + v) : (o = e * Math.pow(2, v - 1) * Math.pow(2, s), u = 0)); s >= 8; i[t + w] = o & 255, w += b, o /= 256, s -= 8)
      ;
    for (u = u << s | o, y += s; y > 0; i[t + w] = u & 255, w += b, u /= 256, y -= 8)
      ;
    i[t + w - b] |= x * 128;
  }), Cr;
}
/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> Aboukhadijeh <https://feross.org>
 * @license  MIT
 */
var Gn;
function xr() {
  return Gn || (Gn = 1, function(i) {
    const e = Ko(), t = Wo(), r = typeof Symbol == "function" && typeof Symbol.for == "function" ? Symbol.for("nodejs.util.inspect.custom") : null;
    i.Buffer = o, i.SlowBuffer = B, i.INSPECT_MAX_BYTES = 50;
    const s = **********;
    i.kMaxLength = s, o.TYPED_ARRAY_SUPPORT = n(), !o.TYPED_ARRAY_SUPPORT && typeof console < "u" && typeof console.error == "function" && console.error(
      "This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."
    );
    function n() {
      try {
        const _ = new Uint8Array(1), l = { foo: function() {
          return 42;
        } };
        return Object.setPrototypeOf(l, Uint8Array.prototype), Object.setPrototypeOf(_, l), _.foo() === 42;
      } catch {
        return !1;
      }
    }
    Object.defineProperty(o.prototype, "parent", {
      enumerable: !0,
      get: function() {
        if (o.isBuffer(this))
          return this.buffer;
      }
    }), Object.defineProperty(o.prototype, "offset", {
      enumerable: !0,
      get: function() {
        if (o.isBuffer(this))
          return this.byteOffset;
      }
    });
    function u(_) {
      if (_ > s)
        throw new RangeError('The value "' + _ + '" is invalid for option "size"');
      const l = new Uint8Array(_);
      return Object.setPrototypeOf(l, o.prototype), l;
    }
    function o(_, l, d) {
      if (typeof _ == "number") {
        if (typeof l == "string")
          throw new TypeError(
            'The "string" argument must be of type string. Received type number'
          );
        return v(_);
      }
      return p(_, l, d);
    }
    o.poolSize = 8192;
    function p(_, l, d) {
      if (typeof _ == "string")
        return m(_, l);
      if (ArrayBuffer.isView(_))
        return b(_);
      if (_ == null)
        throw new TypeError(
          "The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type " + typeof _
        );
      if (Ce(_, ArrayBuffer) || _ && Ce(_.buffer, ArrayBuffer) || typeof SharedArrayBuffer < "u" && (Ce(_, SharedArrayBuffer) || _ && Ce(_.buffer, SharedArrayBuffer)))
        return x(_, l, d);
      if (typeof _ == "number")
        throw new TypeError(
          'The "value" argument must not be of type number. Received type number'
        );
      const c = _.valueOf && _.valueOf();
      if (c != null && c !== _)
        return o.from(c, l, d);
      const a = S(_);
      if (a) return a;
      if (typeof Symbol < "u" && Symbol.toPrimitive != null && typeof _[Symbol.toPrimitive] == "function")
        return o.from(_[Symbol.toPrimitive]("string"), l, d);
      throw new TypeError(
        "The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type " + typeof _
      );
    }
    o.from = function(_, l, d) {
      return p(_, l, d);
    }, Object.setPrototypeOf(o.prototype, Uint8Array.prototype), Object.setPrototypeOf(o, Uint8Array);
    function y(_) {
      if (typeof _ != "number")
        throw new TypeError('"size" argument must be of type number');
      if (_ < 0)
        throw new RangeError('The value "' + _ + '" is invalid for option "size"');
    }
    function f(_, l, d) {
      return y(_), _ <= 0 ? u(_) : l !== void 0 ? typeof d == "string" ? u(_).fill(l, d) : u(_).fill(l) : u(_);
    }
    o.alloc = function(_, l, d) {
      return f(_, l, d);
    };
    function v(_) {
      return y(_), u(_ < 0 ? 0 : E(_) | 0);
    }
    o.allocUnsafe = function(_) {
      return v(_);
    }, o.allocUnsafeSlow = function(_) {
      return v(_);
    };
    function m(_, l) {
      if ((typeof l != "string" || l === "") && (l = "utf8"), !o.isEncoding(l))
        throw new TypeError("Unknown encoding: " + l);
      const d = A(_, l) | 0;
      let c = u(d);
      const a = c.write(_, l);
      return a !== d && (c = c.slice(0, a)), c;
    }
    function w(_) {
      const l = _.length < 0 ? 0 : E(_.length) | 0, d = u(l);
      for (let c = 0; c < l; c += 1)
        d[c] = _[c] & 255;
      return d;
    }
    function b(_) {
      if (Ce(_, Uint8Array)) {
        const l = new Uint8Array(_);
        return x(l.buffer, l.byteOffset, l.byteLength);
      }
      return w(_);
    }
    function x(_, l, d) {
      if (l < 0 || _.byteLength < l)
        throw new RangeError('"offset" is outside of buffer bounds');
      if (_.byteLength < l + (d || 0))
        throw new RangeError('"length" is outside of buffer bounds');
      let c;
      return l === void 0 && d === void 0 ? c = new Uint8Array(_) : d === void 0 ? c = new Uint8Array(_, l) : c = new Uint8Array(_, l, d), Object.setPrototypeOf(c, o.prototype), c;
    }
    function S(_) {
      if (o.isBuffer(_)) {
        const l = E(_.length) | 0, d = u(l);
        return d.length === 0 || _.copy(d, 0, 0, l), d;
      }
      if (_.length !== void 0)
        return typeof _.length != "number" || et(_.length) ? u(0) : w(_);
      if (_.type === "Buffer" && Array.isArray(_.data))
        return w(_.data);
    }
    function E(_) {
      if (_ >= s)
        throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x" + s.toString(16) + " bytes");
      return _ | 0;
    }
    function B(_) {
      return +_ != _ && (_ = 0), o.alloc(+_);
    }
    o.isBuffer = function(l) {
      return l != null && l._isBuffer === !0 && l !== o.prototype;
    }, o.compare = function(l, d) {
      if (Ce(l, Uint8Array) && (l = o.from(l, l.offset, l.byteLength)), Ce(d, Uint8Array) && (d = o.from(d, d.offset, d.byteLength)), !o.isBuffer(l) || !o.isBuffer(d))
        throw new TypeError(
          'The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array'
        );
      if (l === d) return 0;
      let c = l.length, a = d.length;
      for (let h = 0, g = Math.min(c, a); h < g; ++h)
        if (l[h] !== d[h]) {
          c = l[h], a = d[h];
          break;
        }
      return c < a ? -1 : a < c ? 1 : 0;
    }, o.isEncoding = function(l) {
      switch (String(l).toLowerCase()) {
        case "hex":
        case "utf8":
        case "utf-8":
        case "ascii":
        case "latin1":
        case "binary":
        case "base64":
        case "ucs2":
        case "ucs-2":
        case "utf16le":
        case "utf-16le":
          return !0;
        default:
          return !1;
      }
    }, o.concat = function(l, d) {
      if (!Array.isArray(l))
        throw new TypeError('"list" argument must be an Array of Buffers');
      if (l.length === 0)
        return o.alloc(0);
      let c;
      if (d === void 0)
        for (d = 0, c = 0; c < l.length; ++c)
          d += l[c].length;
      const a = o.allocUnsafe(d);
      let h = 0;
      for (c = 0; c < l.length; ++c) {
        let g = l[c];
        if (Ce(g, Uint8Array))
          h + g.length > a.length ? (o.isBuffer(g) || (g = o.from(g)), g.copy(a, h)) : Uint8Array.prototype.set.call(
            a,
            g,
            h
          );
        else if (o.isBuffer(g))
          g.copy(a, h);
        else
          throw new TypeError('"list" argument must be an Array of Buffers');
        h += g.length;
      }
      return a;
    };
    function A(_, l) {
      if (o.isBuffer(_))
        return _.length;
      if (ArrayBuffer.isView(_) || Ce(_, ArrayBuffer))
        return _.byteLength;
      if (typeof _ != "string")
        throw new TypeError(
          'The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type ' + typeof _
        );
      const d = _.length, c = arguments.length > 2 && arguments[2] === !0;
      if (!c && d === 0) return 0;
      let a = !1;
      for (; ; )
        switch (l) {
          case "ascii":
          case "latin1":
          case "binary":
            return d;
          case "utf8":
          case "utf-8":
            return ce(_).length;
          case "ucs2":
          case "ucs-2":
          case "utf16le":
          case "utf-16le":
            return d * 2;
          case "hex":
            return d >>> 1;
          case "base64":
            return st(_).length;
          default:
            if (a)
              return c ? -1 : ce(_).length;
            l = ("" + l).toLowerCase(), a = !0;
        }
    }
    o.byteLength = A;
    function k(_, l, d) {
      let c = !1;
      if ((l === void 0 || l < 0) && (l = 0), l > this.length || ((d === void 0 || d > this.length) && (d = this.length), d <= 0) || (d >>>= 0, l >>>= 0, d <= l))
        return "";
      for (_ || (_ = "utf8"); ; )
        switch (_) {
          case "hex":
            return ge(this, l, d);
          case "utf8":
          case "utf-8":
            return ee(this, l, d);
          case "ascii":
            return N(this, l, d);
          case "latin1":
          case "binary":
            return ye(this, l, d);
          case "base64":
            return X(this, l, d);
          case "ucs2":
          case "ucs-2":
          case "utf16le":
          case "utf-16le":
            return Me(this, l, d);
          default:
            if (c) throw new TypeError("Unknown encoding: " + _);
            _ = (_ + "").toLowerCase(), c = !0;
        }
    }
    o.prototype._isBuffer = !0;
    function T(_, l, d) {
      const c = _[l];
      _[l] = _[d], _[d] = c;
    }
    o.prototype.swap16 = function() {
      const l = this.length;
      if (l % 2 !== 0)
        throw new RangeError("Buffer size must be a multiple of 16-bits");
      for (let d = 0; d < l; d += 2)
        T(this, d, d + 1);
      return this;
    }, o.prototype.swap32 = function() {
      const l = this.length;
      if (l % 4 !== 0)
        throw new RangeError("Buffer size must be a multiple of 32-bits");
      for (let d = 0; d < l; d += 4)
        T(this, d, d + 3), T(this, d + 1, d + 2);
      return this;
    }, o.prototype.swap64 = function() {
      const l = this.length;
      if (l % 8 !== 0)
        throw new RangeError("Buffer size must be a multiple of 64-bits");
      for (let d = 0; d < l; d += 8)
        T(this, d, d + 7), T(this, d + 1, d + 6), T(this, d + 2, d + 5), T(this, d + 3, d + 4);
      return this;
    }, o.prototype.toString = function() {
      const l = this.length;
      return l === 0 ? "" : arguments.length === 0 ? ee(this, 0, l) : k.apply(this, arguments);
    }, o.prototype.toLocaleString = o.prototype.toString, o.prototype.equals = function(l) {
      if (!o.isBuffer(l)) throw new TypeError("Argument must be a Buffer");
      return this === l ? !0 : o.compare(this, l) === 0;
    }, o.prototype.inspect = function() {
      let l = "";
      const d = i.INSPECT_MAX_BYTES;
      return l = this.toString("hex", 0, d).replace(/(.{2})/g, "$1 ").trim(), this.length > d && (l += " ... "), "<Buffer " + l + ">";
    }, r && (o.prototype[r] = o.prototype.inspect), o.prototype.compare = function(l, d, c, a, h) {
      if (Ce(l, Uint8Array) && (l = o.from(l, l.offset, l.byteLength)), !o.isBuffer(l))
        throw new TypeError(
          'The "target" argument must be one of type Buffer or Uint8Array. Received type ' + typeof l
        );
      if (d === void 0 && (d = 0), c === void 0 && (c = l ? l.length : 0), a === void 0 && (a = 0), h === void 0 && (h = this.length), d < 0 || c > l.length || a < 0 || h > this.length)
        throw new RangeError("out of range index");
      if (a >= h && d >= c)
        return 0;
      if (a >= h)
        return -1;
      if (d >= c)
        return 1;
      if (d >>>= 0, c >>>= 0, a >>>= 0, h >>>= 0, this === l) return 0;
      let g = h - a, F = c - d;
      const M = Math.min(g, F), O = this.slice(a, h), Y = l.slice(d, c);
      for (let te = 0; te < M; ++te)
        if (O[te] !== Y[te]) {
          g = O[te], F = Y[te];
          break;
        }
      return g < F ? -1 : F < g ? 1 : 0;
    };
    function D(_, l, d, c, a) {
      if (_.length === 0) return -1;
      if (typeof d == "string" ? (c = d, d = 0) : d > ********** ? d = ********** : d < -2147483648 && (d = -2147483648), d = +d, et(d) && (d = a ? 0 : _.length - 1), d < 0 && (d = _.length + d), d >= _.length) {
        if (a) return -1;
        d = _.length - 1;
      } else if (d < 0)
        if (a) d = 0;
        else return -1;
      if (typeof l == "string" && (l = o.from(l, c)), o.isBuffer(l))
        return l.length === 0 ? -1 : q(_, l, d, c, a);
      if (typeof l == "number")
        return l = l & 255, typeof Uint8Array.prototype.indexOf == "function" ? a ? Uint8Array.prototype.indexOf.call(_, l, d) : Uint8Array.prototype.lastIndexOf.call(_, l, d) : q(_, [l], d, c, a);
      throw new TypeError("val must be string, number or Buffer");
    }
    function q(_, l, d, c, a) {
      let h = 1, g = _.length, F = l.length;
      if (c !== void 0 && (c = String(c).toLowerCase(), c === "ucs2" || c === "ucs-2" || c === "utf16le" || c === "utf-16le")) {
        if (_.length < 2 || l.length < 2)
          return -1;
        h = 2, g /= 2, F /= 2, d /= 2;
      }
      function M(Y, te) {
        return h === 1 ? Y[te] : Y.readUInt16BE(te * h);
      }
      let O;
      if (a) {
        let Y = -1;
        for (O = d; O < g; O++)
          if (M(_, O) === M(l, Y === -1 ? 0 : O - Y)) {
            if (Y === -1 && (Y = O), O - Y + 1 === F) return Y * h;
          } else
            Y !== -1 && (O -= O - Y), Y = -1;
      } else
        for (d + F > g && (d = g - F), O = d; O >= 0; O--) {
          let Y = !0;
          for (let te = 0; te < F; te++)
            if (M(_, O + te) !== M(l, te)) {
              Y = !1;
              break;
            }
          if (Y) return O;
        }
      return -1;
    }
    o.prototype.includes = function(l, d, c) {
      return this.indexOf(l, d, c) !== -1;
    }, o.prototype.indexOf = function(l, d, c) {
      return D(this, l, d, c, !0);
    }, o.prototype.lastIndexOf = function(l, d, c) {
      return D(this, l, d, c, !1);
    };
    function R(_, l, d, c) {
      d = Number(d) || 0;
      const a = _.length - d;
      c ? (c = Number(c), c > a && (c = a)) : c = a;
      const h = l.length;
      c > h / 2 && (c = h / 2);
      let g;
      for (g = 0; g < c; ++g) {
        const F = parseInt(l.substr(g * 2, 2), 16);
        if (et(F)) return g;
        _[d + g] = F;
      }
      return g;
    }
    function G(_, l, d, c) {
      return Ue(ce(l, _.length - d), _, d, c);
    }
    function J(_, l, d, c) {
      return Ue(Qe(l), _, d, c);
    }
    function se(_, l, d, c) {
      return Ue(st(l), _, d, c);
    }
    function Z(_, l, d, c) {
      return Ue(mt(l, _.length - d), _, d, c);
    }
    o.prototype.write = function(l, d, c, a) {
      if (d === void 0)
        a = "utf8", c = this.length, d = 0;
      else if (c === void 0 && typeof d == "string")
        a = d, c = this.length, d = 0;
      else if (isFinite(d))
        d = d >>> 0, isFinite(c) ? (c = c >>> 0, a === void 0 && (a = "utf8")) : (a = c, c = void 0);
      else
        throw new Error(
          "Buffer.write(string, encoding, offset[, length]) is no longer supported"
        );
      const h = this.length - d;
      if ((c === void 0 || c > h) && (c = h), l.length > 0 && (c < 0 || d < 0) || d > this.length)
        throw new RangeError("Attempt to write outside buffer bounds");
      a || (a = "utf8");
      let g = !1;
      for (; ; )
        switch (a) {
          case "hex":
            return R(this, l, d, c);
          case "utf8":
          case "utf-8":
            return G(this, l, d, c);
          case "ascii":
          case "latin1":
          case "binary":
            return J(this, l, d, c);
          case "base64":
            return se(this, l, d, c);
          case "ucs2":
          case "ucs-2":
          case "utf16le":
          case "utf-16le":
            return Z(this, l, d, c);
          default:
            if (g) throw new TypeError("Unknown encoding: " + a);
            a = ("" + a).toLowerCase(), g = !0;
        }
    }, o.prototype.toJSON = function() {
      return {
        type: "Buffer",
        data: Array.prototype.slice.call(this._arr || this, 0)
      };
    };
    function X(_, l, d) {
      return l === 0 && d === _.length ? e.fromByteArray(_) : e.fromByteArray(_.slice(l, d));
    }
    function ee(_, l, d) {
      d = Math.min(_.length, d);
      const c = [];
      let a = l;
      for (; a < d; ) {
        const h = _[a];
        let g = null, F = h > 239 ? 4 : h > 223 ? 3 : h > 191 ? 2 : 1;
        if (a + F <= d) {
          let M, O, Y, te;
          switch (F) {
            case 1:
              h < 128 && (g = h);
              break;
            case 2:
              M = _[a + 1], (M & 192) === 128 && (te = (h & 31) << 6 | M & 63, te > 127 && (g = te));
              break;
            case 3:
              M = _[a + 1], O = _[a + 2], (M & 192) === 128 && (O & 192) === 128 && (te = (h & 15) << 12 | (M & 63) << 6 | O & 63, te > 2047 && (te < 55296 || te > 57343) && (g = te));
              break;
            case 4:
              M = _[a + 1], O = _[a + 2], Y = _[a + 3], (M & 192) === 128 && (O & 192) === 128 && (Y & 192) === 128 && (te = (h & 15) << 18 | (M & 63) << 12 | (O & 63) << 6 | Y & 63, te > 65535 && te < 1114112 && (g = te));
          }
        }
        g === null ? (g = 65533, F = 1) : g > 65535 && (g -= 65536, c.push(g >>> 10 & 1023 | 55296), g = 56320 | g & 1023), c.push(g), a += F;
      }
      return ie(c);
    }
    const re = 4096;
    function ie(_) {
      const l = _.length;
      if (l <= re)
        return String.fromCharCode.apply(String, _);
      let d = "", c = 0;
      for (; c < l; )
        d += String.fromCharCode.apply(
          String,
          _.slice(c, c += re)
        );
      return d;
    }
    function N(_, l, d) {
      let c = "";
      d = Math.min(_.length, d);
      for (let a = l; a < d; ++a)
        c += String.fromCharCode(_[a] & 127);
      return c;
    }
    function ye(_, l, d) {
      let c = "";
      d = Math.min(_.length, d);
      for (let a = l; a < d; ++a)
        c += String.fromCharCode(_[a]);
      return c;
    }
    function ge(_, l, d) {
      const c = _.length;
      (!l || l < 0) && (l = 0), (!d || d < 0 || d > c) && (d = c);
      let a = "";
      for (let h = l; h < d; ++h)
        a += at[_[h]];
      return a;
    }
    function Me(_, l, d) {
      const c = _.slice(l, d);
      let a = "";
      for (let h = 0; h < c.length - 1; h += 2)
        a += String.fromCharCode(c[h] + c[h + 1] * 256);
      return a;
    }
    o.prototype.slice = function(l, d) {
      const c = this.length;
      l = ~~l, d = d === void 0 ? c : ~~d, l < 0 ? (l += c, l < 0 && (l = 0)) : l > c && (l = c), d < 0 ? (d += c, d < 0 && (d = 0)) : d > c && (d = c), d < l && (d = l);
      const a = this.subarray(l, d);
      return Object.setPrototypeOf(a, o.prototype), a;
    };
    function ue(_, l, d) {
      if (_ % 1 !== 0 || _ < 0) throw new RangeError("offset is not uint");
      if (_ + l > d) throw new RangeError("Trying to access beyond buffer length");
    }
    o.prototype.readUintLE = o.prototype.readUIntLE = function(l, d, c) {
      l = l >>> 0, d = d >>> 0, c || ue(l, d, this.length);
      let a = this[l], h = 1, g = 0;
      for (; ++g < d && (h *= 256); )
        a += this[l + g] * h;
      return a;
    }, o.prototype.readUintBE = o.prototype.readUIntBE = function(l, d, c) {
      l = l >>> 0, d = d >>> 0, c || ue(l, d, this.length);
      let a = this[l + --d], h = 1;
      for (; d > 0 && (h *= 256); )
        a += this[l + --d] * h;
      return a;
    }, o.prototype.readUint8 = o.prototype.readUInt8 = function(l, d) {
      return l = l >>> 0, d || ue(l, 1, this.length), this[l];
    }, o.prototype.readUint16LE = o.prototype.readUInt16LE = function(l, d) {
      return l = l >>> 0, d || ue(l, 2, this.length), this[l] | this[l + 1] << 8;
    }, o.prototype.readUint16BE = o.prototype.readUInt16BE = function(l, d) {
      return l = l >>> 0, d || ue(l, 2, this.length), this[l] << 8 | this[l + 1];
    }, o.prototype.readUint32LE = o.prototype.readUInt32LE = function(l, d) {
      return l = l >>> 0, d || ue(l, 4, this.length), (this[l] | this[l + 1] << 8 | this[l + 2] << 16) + this[l + 3] * 16777216;
    }, o.prototype.readUint32BE = o.prototype.readUInt32BE = function(l, d) {
      return l = l >>> 0, d || ue(l, 4, this.length), this[l] * 16777216 + (this[l + 1] << 16 | this[l + 2] << 8 | this[l + 3]);
    }, o.prototype.readBigUInt64LE = Fe(function(l) {
      l = l >>> 0, K(l, "offset");
      const d = this[l], c = this[l + 7];
      (d === void 0 || c === void 0) && $(l, this.length - 8);
      const a = d + this[++l] * 2 ** 8 + this[++l] * 2 ** 16 + this[++l] * 2 ** 24, h = this[++l] + this[++l] * 2 ** 8 + this[++l] * 2 ** 16 + c * 2 ** 24;
      return BigInt(a) + (BigInt(h) << BigInt(32));
    }), o.prototype.readBigUInt64BE = Fe(function(l) {
      l = l >>> 0, K(l, "offset");
      const d = this[l], c = this[l + 7];
      (d === void 0 || c === void 0) && $(l, this.length - 8);
      const a = d * 2 ** 24 + this[++l] * 2 ** 16 + this[++l] * 2 ** 8 + this[++l], h = this[++l] * 2 ** 24 + this[++l] * 2 ** 16 + this[++l] * 2 ** 8 + c;
      return (BigInt(a) << BigInt(32)) + BigInt(h);
    }), o.prototype.readIntLE = function(l, d, c) {
      l = l >>> 0, d = d >>> 0, c || ue(l, d, this.length);
      let a = this[l], h = 1, g = 0;
      for (; ++g < d && (h *= 256); )
        a += this[l + g] * h;
      return h *= 128, a >= h && (a -= Math.pow(2, 8 * d)), a;
    }, o.prototype.readIntBE = function(l, d, c) {
      l = l >>> 0, d = d >>> 0, c || ue(l, d, this.length);
      let a = d, h = 1, g = this[l + --a];
      for (; a > 0 && (h *= 256); )
        g += this[l + --a] * h;
      return h *= 128, g >= h && (g -= Math.pow(2, 8 * d)), g;
    }, o.prototype.readInt8 = function(l, d) {
      return l = l >>> 0, d || ue(l, 1, this.length), this[l] & 128 ? (255 - this[l] + 1) * -1 : this[l];
    }, o.prototype.readInt16LE = function(l, d) {
      l = l >>> 0, d || ue(l, 2, this.length);
      const c = this[l] | this[l + 1] << 8;
      return c & 32768 ? c | 4294901760 : c;
    }, o.prototype.readInt16BE = function(l, d) {
      l = l >>> 0, d || ue(l, 2, this.length);
      const c = this[l + 1] | this[l] << 8;
      return c & 32768 ? c | 4294901760 : c;
    }, o.prototype.readInt32LE = function(l, d) {
      return l = l >>> 0, d || ue(l, 4, this.length), this[l] | this[l + 1] << 8 | this[l + 2] << 16 | this[l + 3] << 24;
    }, o.prototype.readInt32BE = function(l, d) {
      return l = l >>> 0, d || ue(l, 4, this.length), this[l] << 24 | this[l + 1] << 16 | this[l + 2] << 8 | this[l + 3];
    }, o.prototype.readBigInt64LE = Fe(function(l) {
      l = l >>> 0, K(l, "offset");
      const d = this[l], c = this[l + 7];
      (d === void 0 || c === void 0) && $(l, this.length - 8);
      const a = this[l + 4] + this[l + 5] * 2 ** 8 + this[l + 6] * 2 ** 16 + (c << 24);
      return (BigInt(a) << BigInt(32)) + BigInt(d + this[++l] * 2 ** 8 + this[++l] * 2 ** 16 + this[++l] * 2 ** 24);
    }), o.prototype.readBigInt64BE = Fe(function(l) {
      l = l >>> 0, K(l, "offset");
      const d = this[l], c = this[l + 7];
      (d === void 0 || c === void 0) && $(l, this.length - 8);
      const a = (d << 24) + // Overflow
      this[++l] * 2 ** 16 + this[++l] * 2 ** 8 + this[++l];
      return (BigInt(a) << BigInt(32)) + BigInt(this[++l] * 2 ** 24 + this[++l] * 2 ** 16 + this[++l] * 2 ** 8 + c);
    }), o.prototype.readFloatLE = function(l, d) {
      return l = l >>> 0, d || ue(l, 4, this.length), t.read(this, l, !0, 23, 4);
    }, o.prototype.readFloatBE = function(l, d) {
      return l = l >>> 0, d || ue(l, 4, this.length), t.read(this, l, !1, 23, 4);
    }, o.prototype.readDoubleLE = function(l, d) {
      return l = l >>> 0, d || ue(l, 8, this.length), t.read(this, l, !0, 52, 8);
    }, o.prototype.readDoubleBE = function(l, d) {
      return l = l >>> 0, d || ue(l, 8, this.length), t.read(this, l, !1, 52, 8);
    };
    function le(_, l, d, c, a, h) {
      if (!o.isBuffer(_)) throw new TypeError('"buffer" argument must be a Buffer instance');
      if (l > a || l < h) throw new RangeError('"value" argument is out of bounds');
      if (d + c > _.length) throw new RangeError("Index out of range");
    }
    o.prototype.writeUintLE = o.prototype.writeUIntLE = function(l, d, c, a) {
      if (l = +l, d = d >>> 0, c = c >>> 0, !a) {
        const F = Math.pow(2, 8 * c) - 1;
        le(this, l, d, c, F, 0);
      }
      let h = 1, g = 0;
      for (this[d] = l & 255; ++g < c && (h *= 256); )
        this[d + g] = l / h & 255;
      return d + c;
    }, o.prototype.writeUintBE = o.prototype.writeUIntBE = function(l, d, c, a) {
      if (l = +l, d = d >>> 0, c = c >>> 0, !a) {
        const F = Math.pow(2, 8 * c) - 1;
        le(this, l, d, c, F, 0);
      }
      let h = c - 1, g = 1;
      for (this[d + h] = l & 255; --h >= 0 && (g *= 256); )
        this[d + h] = l / g & 255;
      return d + c;
    }, o.prototype.writeUint8 = o.prototype.writeUInt8 = function(l, d, c) {
      return l = +l, d = d >>> 0, c || le(this, l, d, 1, 255, 0), this[d] = l & 255, d + 1;
    }, o.prototype.writeUint16LE = o.prototype.writeUInt16LE = function(l, d, c) {
      return l = +l, d = d >>> 0, c || le(this, l, d, 2, 65535, 0), this[d] = l & 255, this[d + 1] = l >>> 8, d + 2;
    }, o.prototype.writeUint16BE = o.prototype.writeUInt16BE = function(l, d, c) {
      return l = +l, d = d >>> 0, c || le(this, l, d, 2, 65535, 0), this[d] = l >>> 8, this[d + 1] = l & 255, d + 2;
    }, o.prototype.writeUint32LE = o.prototype.writeUInt32LE = function(l, d, c) {
      return l = +l, d = d >>> 0, c || le(this, l, d, 4, 4294967295, 0), this[d + 3] = l >>> 24, this[d + 2] = l >>> 16, this[d + 1] = l >>> 8, this[d] = l & 255, d + 4;
    }, o.prototype.writeUint32BE = o.prototype.writeUInt32BE = function(l, d, c) {
      return l = +l, d = d >>> 0, c || le(this, l, d, 4, 4294967295, 0), this[d] = l >>> 24, this[d + 1] = l >>> 16, this[d + 2] = l >>> 8, this[d + 3] = l & 255, d + 4;
    };
    function Ee(_, l, d, c, a) {
      C(l, c, a, _, d, 7);
      let h = Number(l & BigInt(4294967295));
      _[d++] = h, h = h >> 8, _[d++] = h, h = h >> 8, _[d++] = h, h = h >> 8, _[d++] = h;
      let g = Number(l >> BigInt(32) & BigInt(4294967295));
      return _[d++] = g, g = g >> 8, _[d++] = g, g = g >> 8, _[d++] = g, g = g >> 8, _[d++] = g, d;
    }
    function he(_, l, d, c, a) {
      C(l, c, a, _, d, 7);
      let h = Number(l & BigInt(4294967295));
      _[d + 7] = h, h = h >> 8, _[d + 6] = h, h = h >> 8, _[d + 5] = h, h = h >> 8, _[d + 4] = h;
      let g = Number(l >> BigInt(32) & BigInt(4294967295));
      return _[d + 3] = g, g = g >> 8, _[d + 2] = g, g = g >> 8, _[d + 1] = g, g = g >> 8, _[d] = g, d + 8;
    }
    o.prototype.writeBigUInt64LE = Fe(function(l, d = 0) {
      return Ee(this, l, d, BigInt(0), BigInt("0xffffffffffffffff"));
    }), o.prototype.writeBigUInt64BE = Fe(function(l, d = 0) {
      return he(this, l, d, BigInt(0), BigInt("0xffffffffffffffff"));
    }), o.prototype.writeIntLE = function(l, d, c, a) {
      if (l = +l, d = d >>> 0, !a) {
        const M = Math.pow(2, 8 * c - 1);
        le(this, l, d, c, M - 1, -M);
      }
      let h = 0, g = 1, F = 0;
      for (this[d] = l & 255; ++h < c && (g *= 256); )
        l < 0 && F === 0 && this[d + h - 1] !== 0 && (F = 1), this[d + h] = (l / g >> 0) - F & 255;
      return d + c;
    }, o.prototype.writeIntBE = function(l, d, c, a) {
      if (l = +l, d = d >>> 0, !a) {
        const M = Math.pow(2, 8 * c - 1);
        le(this, l, d, c, M - 1, -M);
      }
      let h = c - 1, g = 1, F = 0;
      for (this[d + h] = l & 255; --h >= 0 && (g *= 256); )
        l < 0 && F === 0 && this[d + h + 1] !== 0 && (F = 1), this[d + h] = (l / g >> 0) - F & 255;
      return d + c;
    }, o.prototype.writeInt8 = function(l, d, c) {
      return l = +l, d = d >>> 0, c || le(this, l, d, 1, 127, -128), l < 0 && (l = 255 + l + 1), this[d] = l & 255, d + 1;
    }, o.prototype.writeInt16LE = function(l, d, c) {
      return l = +l, d = d >>> 0, c || le(this, l, d, 2, 32767, -32768), this[d] = l & 255, this[d + 1] = l >>> 8, d + 2;
    }, o.prototype.writeInt16BE = function(l, d, c) {
      return l = +l, d = d >>> 0, c || le(this, l, d, 2, 32767, -32768), this[d] = l >>> 8, this[d + 1] = l & 255, d + 2;
    }, o.prototype.writeInt32LE = function(l, d, c) {
      return l = +l, d = d >>> 0, c || le(this, l, d, 4, **********, -2147483648), this[d] = l & 255, this[d + 1] = l >>> 8, this[d + 2] = l >>> 16, this[d + 3] = l >>> 24, d + 4;
    }, o.prototype.writeInt32BE = function(l, d, c) {
      return l = +l, d = d >>> 0, c || le(this, l, d, 4, **********, -2147483648), l < 0 && (l = 4294967295 + l + 1), this[d] = l >>> 24, this[d + 1] = l >>> 16, this[d + 2] = l >>> 8, this[d + 3] = l & 255, d + 4;
    }, o.prototype.writeBigInt64LE = Fe(function(l, d = 0) {
      return Ee(this, l, d, -BigInt("0x8000000000000000"), BigInt("0x7fffffffffffffff"));
    }), o.prototype.writeBigInt64BE = Fe(function(l, d = 0) {
      return he(this, l, d, -BigInt("0x8000000000000000"), BigInt("0x7fffffffffffffff"));
    });
    function fe(_, l, d, c, a, h) {
      if (d + c > _.length) throw new RangeError("Index out of range");
      if (d < 0) throw new RangeError("Index out of range");
    }
    function L(_, l, d, c, a) {
      return l = +l, d = d >>> 0, a || fe(_, l, d, 4), t.write(_, l, d, c, 23, 4), d + 4;
    }
    o.prototype.writeFloatLE = function(l, d, c) {
      return L(this, l, d, !0, c);
    }, o.prototype.writeFloatBE = function(l, d, c) {
      return L(this, l, d, !1, c);
    };
    function P(_, l, d, c, a) {
      return l = +l, d = d >>> 0, a || fe(_, l, d, 8), t.write(_, l, d, c, 52, 8), d + 8;
    }
    o.prototype.writeDoubleLE = function(l, d, c) {
      return P(this, l, d, !0, c);
    }, o.prototype.writeDoubleBE = function(l, d, c) {
      return P(this, l, d, !1, c);
    }, o.prototype.copy = function(l, d, c, a) {
      if (!o.isBuffer(l)) throw new TypeError("argument should be a Buffer");
      if (c || (c = 0), !a && a !== 0 && (a = this.length), d >= l.length && (d = l.length), d || (d = 0), a > 0 && a < c && (a = c), a === c || l.length === 0 || this.length === 0) return 0;
      if (d < 0)
        throw new RangeError("targetStart out of bounds");
      if (c < 0 || c >= this.length) throw new RangeError("Index out of range");
      if (a < 0) throw new RangeError("sourceEnd out of bounds");
      a > this.length && (a = this.length), l.length - d < a - c && (a = l.length - d + c);
      const h = a - c;
      return this === l && typeof Uint8Array.prototype.copyWithin == "function" ? this.copyWithin(d, c, a) : Uint8Array.prototype.set.call(
        l,
        this.subarray(c, a),
        d
      ), h;
    }, o.prototype.fill = function(l, d, c, a) {
      if (typeof l == "string") {
        if (typeof d == "string" ? (a = d, d = 0, c = this.length) : typeof c == "string" && (a = c, c = this.length), a !== void 0 && typeof a != "string")
          throw new TypeError("encoding must be a string");
        if (typeof a == "string" && !o.isEncoding(a))
          throw new TypeError("Unknown encoding: " + a);
        if (l.length === 1) {
          const g = l.charCodeAt(0);
          (a === "utf8" && g < 128 || a === "latin1") && (l = g);
        }
      } else typeof l == "number" ? l = l & 255 : typeof l == "boolean" && (l = Number(l));
      if (d < 0 || this.length < d || this.length < c)
        throw new RangeError("Out of range index");
      if (c <= d)
        return this;
      d = d >>> 0, c = c === void 0 ? this.length : c >>> 0, l || (l = 0);
      let h;
      if (typeof l == "number")
        for (h = d; h < c; ++h)
          this[h] = l;
      else {
        const g = o.isBuffer(l) ? l : o.from(l, a), F = g.length;
        if (F === 0)
          throw new TypeError('The value "' + l + '" is invalid for argument "value"');
        for (h = 0; h < c - d; ++h)
          this[h + d] = g[h % F];
      }
      return this;
    };
    const V = {};
    function z(_, l, d) {
      V[_] = class extends d {
        constructor() {
          super(), Object.defineProperty(this, "message", {
            value: l.apply(this, arguments),
            writable: !0,
            configurable: !0
          }), this.name = `${this.name} [${_}]`, this.stack, delete this.name;
        }
        get code() {
          return _;
        }
        set code(a) {
          Object.defineProperty(this, "code", {
            configurable: !0,
            enumerable: !0,
            value: a,
            writable: !0
          });
        }
        toString() {
          return `${this.name} [${_}]: ${this.message}`;
        }
      };
    }
    z(
      "ERR_BUFFER_OUT_OF_BOUNDS",
      function(_) {
        return _ ? `${_} is outside of buffer bounds` : "Attempt to access memory outside buffer bounds";
      },
      RangeError
    ), z(
      "ERR_INVALID_ARG_TYPE",
      function(_, l) {
        return `The "${_}" argument must be of type number. Received type ${typeof l}`;
      },
      TypeError
    ), z(
      "ERR_OUT_OF_RANGE",
      function(_, l, d) {
        let c = `The value of "${_}" is out of range.`, a = d;
        return Number.isInteger(d) && Math.abs(d) > 2 ** 32 ? a = Q(String(d)) : typeof d == "bigint" && (a = String(d), (d > BigInt(2) ** BigInt(32) || d < -(BigInt(2) ** BigInt(32))) && (a = Q(a)), a += "n"), c += ` It must be ${l}. Received ${a}`, c;
      },
      RangeError
    );
    function Q(_) {
      let l = "", d = _.length;
      const c = _[0] === "-" ? 1 : 0;
      for (; d >= c + 4; d -= 3)
        l = `_${_.slice(d - 3, d)}${l}`;
      return `${_.slice(0, d)}${l}`;
    }
    function I(_, l, d) {
      K(l, "offset"), (_[l] === void 0 || _[l + d] === void 0) && $(l, _.length - (d + 1));
    }
    function C(_, l, d, c, a, h) {
      if (_ > d || _ < l) {
        const g = typeof l == "bigint" ? "n" : "";
        let F;
        throw l === 0 || l === BigInt(0) ? F = `>= 0${g} and < 2${g} ** ${(h + 1) * 8}${g}` : F = `>= -(2${g} ** ${(h + 1) * 8 - 1}${g}) and < 2 ** ${(h + 1) * 8 - 1}${g}`, new V.ERR_OUT_OF_RANGE("value", F, _);
      }
      I(c, a, h);
    }
    function K(_, l) {
      if (typeof _ != "number")
        throw new V.ERR_INVALID_ARG_TYPE(l, "number", _);
    }
    function $(_, l, d) {
      throw Math.floor(_) !== _ ? (K(_, d), new V.ERR_OUT_OF_RANGE("offset", "an integer", _)) : l < 0 ? new V.ERR_BUFFER_OUT_OF_BOUNDS() : new V.ERR_OUT_OF_RANGE(
        "offset",
        `>= 0 and <= ${l}`,
        _
      );
    }
    const we = /[^+/0-9A-Za-z-_]/g;
    function ne(_) {
      if (_ = _.split("=")[0], _ = _.trim().replace(we, ""), _.length < 2) return "";
      for (; _.length % 4 !== 0; )
        _ = _ + "=";
      return _;
    }
    function ce(_, l) {
      l = l || 1 / 0;
      let d;
      const c = _.length;
      let a = null;
      const h = [];
      for (let g = 0; g < c; ++g) {
        if (d = _.charCodeAt(g), d > 55295 && d < 57344) {
          if (!a) {
            if (d > 56319) {
              (l -= 3) > -1 && h.push(239, 191, 189);
              continue;
            } else if (g + 1 === c) {
              (l -= 3) > -1 && h.push(239, 191, 189);
              continue;
            }
            a = d;
            continue;
          }
          if (d < 56320) {
            (l -= 3) > -1 && h.push(239, 191, 189), a = d;
            continue;
          }
          d = (a - 55296 << 10 | d - 56320) + 65536;
        } else a && (l -= 3) > -1 && h.push(239, 191, 189);
        if (a = null, d < 128) {
          if ((l -= 1) < 0) break;
          h.push(d);
        } else if (d < 2048) {
          if ((l -= 2) < 0) break;
          h.push(
            d >> 6 | 192,
            d & 63 | 128
          );
        } else if (d < 65536) {
          if ((l -= 3) < 0) break;
          h.push(
            d >> 12 | 224,
            d >> 6 & 63 | 128,
            d & 63 | 128
          );
        } else if (d < 1114112) {
          if ((l -= 4) < 0) break;
          h.push(
            d >> 18 | 240,
            d >> 12 & 63 | 128,
            d >> 6 & 63 | 128,
            d & 63 | 128
          );
        } else
          throw new Error("Invalid code point");
      }
      return h;
    }
    function Qe(_) {
      const l = [];
      for (let d = 0; d < _.length; ++d)
        l.push(_.charCodeAt(d) & 255);
      return l;
    }
    function mt(_, l) {
      let d, c, a;
      const h = [];
      for (let g = 0; g < _.length && !((l -= 2) < 0); ++g)
        d = _.charCodeAt(g), c = d >> 8, a = d % 256, h.push(a), h.push(c);
      return h;
    }
    function st(_) {
      return e.toByteArray(ne(_));
    }
    function Ue(_, l, d, c) {
      let a;
      for (a = 0; a < c && !(a + d >= l.length || a >= _.length); ++a)
        l[a + d] = _[a];
      return a;
    }
    function Ce(_, l) {
      return _ instanceof l || _ != null && _.constructor != null && _.constructor.name != null && _.constructor.name === l.name;
    }
    function et(_) {
      return _ !== _;
    }
    const at = function() {
      const _ = "0123456789abcdef", l = new Array(256);
      for (let d = 0; d < 16; ++d) {
        const c = d * 16;
        for (let a = 0; a < 16; ++a)
          l[c + a] = _[d] + _[a];
      }
      return l;
    }();
    function Fe(_) {
      return typeof BigInt > "u" ? dt : _;
    }
    function dt() {
      throw new Error("BigInt not supported");
    }
  }(si)), si;
}
/*! safe-buffer. MIT License. Feross Aboukhadijeh <https://feross.org/opensource> */
var Kn;
function ke() {
  return Kn || (Kn = 1, function(i, e) {
    var t = xr(), r = t.Buffer;
    function s(u, o) {
      for (var p in u)
        o[p] = u[p];
    }
    r.from && r.alloc && r.allocUnsafe && r.allocUnsafeSlow ? i.exports = t : (s(t, e), e.Buffer = n);
    function n(u, o, p) {
      return r(u, o, p);
    }
    n.prototype = Object.create(r.prototype), s(r, n), n.from = function(u, o, p) {
      if (typeof u == "number")
        throw new TypeError("Argument must not be a number");
      return r(u, o, p);
    }, n.alloc = function(u, o, p) {
      if (typeof u != "number")
        throw new TypeError("Argument must be a number");
      var y = r(u);
      return o !== void 0 ? typeof p == "string" ? y.fill(o, p) : y.fill(o) : y.fill(0), y;
    }, n.allocUnsafe = function(u) {
      if (typeof u != "number")
        throw new TypeError("Argument must be a number");
      return r(u);
    }, n.allocUnsafeSlow = function(u) {
      if (typeof u != "number")
        throw new TypeError("Argument must be a number");
      return t.SlowBuffer(u);
    };
  }(Ir, Ir.exports)), Ir.exports;
}
var Wn;
function $o() {
  if (Wn) return ni;
  Wn = 1;
  var i = ke().Buffer, e = br();
  function t(r, s, n) {
    var u = s.length, o = e(s, r._cache);
    return r._cache = r._cache.slice(u), r._prev = i.concat([r._prev, n ? s : o]), o;
  }
  return ni.encrypt = function(r, s, n) {
    for (var u = i.allocUnsafe(0), o; s.length; )
      if (r._cache.length === 0 && (r._cache = r._cipher.encryptBlock(r._prev), r._prev = i.allocUnsafe(0)), r._cache.length <= s.length)
        o = r._cache.length, u = i.concat([u, t(r, s.slice(0, o), n)]), s = s.slice(o);
      else {
        u = i.concat([u, t(r, s, n)]);
        break;
      }
    return u;
  }, ni;
}
var ai = {}, $n;
function Xo() {
  if ($n) return ai;
  $n = 1;
  var i = ke().Buffer;
  function e(t, r, s) {
    var n = t._cipher.encryptBlock(t._prev), u = n[0] ^ r;
    return t._prev = i.concat([
      t._prev.slice(1),
      i.from([s ? r : u])
    ]), u;
  }
  return ai.encrypt = function(t, r, s) {
    for (var n = r.length, u = i.allocUnsafe(n), o = -1; ++o < n; )
      u[o] = e(t, r[o], s);
    return u;
  }, ai;
}
var oi = {}, Xn;
function zo() {
  if (Xn) return oi;
  Xn = 1;
  var i = ke().Buffer;
  function e(r, s, n) {
    for (var u, o = -1, p = 8, y = 0, f, v; ++o < p; )
      u = r._cipher.encryptBlock(r._prev), f = s & 1 << 7 - o ? 128 : 0, v = u[0] ^ f, y += (v & 128) >> o % 8, r._prev = t(r._prev, n ? f : v);
    return y;
  }
  function t(r, s) {
    var n = r.length, u = -1, o = i.allocUnsafe(r.length);
    for (r = i.concat([r, i.from([s])]); ++u < n; )
      o[u] = r[u] << 1 | r[u + 1] >> 7;
    return o;
  }
  return oi.encrypt = function(r, s, n) {
    for (var u = s.length, o = i.allocUnsafe(u), p = -1; ++p < u; )
      o[p] = e(r, s[p], n);
    return o;
  }, oi;
}
var hi = {}, zn;
function Jo() {
  if (zn) return hi;
  zn = 1;
  var i = br();
  function e(t) {
    return t._prev = t._cipher.encryptBlock(t._prev), t._prev;
  }
  return hi.encrypt = function(t, r) {
    for (; t._cache.length < r.length; )
      t._cache = qe.concat([t._cache, e(t)]);
    var s = t._cache.slice(0, r.length);
    return t._cache = t._cache.slice(r.length), i(r, s);
  }, hi;
}
var ui = {}, fi, Jn;
function ua() {
  if (Jn) return fi;
  Jn = 1;
  function i(e) {
    for (var t = e.length, r; t--; )
      if (r = e.readUInt8(t), r === 255)
        e.writeUInt8(0, t);
      else {
        r++, e.writeUInt8(r, t);
        break;
      }
  }
  return fi = i, fi;
}
var Yn;
function Zn() {
  if (Yn) return ui;
  Yn = 1;
  var i = br(), e = ke().Buffer, t = ua();
  function r(n) {
    var u = n._cipher.encryptBlockRaw(n._prev);
    return t(n._prev), u;
  }
  var s = 16;
  return ui.encrypt = function(n, u) {
    var o = Math.ceil(u.length / s), p = n._cache.length;
    n._cache = e.concat([
      n._cache,
      e.allocUnsafe(o * s)
    ]);
    for (var y = 0; y < o; y++) {
      var f = r(n), v = p + y * s;
      n._cache.writeUInt32BE(f[0], v + 0), n._cache.writeUInt32BE(f[1], v + 4), n._cache.writeUInt32BE(f[2], v + 8), n._cache.writeUInt32BE(f[3], v + 12);
    }
    var m = n._cache.slice(0, u.length);
    return n._cache = n._cache.slice(u.length), i(u, m);
  }, ui;
}
const Yo = { cipher: "AES", key: 128, iv: 16, mode: "CBC", type: "block" }, Zo = { cipher: "AES", key: 192, iv: 16, mode: "CBC", type: "block" }, Qo = { cipher: "AES", key: 256, iv: 16, mode: "CBC", type: "block" }, fa = {
  "aes-128-ecb": { cipher: "AES", key: 128, iv: 0, mode: "ECB", type: "block" },
  "aes-192-ecb": { cipher: "AES", key: 192, iv: 0, mode: "ECB", type: "block" },
  "aes-256-ecb": { cipher: "AES", key: 256, iv: 0, mode: "ECB", type: "block" },
  "aes-128-cbc": { cipher: "AES", key: 128, iv: 16, mode: "CBC", type: "block" },
  "aes-192-cbc": { cipher: "AES", key: 192, iv: 16, mode: "CBC", type: "block" },
  "aes-256-cbc": { cipher: "AES", key: 256, iv: 16, mode: "CBC", type: "block" },
  aes128: Yo,
  aes192: Zo,
  aes256: Qo,
  "aes-128-cfb": { cipher: "AES", key: 128, iv: 16, mode: "CFB", type: "stream" },
  "aes-192-cfb": { cipher: "AES", key: 192, iv: 16, mode: "CFB", type: "stream" },
  "aes-256-cfb": { cipher: "AES", key: 256, iv: 16, mode: "CFB", type: "stream" },
  "aes-128-cfb8": { cipher: "AES", key: 128, iv: 16, mode: "CFB8", type: "stream" },
  "aes-192-cfb8": { cipher: "AES", key: 192, iv: 16, mode: "CFB8", type: "stream" },
  "aes-256-cfb8": { cipher: "AES", key: 256, iv: 16, mode: "CFB8", type: "stream" },
  "aes-128-cfb1": { cipher: "AES", key: 128, iv: 16, mode: "CFB1", type: "stream" },
  "aes-192-cfb1": { cipher: "AES", key: 192, iv: 16, mode: "CFB1", type: "stream" },
  "aes-256-cfb1": { cipher: "AES", key: 256, iv: 16, mode: "CFB1", type: "stream" },
  "aes-128-ofb": { cipher: "AES", key: 128, iv: 16, mode: "OFB", type: "stream" },
  "aes-192-ofb": { cipher: "AES", key: 192, iv: 16, mode: "OFB", type: "stream" },
  "aes-256-ofb": { cipher: "AES", key: 256, iv: 16, mode: "OFB", type: "stream" },
  "aes-128-ctr": { cipher: "AES", key: 128, iv: 16, mode: "CTR", type: "stream" },
  "aes-192-ctr": { cipher: "AES", key: 192, iv: 16, mode: "CTR", type: "stream" },
  "aes-256-ctr": { cipher: "AES", key: 256, iv: 16, mode: "CTR", type: "stream" },
  "aes-128-gcm": { cipher: "AES", key: 128, iv: 12, mode: "GCM", type: "auth" },
  "aes-192-gcm": { cipher: "AES", key: 192, iv: 12, mode: "GCM", type: "auth" },
  "aes-256-gcm": { cipher: "AES", key: 256, iv: 12, mode: "GCM", type: "auth" }
};
var ci, Qn;
function ca() {
  if (Qn) return ci;
  Qn = 1;
  var i = {
    ECB: Vo(),
    CBC: Go(),
    CFB: $o(),
    CFB8: Xo(),
    CFB1: zo(),
    OFB: Jo(),
    CTR: Zn(),
    GCM: Zn()
  }, e = fa;
  for (var t in e)
    e[t].module = i[e[t].mode];
  return ci = e, ci;
}
var li = {}, es;
function $r() {
  if (es) return li;
  es = 1;
  var i = ke().Buffer;
  function e(o) {
    i.isBuffer(o) || (o = i.from(o));
    for (var p = o.length / 4 | 0, y = new Array(p), f = 0; f < p; f++)
      y[f] = o.readUInt32BE(f * 4);
    return y;
  }
  function t(o) {
    for (var p = 0; p < o.length; o++)
      o[p] = 0;
  }
  function r(o, p, y, f, v) {
    for (var m = y[0], w = y[1], b = y[2], x = y[3], S = o[0] ^ p[0], E = o[1] ^ p[1], B = o[2] ^ p[2], A = o[3] ^ p[3], k, T, D, q, R = 4, G = 1; G < v; G++)
      k = m[S >>> 24] ^ w[E >>> 16 & 255] ^ b[B >>> 8 & 255] ^ x[A & 255] ^ p[R++], T = m[E >>> 24] ^ w[B >>> 16 & 255] ^ b[A >>> 8 & 255] ^ x[S & 255] ^ p[R++], D = m[B >>> 24] ^ w[A >>> 16 & 255] ^ b[S >>> 8 & 255] ^ x[E & 255] ^ p[R++], q = m[A >>> 24] ^ w[S >>> 16 & 255] ^ b[E >>> 8 & 255] ^ x[B & 255] ^ p[R++], S = k, E = T, B = D, A = q;
    return k = (f[S >>> 24] << 24 | f[E >>> 16 & 255] << 16 | f[B >>> 8 & 255] << 8 | f[A & 255]) ^ p[R++], T = (f[E >>> 24] << 24 | f[B >>> 16 & 255] << 16 | f[A >>> 8 & 255] << 8 | f[S & 255]) ^ p[R++], D = (f[B >>> 24] << 24 | f[A >>> 16 & 255] << 16 | f[S >>> 8 & 255] << 8 | f[E & 255]) ^ p[R++], q = (f[A >>> 24] << 24 | f[S >>> 16 & 255] << 16 | f[E >>> 8 & 255] << 8 | f[B & 255]) ^ p[R++], k = k >>> 0, T = T >>> 0, D = D >>> 0, q = q >>> 0, [k, T, D, q];
  }
  var s = [0, 1, 2, 4, 8, 16, 32, 64, 128, 27, 54], n = function() {
    for (var o = new Array(256), p = 0; p < 256; p++)
      p < 128 ? o[p] = p << 1 : o[p] = p << 1 ^ 283;
    for (var y = [], f = [], v = [[], [], [], []], m = [[], [], [], []], w = 0, b = 0, x = 0; x < 256; ++x) {
      var S = b ^ b << 1 ^ b << 2 ^ b << 3 ^ b << 4;
      S = S >>> 8 ^ S & 255 ^ 99, y[w] = S, f[S] = w;
      var E = o[w], B = o[E], A = o[B], k = o[S] * 257 ^ S * 16843008;
      v[0][w] = k << 24 | k >>> 8, v[1][w] = k << 16 | k >>> 16, v[2][w] = k << 8 | k >>> 24, v[3][w] = k, k = A * 16843009 ^ B * 65537 ^ E * 257 ^ w * 16843008, m[0][S] = k << 24 | k >>> 8, m[1][S] = k << 16 | k >>> 16, m[2][S] = k << 8 | k >>> 24, m[3][S] = k, w === 0 ? w = b = 1 : (w = E ^ o[o[o[A ^ E]]], b ^= o[o[b]]);
    }
    return {
      SBOX: y,
      INV_SBOX: f,
      SUB_MIX: v,
      INV_SUB_MIX: m
    };
  }();
  function u(o) {
    this._key = e(o), this._reset();
  }
  return u.blockSize = 4 * 4, u.keySize = 256 / 8, u.prototype.blockSize = u.blockSize, u.prototype.keySize = u.keySize, u.prototype._reset = function() {
    for (var o = this._key, p = o.length, y = p + 6, f = (y + 1) * 4, v = [], m = 0; m < p; m++)
      v[m] = o[m];
    for (m = p; m < f; m++) {
      var w = v[m - 1];
      m % p === 0 ? (w = w << 8 | w >>> 24, w = n.SBOX[w >>> 24] << 24 | n.SBOX[w >>> 16 & 255] << 16 | n.SBOX[w >>> 8 & 255] << 8 | n.SBOX[w & 255], w ^= s[m / p | 0] << 24) : p > 6 && m % p === 4 && (w = n.SBOX[w >>> 24] << 24 | n.SBOX[w >>> 16 & 255] << 16 | n.SBOX[w >>> 8 & 255] << 8 | n.SBOX[w & 255]), v[m] = v[m - p] ^ w;
    }
    for (var b = [], x = 0; x < f; x++) {
      var S = f - x, E = v[S - (x % 4 ? 0 : 4)];
      x < 4 || S <= 4 ? b[x] = E : b[x] = n.INV_SUB_MIX[0][n.SBOX[E >>> 24]] ^ n.INV_SUB_MIX[1][n.SBOX[E >>> 16 & 255]] ^ n.INV_SUB_MIX[2][n.SBOX[E >>> 8 & 255]] ^ n.INV_SUB_MIX[3][n.SBOX[E & 255]];
    }
    this._nRounds = y, this._keySchedule = v, this._invKeySchedule = b;
  }, u.prototype.encryptBlockRaw = function(o) {
    return o = e(o), r(o, this._keySchedule, n.SUB_MIX, n.SBOX, this._nRounds);
  }, u.prototype.encryptBlock = function(o) {
    var p = this.encryptBlockRaw(o), y = i.allocUnsafe(16);
    return y.writeUInt32BE(p[0], 0), y.writeUInt32BE(p[1], 4), y.writeUInt32BE(p[2], 8), y.writeUInt32BE(p[3], 12), y;
  }, u.prototype.decryptBlock = function(o) {
    o = e(o);
    var p = o[1];
    o[1] = o[3], o[3] = p;
    var y = r(o, this._invKeySchedule, n.INV_SUB_MIX, n.INV_SBOX, this._nRounds), f = i.allocUnsafe(16);
    return f.writeUInt32BE(y[0], 0), f.writeUInt32BE(y[3], 4), f.writeUInt32BE(y[2], 8), f.writeUInt32BE(y[1], 12), f;
  }, u.prototype.scrub = function() {
    t(this._keySchedule), t(this._invKeySchedule), t(this._key);
  }, li.AES = u, li;
}
var Ar = { exports: {} }, ts;
function bn() {
  if (ts) return Ar.exports;
  ts = 1;
  var i = typeof Reflect == "object" ? Reflect : null, e = i && typeof i.apply == "function" ? i.apply : function(A, k, T) {
    return Function.prototype.apply.call(A, k, T);
  }, t;
  i && typeof i.ownKeys == "function" ? t = i.ownKeys : Object.getOwnPropertySymbols ? t = function(A) {
    return Object.getOwnPropertyNames(A).concat(Object.getOwnPropertySymbols(A));
  } : t = function(A) {
    return Object.getOwnPropertyNames(A);
  };
  function r(B) {
    console && console.warn && console.warn(B);
  }
  var s = Number.isNaN || function(A) {
    return A !== A;
  };
  function n() {
    n.init.call(this);
  }
  Ar.exports = n, Ar.exports.once = E, n.EventEmitter = n, n.prototype._events = void 0, n.prototype._eventsCount = 0, n.prototype._maxListeners = void 0;
  var u = 10;
  function o(B) {
    if (typeof B != "function")
      throw new TypeError('The "listener" argument must be of type Function. Received type ' + typeof B);
  }
  Object.defineProperty(n, "defaultMaxListeners", {
    enumerable: !0,
    get: function() {
      return u;
    },
    set: function(B) {
      if (typeof B != "number" || B < 0 || s(B))
        throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received ' + B + ".");
      u = B;
    }
  }), n.init = function() {
    (this._events === void 0 || this._events === Object.getPrototypeOf(this)._events) && (this._events = /* @__PURE__ */ Object.create(null), this._eventsCount = 0), this._maxListeners = this._maxListeners || void 0;
  }, n.prototype.setMaxListeners = function(A) {
    if (typeof A != "number" || A < 0 || s(A))
      throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received ' + A + ".");
    return this._maxListeners = A, this;
  };
  function p(B) {
    return B._maxListeners === void 0 ? n.defaultMaxListeners : B._maxListeners;
  }
  n.prototype.getMaxListeners = function() {
    return p(this);
  }, n.prototype.emit = function(A) {
    for (var k = [], T = 1; T < arguments.length; T++) k.push(arguments[T]);
    var D = A === "error", q = this._events;
    if (q !== void 0)
      D = D && q.error === void 0;
    else if (!D)
      return !1;
    if (D) {
      var R;
      if (k.length > 0 && (R = k[0]), R instanceof Error)
        throw R;
      var G = new Error("Unhandled error." + (R ? " (" + R.message + ")" : ""));
      throw G.context = R, G;
    }
    var J = q[A];
    if (J === void 0)
      return !1;
    if (typeof J == "function")
      e(J, this, k);
    else
      for (var se = J.length, Z = b(J, se), T = 0; T < se; ++T)
        e(Z[T], this, k);
    return !0;
  };
  function y(B, A, k, T) {
    var D, q, R;
    if (o(k), q = B._events, q === void 0 ? (q = B._events = /* @__PURE__ */ Object.create(null), B._eventsCount = 0) : (q.newListener !== void 0 && (B.emit(
      "newListener",
      A,
      k.listener ? k.listener : k
    ), q = B._events), R = q[A]), R === void 0)
      R = q[A] = k, ++B._eventsCount;
    else if (typeof R == "function" ? R = q[A] = T ? [k, R] : [R, k] : T ? R.unshift(k) : R.push(k), D = p(B), D > 0 && R.length > D && !R.warned) {
      R.warned = !0;
      var G = new Error("Possible EventEmitter memory leak detected. " + R.length + " " + String(A) + " listeners added. Use emitter.setMaxListeners() to increase limit");
      G.name = "MaxListenersExceededWarning", G.emitter = B, G.type = A, G.count = R.length, r(G);
    }
    return B;
  }
  n.prototype.addListener = function(A, k) {
    return y(this, A, k, !1);
  }, n.prototype.on = n.prototype.addListener, n.prototype.prependListener = function(A, k) {
    return y(this, A, k, !0);
  };
  function f() {
    if (!this.fired)
      return this.target.removeListener(this.type, this.wrapFn), this.fired = !0, arguments.length === 0 ? this.listener.call(this.target) : this.listener.apply(this.target, arguments);
  }
  function v(B, A, k) {
    var T = { fired: !1, wrapFn: void 0, target: B, type: A, listener: k }, D = f.bind(T);
    return D.listener = k, T.wrapFn = D, D;
  }
  n.prototype.once = function(A, k) {
    return o(k), this.on(A, v(this, A, k)), this;
  }, n.prototype.prependOnceListener = function(A, k) {
    return o(k), this.prependListener(A, v(this, A, k)), this;
  }, n.prototype.removeListener = function(A, k) {
    var T, D, q, R, G;
    if (o(k), D = this._events, D === void 0)
      return this;
    if (T = D[A], T === void 0)
      return this;
    if (T === k || T.listener === k)
      --this._eventsCount === 0 ? this._events = /* @__PURE__ */ Object.create(null) : (delete D[A], D.removeListener && this.emit("removeListener", A, T.listener || k));
    else if (typeof T != "function") {
      for (q = -1, R = T.length - 1; R >= 0; R--)
        if (T[R] === k || T[R].listener === k) {
          G = T[R].listener, q = R;
          break;
        }
      if (q < 0)
        return this;
      q === 0 ? T.shift() : x(T, q), T.length === 1 && (D[A] = T[0]), D.removeListener !== void 0 && this.emit("removeListener", A, G || k);
    }
    return this;
  }, n.prototype.off = n.prototype.removeListener, n.prototype.removeAllListeners = function(A) {
    var k, T, D;
    if (T = this._events, T === void 0)
      return this;
    if (T.removeListener === void 0)
      return arguments.length === 0 ? (this._events = /* @__PURE__ */ Object.create(null), this._eventsCount = 0) : T[A] !== void 0 && (--this._eventsCount === 0 ? this._events = /* @__PURE__ */ Object.create(null) : delete T[A]), this;
    if (arguments.length === 0) {
      var q = Object.keys(T), R;
      for (D = 0; D < q.length; ++D)
        R = q[D], R !== "removeListener" && this.removeAllListeners(R);
      return this.removeAllListeners("removeListener"), this._events = /* @__PURE__ */ Object.create(null), this._eventsCount = 0, this;
    }
    if (k = T[A], typeof k == "function")
      this.removeListener(A, k);
    else if (k !== void 0)
      for (D = k.length - 1; D >= 0; D--)
        this.removeListener(A, k[D]);
    return this;
  };
  function m(B, A, k) {
    var T = B._events;
    if (T === void 0)
      return [];
    var D = T[A];
    return D === void 0 ? [] : typeof D == "function" ? k ? [D.listener || D] : [D] : k ? S(D) : b(D, D.length);
  }
  n.prototype.listeners = function(A) {
    return m(this, A, !0);
  }, n.prototype.rawListeners = function(A) {
    return m(this, A, !1);
  }, n.listenerCount = function(B, A) {
    return typeof B.listenerCount == "function" ? B.listenerCount(A) : w.call(B, A);
  }, n.prototype.listenerCount = w;
  function w(B) {
    var A = this._events;
    if (A !== void 0) {
      var k = A[B];
      if (typeof k == "function")
        return 1;
      if (k !== void 0)
        return k.length;
    }
    return 0;
  }
  n.prototype.eventNames = function() {
    return this._eventsCount > 0 ? t(this._events) : [];
  };
  function b(B, A) {
    for (var k = new Array(A), T = 0; T < A; ++T)
      k[T] = B[T];
    return k;
  }
  function x(B, A) {
    for (; A + 1 < B.length; A++)
      B[A] = B[A + 1];
    B.pop();
  }
  function S(B) {
    for (var A = new Array(B.length), k = 0; k < A.length; ++k)
      A[k] = B[k].listener || B[k];
    return A;
  }
  function E(B, A) {
    return new Promise(function(k, T) {
      function D() {
        q !== void 0 && B.removeListener("error", q), k([].slice.call(arguments));
      }
      var q;
      A !== "error" && (q = function(G) {
        B.removeListener(A, D), T(G);
      }, B.once("error", q)), B.once(A, D);
    });
  }
  return Ar.exports;
}
var kr = { exports: {} }, rs;
function Te() {
  return rs || (rs = 1, typeof Object.create == "function" ? kr.exports = function(e, t) {
    t && (e.super_ = t, e.prototype = Object.create(t.prototype, {
      constructor: {
        value: e,
        enumerable: !1,
        writable: !0,
        configurable: !0
      }
    }));
  } : kr.exports = function(e, t) {
    if (t) {
      e.super_ = t;
      var r = function() {
      };
      r.prototype = t.prototype, e.prototype = new r(), e.prototype.constructor = e;
    }
  }), kr.exports;
}
function eh(i) {
  return i && i.__esModule && Object.prototype.hasOwnProperty.call(i, "default") ? i.default : i;
}
var la = { exports: {} }, Pe = la.exports = {}, _t, bt;
function an() {
  throw new Error("setTimeout has not been defined");
}
function on() {
  throw new Error("clearTimeout has not been defined");
}
(function() {
  try {
    typeof setTimeout == "function" ? _t = setTimeout : _t = an;
  } catch {
    _t = an;
  }
  try {
    typeof clearTimeout == "function" ? bt = clearTimeout : bt = on;
  } catch {
    bt = on;
  }
})();
function da(i) {
  if (_t === setTimeout)
    return setTimeout(i, 0);
  if ((_t === an || !_t) && setTimeout)
    return _t = setTimeout, setTimeout(i, 0);
  try {
    return _t(i, 0);
  } catch {
    try {
      return _t.call(null, i, 0);
    } catch {
      return _t.call(this, i, 0);
    }
  }
}
function th(i) {
  if (bt === clearTimeout)
    return clearTimeout(i);
  if ((bt === on || !bt) && clearTimeout)
    return bt = clearTimeout, clearTimeout(i);
  try {
    return bt(i);
  } catch {
    try {
      return bt.call(null, i);
    } catch {
      return bt.call(this, i);
    }
  }
}
var kt = [], er = !1, Ht, Pr = -1;
function rh() {
  !er || !Ht || (er = !1, Ht.length ? kt = Ht.concat(kt) : Pr = -1, kt.length && pa());
}
function pa() {
  if (!er) {
    var i = da(rh);
    er = !0;
    for (var e = kt.length; e; ) {
      for (Ht = kt, kt = []; ++Pr < e; )
        Ht && Ht[Pr].run();
      Pr = -1, e = kt.length;
    }
    Ht = null, er = !1, th(i);
  }
}
Pe.nextTick = function(i) {
  var e = new Array(arguments.length - 1);
  if (arguments.length > 1)
    for (var t = 1; t < arguments.length; t++)
      e[t - 1] = arguments[t];
  kt.push(new ya(i, e)), kt.length === 1 && !er && da(pa);
};
function ya(i, e) {
  this.fun = i, this.array = e;
}
ya.prototype.run = function() {
  this.fun.apply(null, this.array);
};
Pe.title = "browser";
Pe.browser = !0;
Pe.env = {};
Pe.argv = [];
Pe.version = "";
Pe.versions = {};
function Ft() {
}
Pe.on = Ft;
Pe.addListener = Ft;
Pe.once = Ft;
Pe.off = Ft;
Pe.removeListener = Ft;
Pe.removeAllListeners = Ft;
Pe.emit = Ft;
Pe.prependListener = Ft;
Pe.prependOnceListener = Ft;
Pe.listeners = function(i) {
  return [];
};
Pe.binding = function(i) {
  throw new Error("process.binding is not supported");
};
Pe.cwd = function() {
  return "/";
};
Pe.chdir = function(i) {
  throw new Error("process.chdir is not supported");
};
Pe.umask = function() {
  return 0;
};
var ih = la.exports;
const xe = /* @__PURE__ */ eh(ih);
var di, is;
function ga() {
  return is || (is = 1, di = bn().EventEmitter), di;
}
const nh = {}, sh = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: nh
}, Symbol.toStringTag, { value: "Module" })), wa = /* @__PURE__ */ qo(sh);
var pi, ns;
function ah() {
  if (ns) return pi;
  ns = 1;
  function i(b, x) {
    var S = Object.keys(b);
    if (Object.getOwnPropertySymbols) {
      var E = Object.getOwnPropertySymbols(b);
      x && (E = E.filter(function(B) {
        return Object.getOwnPropertyDescriptor(b, B).enumerable;
      })), S.push.apply(S, E);
    }
    return S;
  }
  function e(b) {
    for (var x = 1; x < arguments.length; x++) {
      var S = arguments[x] != null ? arguments[x] : {};
      x % 2 ? i(Object(S), !0).forEach(function(E) {
        t(b, E, S[E]);
      }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(b, Object.getOwnPropertyDescriptors(S)) : i(Object(S)).forEach(function(E) {
        Object.defineProperty(b, E, Object.getOwnPropertyDescriptor(S, E));
      });
    }
    return b;
  }
  function t(b, x, S) {
    return x = u(x), x in b ? Object.defineProperty(b, x, { value: S, enumerable: !0, configurable: !0, writable: !0 }) : b[x] = S, b;
  }
  function r(b, x) {
    if (!(b instanceof x))
      throw new TypeError("Cannot call a class as a function");
  }
  function s(b, x) {
    for (var S = 0; S < x.length; S++) {
      var E = x[S];
      E.enumerable = E.enumerable || !1, E.configurable = !0, "value" in E && (E.writable = !0), Object.defineProperty(b, u(E.key), E);
    }
  }
  function n(b, x, S) {
    return x && s(b.prototype, x), Object.defineProperty(b, "prototype", { writable: !1 }), b;
  }
  function u(b) {
    var x = o(b, "string");
    return typeof x == "symbol" ? x : String(x);
  }
  function o(b, x) {
    if (typeof b != "object" || b === null) return b;
    var S = b[Symbol.toPrimitive];
    if (S !== void 0) {
      var E = S.call(b, x);
      if (typeof E != "object") return E;
      throw new TypeError("@@toPrimitive must return a primitive value.");
    }
    return String(b);
  }
  var p = xr(), y = p.Buffer, f = wa, v = f.inspect, m = v && v.custom || "inspect";
  function w(b, x, S) {
    y.prototype.copy.call(b, x, S);
  }
  return pi = /* @__PURE__ */ function() {
    function b() {
      r(this, b), this.head = null, this.tail = null, this.length = 0;
    }
    return n(b, [{
      key: "push",
      value: function(S) {
        var E = {
          data: S,
          next: null
        };
        this.length > 0 ? this.tail.next = E : this.head = E, this.tail = E, ++this.length;
      }
    }, {
      key: "unshift",
      value: function(S) {
        var E = {
          data: S,
          next: this.head
        };
        this.length === 0 && (this.tail = E), this.head = E, ++this.length;
      }
    }, {
      key: "shift",
      value: function() {
        if (this.length !== 0) {
          var S = this.head.data;
          return this.length === 1 ? this.head = this.tail = null : this.head = this.head.next, --this.length, S;
        }
      }
    }, {
      key: "clear",
      value: function() {
        this.head = this.tail = null, this.length = 0;
      }
    }, {
      key: "join",
      value: function(S) {
        if (this.length === 0) return "";
        for (var E = this.head, B = "" + E.data; E = E.next; ) B += S + E.data;
        return B;
      }
    }, {
      key: "concat",
      value: function(S) {
        if (this.length === 0) return y.alloc(0);
        for (var E = y.allocUnsafe(S >>> 0), B = this.head, A = 0; B; )
          w(B.data, E, A), A += B.data.length, B = B.next;
        return E;
      }
      // Consumes a specified amount of bytes or characters from the buffered data.
    }, {
      key: "consume",
      value: function(S, E) {
        var B;
        return S < this.head.data.length ? (B = this.head.data.slice(0, S), this.head.data = this.head.data.slice(S)) : S === this.head.data.length ? B = this.shift() : B = E ? this._getString(S) : this._getBuffer(S), B;
      }
    }, {
      key: "first",
      value: function() {
        return this.head.data;
      }
      // Consumes a specified amount of characters from the buffered data.
    }, {
      key: "_getString",
      value: function(S) {
        var E = this.head, B = 1, A = E.data;
        for (S -= A.length; E = E.next; ) {
          var k = E.data, T = S > k.length ? k.length : S;
          if (T === k.length ? A += k : A += k.slice(0, S), S -= T, S === 0) {
            T === k.length ? (++B, E.next ? this.head = E.next : this.head = this.tail = null) : (this.head = E, E.data = k.slice(T));
            break;
          }
          ++B;
        }
        return this.length -= B, A;
      }
      // Consumes a specified amount of bytes from the buffered data.
    }, {
      key: "_getBuffer",
      value: function(S) {
        var E = y.allocUnsafe(S), B = this.head, A = 1;
        for (B.data.copy(E), S -= B.data.length; B = B.next; ) {
          var k = B.data, T = S > k.length ? k.length : S;
          if (k.copy(E, E.length - S, 0, T), S -= T, S === 0) {
            T === k.length ? (++A, B.next ? this.head = B.next : this.head = this.tail = null) : (this.head = B, B.data = k.slice(T));
            break;
          }
          ++A;
        }
        return this.length -= A, E;
      }
      // Make sure the linked list only shows the minimal necessary information.
    }, {
      key: m,
      value: function(S, E) {
        return v(this, e(e({}, E), {}, {
          // Only inspect one level.
          depth: 0,
          // It should not recurse.
          customInspect: !1
        }));
      }
    }]), b;
  }(), pi;
}
var yi, ss;
function ma() {
  if (ss) return yi;
  ss = 1;
  function i(u, o) {
    var p = this, y = this._readableState && this._readableState.destroyed, f = this._writableState && this._writableState.destroyed;
    return y || f ? (o ? o(u) : u && (this._writableState ? this._writableState.errorEmitted || (this._writableState.errorEmitted = !0, xe.nextTick(s, this, u)) : xe.nextTick(s, this, u)), this) : (this._readableState && (this._readableState.destroyed = !0), this._writableState && (this._writableState.destroyed = !0), this._destroy(u || null, function(v) {
      !o && v ? p._writableState ? p._writableState.errorEmitted ? xe.nextTick(t, p) : (p._writableState.errorEmitted = !0, xe.nextTick(e, p, v)) : xe.nextTick(e, p, v) : o ? (xe.nextTick(t, p), o(v)) : xe.nextTick(t, p);
    }), this);
  }
  function e(u, o) {
    s(u, o), t(u);
  }
  function t(u) {
    u._writableState && !u._writableState.emitClose || u._readableState && !u._readableState.emitClose || u.emit("close");
  }
  function r() {
    this._readableState && (this._readableState.destroyed = !1, this._readableState.reading = !1, this._readableState.ended = !1, this._readableState.endEmitted = !1), this._writableState && (this._writableState.destroyed = !1, this._writableState.ended = !1, this._writableState.ending = !1, this._writableState.finalCalled = !1, this._writableState.prefinished = !1, this._writableState.finished = !1, this._writableState.errorEmitted = !1);
  }
  function s(u, o) {
    u.emit("error", o);
  }
  function n(u, o) {
    var p = u._readableState, y = u._writableState;
    p && p.autoDestroy || y && y.autoDestroy ? u.destroy(o) : u.emit("error", o);
  }
  return yi = {
    destroy: i,
    undestroy: r,
    errorOrDestroy: n
  }, yi;
}
var gi = {}, as;
function or() {
  if (as) return gi;
  as = 1;
  function i(o, p) {
    o.prototype = Object.create(p.prototype), o.prototype.constructor = o, o.__proto__ = p;
  }
  var e = {};
  function t(o, p, y) {
    y || (y = Error);
    function f(m, w, b) {
      return typeof p == "string" ? p : p(m, w, b);
    }
    var v = /* @__PURE__ */ function(m) {
      i(w, m);
      function w(b, x, S) {
        return m.call(this, f(b, x, S)) || this;
      }
      return w;
    }(y);
    v.prototype.name = y.name, v.prototype.code = o, e[o] = v;
  }
  function r(o, p) {
    if (Array.isArray(o)) {
      var y = o.length;
      return o = o.map(function(f) {
        return String(f);
      }), y > 2 ? "one of ".concat(p, " ").concat(o.slice(0, y - 1).join(", "), ", or ") + o[y - 1] : y === 2 ? "one of ".concat(p, " ").concat(o[0], " or ").concat(o[1]) : "of ".concat(p, " ").concat(o[0]);
    } else
      return "of ".concat(p, " ").concat(String(o));
  }
  function s(o, p, y) {
    return o.substr(0, p.length) === p;
  }
  function n(o, p, y) {
    return (y === void 0 || y > o.length) && (y = o.length), o.substring(y - p.length, y) === p;
  }
  function u(o, p, y) {
    return typeof y != "number" && (y = 0), y + p.length > o.length ? !1 : o.indexOf(p, y) !== -1;
  }
  return t("ERR_INVALID_OPT_VALUE", function(o, p) {
    return 'The value "' + p + '" is invalid for option "' + o + '"';
  }, TypeError), t("ERR_INVALID_ARG_TYPE", function(o, p, y) {
    var f;
    typeof p == "string" && s(p, "not ") ? (f = "must not be", p = p.replace(/^not /, "")) : f = "must be";
    var v;
    if (n(o, " argument"))
      v = "The ".concat(o, " ").concat(f, " ").concat(r(p, "type"));
    else {
      var m = u(o, ".") ? "property" : "argument";
      v = 'The "'.concat(o, '" ').concat(m, " ").concat(f, " ").concat(r(p, "type"));
    }
    return v += ". Received type ".concat(typeof y), v;
  }, TypeError), t("ERR_STREAM_PUSH_AFTER_EOF", "stream.push() after EOF"), t("ERR_METHOD_NOT_IMPLEMENTED", function(o) {
    return "The " + o + " method is not implemented";
  }), t("ERR_STREAM_PREMATURE_CLOSE", "Premature close"), t("ERR_STREAM_DESTROYED", function(o) {
    return "Cannot call " + o + " after a stream was destroyed";
  }), t("ERR_MULTIPLE_CALLBACK", "Callback called multiple times"), t("ERR_STREAM_CANNOT_PIPE", "Cannot pipe, not readable"), t("ERR_STREAM_WRITE_AFTER_END", "write after end"), t("ERR_STREAM_NULL_VALUES", "May not write null values to stream", TypeError), t("ERR_UNKNOWN_ENCODING", function(o) {
    return "Unknown encoding: " + o;
  }, TypeError), t("ERR_STREAM_UNSHIFT_AFTER_END_EVENT", "stream.unshift() after end event"), gi.codes = e, gi;
}
var wi, os;
function _a() {
  if (os) return wi;
  os = 1;
  var i = or().codes.ERR_INVALID_OPT_VALUE;
  function e(r, s, n) {
    return r.highWaterMark != null ? r.highWaterMark : s ? r[n] : null;
  }
  function t(r, s, n, u) {
    var o = e(s, u, n);
    if (o != null) {
      if (!(isFinite(o) && Math.floor(o) === o) || o < 0) {
        var p = u ? n : "highWaterMark";
        throw new i(p, o);
      }
      return Math.floor(o);
    }
    return r.objectMode ? 16 : 16 * 1024;
  }
  return wi = {
    getHighWaterMark: t
  }, wi;
}
var mi, hs;
function oh() {
  if (hs) return mi;
  hs = 1, mi = i;
  function i(t, r) {
    if (e("noDeprecation"))
      return t;
    var s = !1;
    function n() {
      if (!s) {
        if (e("throwDeprecation"))
          throw new Error(r);
        e("traceDeprecation") ? console.trace(r) : console.warn(r), s = !0;
      }
      return t.apply(this, arguments);
    }
    return n;
  }
  function e(t) {
    try {
      if (!ir.localStorage) return !1;
    } catch {
      return !1;
    }
    var r = ir.localStorage[t];
    return r == null ? !1 : String(r).toLowerCase() === "true";
  }
  return mi;
}
var _i, us;
function xn() {
  if (us) return _i;
  us = 1, _i = R;
  function i(L) {
    var P = this;
    this.next = null, this.entry = null, this.finish = function() {
      fe(P, L);
    };
  }
  var e;
  R.WritableState = D;
  var t = {
    deprecate: oh()
  }, r = ga(), s = xr().Buffer, n = (typeof ir < "u" ? ir : typeof window < "u" ? window : typeof self < "u" ? self : {}).Uint8Array || function() {
  };
  function u(L) {
    return s.from(L);
  }
  function o(L) {
    return s.isBuffer(L) || L instanceof n;
  }
  var p = ma(), y = _a(), f = y.getHighWaterMark, v = or().codes, m = v.ERR_INVALID_ARG_TYPE, w = v.ERR_METHOD_NOT_IMPLEMENTED, b = v.ERR_MULTIPLE_CALLBACK, x = v.ERR_STREAM_CANNOT_PIPE, S = v.ERR_STREAM_DESTROYED, E = v.ERR_STREAM_NULL_VALUES, B = v.ERR_STREAM_WRITE_AFTER_END, A = v.ERR_UNKNOWN_ENCODING, k = p.errorOrDestroy;
  Te()(R, r);
  function T() {
  }
  function D(L, P, V) {
    e = e || $t(), L = L || {}, typeof V != "boolean" && (V = P instanceof e), this.objectMode = !!L.objectMode, V && (this.objectMode = this.objectMode || !!L.writableObjectMode), this.highWaterMark = f(this, L, "writableHighWaterMark", V), this.finalCalled = !1, this.needDrain = !1, this.ending = !1, this.ended = !1, this.finished = !1, this.destroyed = !1;
    var z = L.decodeStrings === !1;
    this.decodeStrings = !z, this.defaultEncoding = L.defaultEncoding || "utf8", this.length = 0, this.writing = !1, this.corked = 0, this.sync = !0, this.bufferProcessing = !1, this.onwrite = function(Q) {
      ie(P, Q);
    }, this.writecb = null, this.writelen = 0, this.bufferedRequest = null, this.lastBufferedRequest = null, this.pendingcb = 0, this.prefinished = !1, this.errorEmitted = !1, this.emitClose = L.emitClose !== !1, this.autoDestroy = !!L.autoDestroy, this.bufferedRequestCount = 0, this.corkedRequestsFree = new i(this);
  }
  D.prototype.getBuffer = function() {
    for (var P = this.bufferedRequest, V = []; P; )
      V.push(P), P = P.next;
    return V;
  }, function() {
    try {
      Object.defineProperty(D.prototype, "buffer", {
        get: t.deprecate(function() {
          return this.getBuffer();
        }, "_writableState.buffer is deprecated. Use _writableState.getBuffer instead.", "DEP0003")
      });
    } catch {
    }
  }();
  var q;
  typeof Symbol == "function" && Symbol.hasInstance && typeof Function.prototype[Symbol.hasInstance] == "function" ? (q = Function.prototype[Symbol.hasInstance], Object.defineProperty(R, Symbol.hasInstance, {
    value: function(P) {
      return q.call(this, P) ? !0 : this !== R ? !1 : P && P._writableState instanceof D;
    }
  })) : q = function(P) {
    return P instanceof this;
  };
  function R(L) {
    e = e || $t();
    var P = this instanceof e;
    if (!P && !q.call(R, this)) return new R(L);
    this._writableState = new D(L, this, P), this.writable = !0, L && (typeof L.write == "function" && (this._write = L.write), typeof L.writev == "function" && (this._writev = L.writev), typeof L.destroy == "function" && (this._destroy = L.destroy), typeof L.final == "function" && (this._final = L.final)), r.call(this);
  }
  R.prototype.pipe = function() {
    k(this, new x());
  };
  function G(L, P) {
    var V = new B();
    k(L, V), xe.nextTick(P, V);
  }
  function J(L, P, V, z) {
    var Q;
    return V === null ? Q = new E() : typeof V != "string" && !P.objectMode && (Q = new m("chunk", ["string", "Buffer"], V)), Q ? (k(L, Q), xe.nextTick(z, Q), !1) : !0;
  }
  R.prototype.write = function(L, P, V) {
    var z = this._writableState, Q = !1, I = !z.objectMode && o(L);
    return I && !s.isBuffer(L) && (L = u(L)), typeof P == "function" && (V = P, P = null), I ? P = "buffer" : P || (P = z.defaultEncoding), typeof V != "function" && (V = T), z.ending ? G(this, V) : (I || J(this, z, L, V)) && (z.pendingcb++, Q = Z(this, z, I, L, P, V)), Q;
  }, R.prototype.cork = function() {
    this._writableState.corked++;
  }, R.prototype.uncork = function() {
    var L = this._writableState;
    L.corked && (L.corked--, !L.writing && !L.corked && !L.bufferProcessing && L.bufferedRequest && ge(this, L));
  }, R.prototype.setDefaultEncoding = function(P) {
    if (typeof P == "string" && (P = P.toLowerCase()), !(["hex", "utf8", "utf-8", "ascii", "binary", "base64", "ucs2", "ucs-2", "utf16le", "utf-16le", "raw"].indexOf((P + "").toLowerCase()) > -1)) throw new A(P);
    return this._writableState.defaultEncoding = P, this;
  }, Object.defineProperty(R.prototype, "writableBuffer", {
    // making it explicit this property is not enumerable
    // because otherwise some prototype manipulation in
    // userland will fail
    enumerable: !1,
    get: function() {
      return this._writableState && this._writableState.getBuffer();
    }
  });
  function se(L, P, V) {
    return !L.objectMode && L.decodeStrings !== !1 && typeof P == "string" && (P = s.from(P, V)), P;
  }
  Object.defineProperty(R.prototype, "writableHighWaterMark", {
    // making it explicit this property is not enumerable
    // because otherwise some prototype manipulation in
    // userland will fail
    enumerable: !1,
    get: function() {
      return this._writableState.highWaterMark;
    }
  });
  function Z(L, P, V, z, Q, I) {
    if (!V) {
      var C = se(P, z, Q);
      z !== C && (V = !0, Q = "buffer", z = C);
    }
    var K = P.objectMode ? 1 : z.length;
    P.length += K;
    var $ = P.length < P.highWaterMark;
    if ($ || (P.needDrain = !0), P.writing || P.corked) {
      var we = P.lastBufferedRequest;
      P.lastBufferedRequest = {
        chunk: z,
        encoding: Q,
        isBuf: V,
        callback: I,
        next: null
      }, we ? we.next = P.lastBufferedRequest : P.bufferedRequest = P.lastBufferedRequest, P.bufferedRequestCount += 1;
    } else
      X(L, P, !1, K, z, Q, I);
    return $;
  }
  function X(L, P, V, z, Q, I, C) {
    P.writelen = z, P.writecb = C, P.writing = !0, P.sync = !0, P.destroyed ? P.onwrite(new S("write")) : V ? L._writev(Q, P.onwrite) : L._write(Q, I, P.onwrite), P.sync = !1;
  }
  function ee(L, P, V, z, Q) {
    --P.pendingcb, V ? (xe.nextTick(Q, z), xe.nextTick(Ee, L, P), L._writableState.errorEmitted = !0, k(L, z)) : (Q(z), L._writableState.errorEmitted = !0, k(L, z), Ee(L, P));
  }
  function re(L) {
    L.writing = !1, L.writecb = null, L.length -= L.writelen, L.writelen = 0;
  }
  function ie(L, P) {
    var V = L._writableState, z = V.sync, Q = V.writecb;
    if (typeof Q != "function") throw new b();
    if (re(V), P) ee(L, V, z, P, Q);
    else {
      var I = Me(V) || L.destroyed;
      !I && !V.corked && !V.bufferProcessing && V.bufferedRequest && ge(L, V), z ? xe.nextTick(N, L, V, I, Q) : N(L, V, I, Q);
    }
  }
  function N(L, P, V, z) {
    V || ye(L, P), P.pendingcb--, z(), Ee(L, P);
  }
  function ye(L, P) {
    P.length === 0 && P.needDrain && (P.needDrain = !1, L.emit("drain"));
  }
  function ge(L, P) {
    P.bufferProcessing = !0;
    var V = P.bufferedRequest;
    if (L._writev && V && V.next) {
      var z = P.bufferedRequestCount, Q = new Array(z), I = P.corkedRequestsFree;
      I.entry = V;
      for (var C = 0, K = !0; V; )
        Q[C] = V, V.isBuf || (K = !1), V = V.next, C += 1;
      Q.allBuffers = K, X(L, P, !0, P.length, Q, "", I.finish), P.pendingcb++, P.lastBufferedRequest = null, I.next ? (P.corkedRequestsFree = I.next, I.next = null) : P.corkedRequestsFree = new i(P), P.bufferedRequestCount = 0;
    } else {
      for (; V; ) {
        var $ = V.chunk, we = V.encoding, ne = V.callback, ce = P.objectMode ? 1 : $.length;
        if (X(L, P, !1, ce, $, we, ne), V = V.next, P.bufferedRequestCount--, P.writing)
          break;
      }
      V === null && (P.lastBufferedRequest = null);
    }
    P.bufferedRequest = V, P.bufferProcessing = !1;
  }
  R.prototype._write = function(L, P, V) {
    V(new w("_write()"));
  }, R.prototype._writev = null, R.prototype.end = function(L, P, V) {
    var z = this._writableState;
    return typeof L == "function" ? (V = L, L = null, P = null) : typeof P == "function" && (V = P, P = null), L != null && this.write(L, P), z.corked && (z.corked = 1, this.uncork()), z.ending || he(this, z, V), this;
  }, Object.defineProperty(R.prototype, "writableLength", {
    // making it explicit this property is not enumerable
    // because otherwise some prototype manipulation in
    // userland will fail
    enumerable: !1,
    get: function() {
      return this._writableState.length;
    }
  });
  function Me(L) {
    return L.ending && L.length === 0 && L.bufferedRequest === null && !L.finished && !L.writing;
  }
  function ue(L, P) {
    L._final(function(V) {
      P.pendingcb--, V && k(L, V), P.prefinished = !0, L.emit("prefinish"), Ee(L, P);
    });
  }
  function le(L, P) {
    !P.prefinished && !P.finalCalled && (typeof L._final == "function" && !P.destroyed ? (P.pendingcb++, P.finalCalled = !0, xe.nextTick(ue, L, P)) : (P.prefinished = !0, L.emit("prefinish")));
  }
  function Ee(L, P) {
    var V = Me(P);
    if (V && (le(L, P), P.pendingcb === 0 && (P.finished = !0, L.emit("finish"), P.autoDestroy))) {
      var z = L._readableState;
      (!z || z.autoDestroy && z.endEmitted) && L.destroy();
    }
    return V;
  }
  function he(L, P, V) {
    P.ending = !0, Ee(L, P), V && (P.finished ? xe.nextTick(V) : L.once("finish", V)), P.ended = !0, L.writable = !1;
  }
  function fe(L, P, V) {
    var z = L.entry;
    for (L.entry = null; z; ) {
      var Q = z.callback;
      P.pendingcb--, Q(V), z = z.next;
    }
    P.corkedRequestsFree.next = L;
  }
  return Object.defineProperty(R.prototype, "destroyed", {
    // making it explicit this property is not enumerable
    // because otherwise some prototype manipulation in
    // userland will fail
    enumerable: !1,
    get: function() {
      return this._writableState === void 0 ? !1 : this._writableState.destroyed;
    },
    set: function(P) {
      this._writableState && (this._writableState.destroyed = P);
    }
  }), R.prototype.destroy = p.destroy, R.prototype._undestroy = p.undestroy, R.prototype._destroy = function(L, P) {
    P(L);
  }, _i;
}
var bi, fs;
function $t() {
  if (fs) return bi;
  fs = 1;
  var i = Object.keys || function(y) {
    var f = [];
    for (var v in y) f.push(v);
    return f;
  };
  bi = u;
  var e = vn(), t = xn();
  Te()(u, e);
  for (var r = i(t.prototype), s = 0; s < r.length; s++) {
    var n = r[s];
    u.prototype[n] || (u.prototype[n] = t.prototype[n]);
  }
  function u(y) {
    if (!(this instanceof u)) return new u(y);
    e.call(this, y), t.call(this, y), this.allowHalfOpen = !0, y && (y.readable === !1 && (this.readable = !1), y.writable === !1 && (this.writable = !1), y.allowHalfOpen === !1 && (this.allowHalfOpen = !1, this.once("end", o)));
  }
  Object.defineProperty(u.prototype, "writableHighWaterMark", {
    // making it explicit this property is not enumerable
    // because otherwise some prototype manipulation in
    // userland will fail
    enumerable: !1,
    get: function() {
      return this._writableState.highWaterMark;
    }
  }), Object.defineProperty(u.prototype, "writableBuffer", {
    // making it explicit this property is not enumerable
    // because otherwise some prototype manipulation in
    // userland will fail
    enumerable: !1,
    get: function() {
      return this._writableState && this._writableState.getBuffer();
    }
  }), Object.defineProperty(u.prototype, "writableLength", {
    // making it explicit this property is not enumerable
    // because otherwise some prototype manipulation in
    // userland will fail
    enumerable: !1,
    get: function() {
      return this._writableState.length;
    }
  });
  function o() {
    this._writableState.ended || xe.nextTick(p, this);
  }
  function p(y) {
    y.end();
  }
  return Object.defineProperty(u.prototype, "destroyed", {
    // making it explicit this property is not enumerable
    // because otherwise some prototype manipulation in
    // userland will fail
    enumerable: !1,
    get: function() {
      return this._readableState === void 0 || this._writableState === void 0 ? !1 : this._readableState.destroyed && this._writableState.destroyed;
    },
    set: function(f) {
      this._readableState === void 0 || this._writableState === void 0 || (this._readableState.destroyed = f, this._writableState.destroyed = f);
    }
  }), bi;
}
var xi = {}, cs;
function hn() {
  if (cs) return xi;
  cs = 1;
  var i = ke().Buffer, e = i.isEncoding || function(E) {
    switch (E = "" + E, E && E.toLowerCase()) {
      case "hex":
      case "utf8":
      case "utf-8":
      case "ascii":
      case "binary":
      case "base64":
      case "ucs2":
      case "ucs-2":
      case "utf16le":
      case "utf-16le":
      case "raw":
        return !0;
      default:
        return !1;
    }
  };
  function t(E) {
    if (!E) return "utf8";
    for (var B; ; )
      switch (E) {
        case "utf8":
        case "utf-8":
          return "utf8";
        case "ucs2":
        case "ucs-2":
        case "utf16le":
        case "utf-16le":
          return "utf16le";
        case "latin1":
        case "binary":
          return "latin1";
        case "base64":
        case "ascii":
        case "hex":
          return E;
        default:
          if (B) return;
          E = ("" + E).toLowerCase(), B = !0;
      }
  }
  function r(E) {
    var B = t(E);
    if (typeof B != "string" && (i.isEncoding === e || !e(E))) throw new Error("Unknown encoding: " + E);
    return B || E;
  }
  xi.StringDecoder = s;
  function s(E) {
    this.encoding = r(E);
    var B;
    switch (this.encoding) {
      case "utf16le":
        this.text = v, this.end = m, B = 4;
        break;
      case "utf8":
        this.fillLast = p, B = 4;
        break;
      case "base64":
        this.text = w, this.end = b, B = 3;
        break;
      default:
        this.write = x, this.end = S;
        return;
    }
    this.lastNeed = 0, this.lastTotal = 0, this.lastChar = i.allocUnsafe(B);
  }
  s.prototype.write = function(E) {
    if (E.length === 0) return "";
    var B, A;
    if (this.lastNeed) {
      if (B = this.fillLast(E), B === void 0) return "";
      A = this.lastNeed, this.lastNeed = 0;
    } else
      A = 0;
    return A < E.length ? B ? B + this.text(E, A) : this.text(E, A) : B || "";
  }, s.prototype.end = f, s.prototype.text = y, s.prototype.fillLast = function(E) {
    if (this.lastNeed <= E.length)
      return E.copy(this.lastChar, this.lastTotal - this.lastNeed, 0, this.lastNeed), this.lastChar.toString(this.encoding, 0, this.lastTotal);
    E.copy(this.lastChar, this.lastTotal - this.lastNeed, 0, E.length), this.lastNeed -= E.length;
  };
  function n(E) {
    return E <= 127 ? 0 : E >> 5 === 6 ? 2 : E >> 4 === 14 ? 3 : E >> 3 === 30 ? 4 : E >> 6 === 2 ? -1 : -2;
  }
  function u(E, B, A) {
    var k = B.length - 1;
    if (k < A) return 0;
    var T = n(B[k]);
    return T >= 0 ? (T > 0 && (E.lastNeed = T - 1), T) : --k < A || T === -2 ? 0 : (T = n(B[k]), T >= 0 ? (T > 0 && (E.lastNeed = T - 2), T) : --k < A || T === -2 ? 0 : (T = n(B[k]), T >= 0 ? (T > 0 && (T === 2 ? T = 0 : E.lastNeed = T - 3), T) : 0));
  }
  function o(E, B, A) {
    if ((B[0] & 192) !== 128)
      return E.lastNeed = 0, "�";
    if (E.lastNeed > 1 && B.length > 1) {
      if ((B[1] & 192) !== 128)
        return E.lastNeed = 1, "�";
      if (E.lastNeed > 2 && B.length > 2 && (B[2] & 192) !== 128)
        return E.lastNeed = 2, "�";
    }
  }
  function p(E) {
    var B = this.lastTotal - this.lastNeed, A = o(this, E);
    if (A !== void 0) return A;
    if (this.lastNeed <= E.length)
      return E.copy(this.lastChar, B, 0, this.lastNeed), this.lastChar.toString(this.encoding, 0, this.lastTotal);
    E.copy(this.lastChar, B, 0, E.length), this.lastNeed -= E.length;
  }
  function y(E, B) {
    var A = u(this, E, B);
    if (!this.lastNeed) return E.toString("utf8", B);
    this.lastTotal = A;
    var k = E.length - (A - this.lastNeed);
    return E.copy(this.lastChar, 0, k), E.toString("utf8", B, k);
  }
  function f(E) {
    var B = E && E.length ? this.write(E) : "";
    return this.lastNeed ? B + "�" : B;
  }
  function v(E, B) {
    if ((E.length - B) % 2 === 0) {
      var A = E.toString("utf16le", B);
      if (A) {
        var k = A.charCodeAt(A.length - 1);
        if (k >= 55296 && k <= 56319)
          return this.lastNeed = 2, this.lastTotal = 4, this.lastChar[0] = E[E.length - 2], this.lastChar[1] = E[E.length - 1], A.slice(0, -1);
      }
      return A;
    }
    return this.lastNeed = 1, this.lastTotal = 2, this.lastChar[0] = E[E.length - 1], E.toString("utf16le", B, E.length - 1);
  }
  function m(E) {
    var B = E && E.length ? this.write(E) : "";
    if (this.lastNeed) {
      var A = this.lastTotal - this.lastNeed;
      return B + this.lastChar.toString("utf16le", 0, A);
    }
    return B;
  }
  function w(E, B) {
    var A = (E.length - B) % 3;
    return A === 0 ? E.toString("base64", B) : (this.lastNeed = 3 - A, this.lastTotal = 3, A === 1 ? this.lastChar[0] = E[E.length - 1] : (this.lastChar[0] = E[E.length - 2], this.lastChar[1] = E[E.length - 1]), E.toString("base64", B, E.length - A));
  }
  function b(E) {
    var B = E && E.length ? this.write(E) : "";
    return this.lastNeed ? B + this.lastChar.toString("base64", 0, 3 - this.lastNeed) : B;
  }
  function x(E) {
    return E.toString(this.encoding);
  }
  function S(E) {
    return E && E.length ? this.write(E) : "";
  }
  return xi;
}
var vi, ls;
function Xr() {
  if (ls) return vi;
  ls = 1;
  var i = or().codes.ERR_STREAM_PREMATURE_CLOSE;
  function e(n) {
    var u = !1;
    return function() {
      if (!u) {
        u = !0;
        for (var o = arguments.length, p = new Array(o), y = 0; y < o; y++)
          p[y] = arguments[y];
        n.apply(this, p);
      }
    };
  }
  function t() {
  }
  function r(n) {
    return n.setHeader && typeof n.abort == "function";
  }
  function s(n, u, o) {
    if (typeof u == "function") return s(n, null, u);
    u || (u = {}), o = e(o || t);
    var p = u.readable || u.readable !== !1 && n.readable, y = u.writable || u.writable !== !1 && n.writable, f = function() {
      n.writable || m();
    }, v = n._writableState && n._writableState.finished, m = function() {
      y = !1, v = !0, p || o.call(n);
    }, w = n._readableState && n._readableState.endEmitted, b = function() {
      p = !1, w = !0, y || o.call(n);
    }, x = function(A) {
      o.call(n, A);
    }, S = function() {
      var A;
      if (p && !w)
        return (!n._readableState || !n._readableState.ended) && (A = new i()), o.call(n, A);
      if (y && !v)
        return (!n._writableState || !n._writableState.ended) && (A = new i()), o.call(n, A);
    }, E = function() {
      n.req.on("finish", m);
    };
    return r(n) ? (n.on("complete", m), n.on("abort", S), n.req ? E() : n.on("request", E)) : y && !n._writableState && (n.on("end", f), n.on("close", f)), n.on("end", b), n.on("finish", m), u.error !== !1 && n.on("error", x), n.on("close", S), function() {
      n.removeListener("complete", m), n.removeListener("abort", S), n.removeListener("request", E), n.req && n.req.removeListener("finish", m), n.removeListener("end", f), n.removeListener("close", f), n.removeListener("finish", m), n.removeListener("end", b), n.removeListener("error", x), n.removeListener("close", S);
    };
  }
  return vi = s, vi;
}
var Ei, ds;
function hh() {
  if (ds) return Ei;
  ds = 1;
  var i;
  function e(A, k, T) {
    return k = t(k), k in A ? Object.defineProperty(A, k, { value: T, enumerable: !0, configurable: !0, writable: !0 }) : A[k] = T, A;
  }
  function t(A) {
    var k = r(A, "string");
    return typeof k == "symbol" ? k : String(k);
  }
  function r(A, k) {
    if (typeof A != "object" || A === null) return A;
    var T = A[Symbol.toPrimitive];
    if (T !== void 0) {
      var D = T.call(A, k);
      if (typeof D != "object") return D;
      throw new TypeError("@@toPrimitive must return a primitive value.");
    }
    return (k === "string" ? String : Number)(A);
  }
  var s = Xr(), n = Symbol("lastResolve"), u = Symbol("lastReject"), o = Symbol("error"), p = Symbol("ended"), y = Symbol("lastPromise"), f = Symbol("handlePromise"), v = Symbol("stream");
  function m(A, k) {
    return {
      value: A,
      done: k
    };
  }
  function w(A) {
    var k = A[n];
    if (k !== null) {
      var T = A[v].read();
      T !== null && (A[y] = null, A[n] = null, A[u] = null, k(m(T, !1)));
    }
  }
  function b(A) {
    xe.nextTick(w, A);
  }
  function x(A, k) {
    return function(T, D) {
      A.then(function() {
        if (k[p]) {
          T(m(void 0, !0));
          return;
        }
        k[f](T, D);
      }, D);
    };
  }
  var S = Object.getPrototypeOf(function() {
  }), E = Object.setPrototypeOf((i = {
    get stream() {
      return this[v];
    },
    next: function() {
      var k = this, T = this[o];
      if (T !== null)
        return Promise.reject(T);
      if (this[p])
        return Promise.resolve(m(void 0, !0));
      if (this[v].destroyed)
        return new Promise(function(G, J) {
          xe.nextTick(function() {
            k[o] ? J(k[o]) : G(m(void 0, !0));
          });
        });
      var D = this[y], q;
      if (D)
        q = new Promise(x(D, this));
      else {
        var R = this[v].read();
        if (R !== null)
          return Promise.resolve(m(R, !1));
        q = new Promise(this[f]);
      }
      return this[y] = q, q;
    }
  }, e(i, Symbol.asyncIterator, function() {
    return this;
  }), e(i, "return", function() {
    var k = this;
    return new Promise(function(T, D) {
      k[v].destroy(null, function(q) {
        if (q) {
          D(q);
          return;
        }
        T(m(void 0, !0));
      });
    });
  }), i), S), B = function(k) {
    var T, D = Object.create(E, (T = {}, e(T, v, {
      value: k,
      writable: !0
    }), e(T, n, {
      value: null,
      writable: !0
    }), e(T, u, {
      value: null,
      writable: !0
    }), e(T, o, {
      value: null,
      writable: !0
    }), e(T, p, {
      value: k._readableState.endEmitted,
      writable: !0
    }), e(T, f, {
      value: function(R, G) {
        var J = D[v].read();
        J ? (D[y] = null, D[n] = null, D[u] = null, R(m(J, !1))) : (D[n] = R, D[u] = G);
      },
      writable: !0
    }), T));
    return D[y] = null, s(k, function(q) {
      if (q && q.code !== "ERR_STREAM_PREMATURE_CLOSE") {
        var R = D[u];
        R !== null && (D[y] = null, D[n] = null, D[u] = null, R(q)), D[o] = q;
        return;
      }
      var G = D[n];
      G !== null && (D[y] = null, D[n] = null, D[u] = null, G(m(void 0, !0))), D[p] = !0;
    }), k.on("readable", b.bind(null, D)), D;
  };
  return Ei = B, Ei;
}
var Bi, ps;
function uh() {
  return ps || (ps = 1, Bi = function() {
    throw new Error("Readable.from is not available in the browser");
  }), Bi;
}
var Si, ys;
function vn() {
  if (ys) return Si;
  ys = 1, Si = G;
  var i;
  G.ReadableState = R, bn().EventEmitter;
  var e = function(C, K) {
    return C.listeners(K).length;
  }, t = ga(), r = xr().Buffer, s = (typeof ir < "u" ? ir : typeof window < "u" ? window : typeof self < "u" ? self : {}).Uint8Array || function() {
  };
  function n(I) {
    return r.from(I);
  }
  function u(I) {
    return r.isBuffer(I) || I instanceof s;
  }
  var o = wa, p;
  o && o.debuglog ? p = o.debuglog("stream") : p = function() {
  };
  var y = ah(), f = ma(), v = _a(), m = v.getHighWaterMark, w = or().codes, b = w.ERR_INVALID_ARG_TYPE, x = w.ERR_STREAM_PUSH_AFTER_EOF, S = w.ERR_METHOD_NOT_IMPLEMENTED, E = w.ERR_STREAM_UNSHIFT_AFTER_END_EVENT, B, A, k;
  Te()(G, t);
  var T = f.errorOrDestroy, D = ["error", "close", "destroy", "pause", "resume"];
  function q(I, C, K) {
    if (typeof I.prependListener == "function") return I.prependListener(C, K);
    !I._events || !I._events[C] ? I.on(C, K) : Array.isArray(I._events[C]) ? I._events[C].unshift(K) : I._events[C] = [K, I._events[C]];
  }
  function R(I, C, K) {
    i = i || $t(), I = I || {}, typeof K != "boolean" && (K = C instanceof i), this.objectMode = !!I.objectMode, K && (this.objectMode = this.objectMode || !!I.readableObjectMode), this.highWaterMark = m(this, I, "readableHighWaterMark", K), this.buffer = new y(), this.length = 0, this.pipes = null, this.pipesCount = 0, this.flowing = null, this.ended = !1, this.endEmitted = !1, this.reading = !1, this.sync = !0, this.needReadable = !1, this.emittedReadable = !1, this.readableListening = !1, this.resumeScheduled = !1, this.paused = !0, this.emitClose = I.emitClose !== !1, this.autoDestroy = !!I.autoDestroy, this.destroyed = !1, this.defaultEncoding = I.defaultEncoding || "utf8", this.awaitDrain = 0, this.readingMore = !1, this.decoder = null, this.encoding = null, I.encoding && (B || (B = hn().StringDecoder), this.decoder = new B(I.encoding), this.encoding = I.encoding);
  }
  function G(I) {
    if (i = i || $t(), !(this instanceof G)) return new G(I);
    var C = this instanceof i;
    this._readableState = new R(I, this, C), this.readable = !0, I && (typeof I.read == "function" && (this._read = I.read), typeof I.destroy == "function" && (this._destroy = I.destroy)), t.call(this);
  }
  Object.defineProperty(G.prototype, "destroyed", {
    // making it explicit this property is not enumerable
    // because otherwise some prototype manipulation in
    // userland will fail
    enumerable: !1,
    get: function() {
      return this._readableState === void 0 ? !1 : this._readableState.destroyed;
    },
    set: function(C) {
      this._readableState && (this._readableState.destroyed = C);
    }
  }), G.prototype.destroy = f.destroy, G.prototype._undestroy = f.undestroy, G.prototype._destroy = function(I, C) {
    C(I);
  }, G.prototype.push = function(I, C) {
    var K = this._readableState, $;
    return K.objectMode ? $ = !0 : typeof I == "string" && (C = C || K.defaultEncoding, C !== K.encoding && (I = r.from(I, C), C = ""), $ = !0), J(this, I, C, !1, $);
  }, G.prototype.unshift = function(I) {
    return J(this, I, null, !0, !1);
  };
  function J(I, C, K, $, we) {
    p("readableAddChunk", C);
    var ne = I._readableState;
    if (C === null)
      ne.reading = !1, ie(I, ne);
    else {
      var ce;
      if (we || (ce = Z(ne, C)), ce)
        T(I, ce);
      else if (ne.objectMode || C && C.length > 0)
        if (typeof C != "string" && !ne.objectMode && Object.getPrototypeOf(C) !== r.prototype && (C = n(C)), $)
          ne.endEmitted ? T(I, new E()) : se(I, ne, C, !0);
        else if (ne.ended)
          T(I, new x());
        else {
          if (ne.destroyed)
            return !1;
          ne.reading = !1, ne.decoder && !K ? (C = ne.decoder.write(C), ne.objectMode || C.length !== 0 ? se(I, ne, C, !1) : ge(I, ne)) : se(I, ne, C, !1);
        }
      else $ || (ne.reading = !1, ge(I, ne));
    }
    return !ne.ended && (ne.length < ne.highWaterMark || ne.length === 0);
  }
  function se(I, C, K, $) {
    C.flowing && C.length === 0 && !C.sync ? (C.awaitDrain = 0, I.emit("data", K)) : (C.length += C.objectMode ? 1 : K.length, $ ? C.buffer.unshift(K) : C.buffer.push(K), C.needReadable && N(I)), ge(I, C);
  }
  function Z(I, C) {
    var K;
    return !u(C) && typeof C != "string" && C !== void 0 && !I.objectMode && (K = new b("chunk", ["string", "Buffer", "Uint8Array"], C)), K;
  }
  G.prototype.isPaused = function() {
    return this._readableState.flowing === !1;
  }, G.prototype.setEncoding = function(I) {
    B || (B = hn().StringDecoder);
    var C = new B(I);
    this._readableState.decoder = C, this._readableState.encoding = this._readableState.decoder.encoding;
    for (var K = this._readableState.buffer.head, $ = ""; K !== null; )
      $ += C.write(K.data), K = K.next;
    return this._readableState.buffer.clear(), $ !== "" && this._readableState.buffer.push($), this._readableState.length = $.length, this;
  };
  var X = 1073741824;
  function ee(I) {
    return I >= X ? I = X : (I--, I |= I >>> 1, I |= I >>> 2, I |= I >>> 4, I |= I >>> 8, I |= I >>> 16, I++), I;
  }
  function re(I, C) {
    return I <= 0 || C.length === 0 && C.ended ? 0 : C.objectMode ? 1 : I !== I ? C.flowing && C.length ? C.buffer.head.data.length : C.length : (I > C.highWaterMark && (C.highWaterMark = ee(I)), I <= C.length ? I : C.ended ? C.length : (C.needReadable = !0, 0));
  }
  G.prototype.read = function(I) {
    p("read", I), I = parseInt(I, 10);
    var C = this._readableState, K = I;
    if (I !== 0 && (C.emittedReadable = !1), I === 0 && C.needReadable && ((C.highWaterMark !== 0 ? C.length >= C.highWaterMark : C.length > 0) || C.ended))
      return p("read: emitReadable", C.length, C.ended), C.length === 0 && C.ended ? V(this) : N(this), null;
    if (I = re(I, C), I === 0 && C.ended)
      return C.length === 0 && V(this), null;
    var $ = C.needReadable;
    p("need readable", $), (C.length === 0 || C.length - I < C.highWaterMark) && ($ = !0, p("length less than watermark", $)), C.ended || C.reading ? ($ = !1, p("reading or ended", $)) : $ && (p("do read"), C.reading = !0, C.sync = !0, C.length === 0 && (C.needReadable = !0), this._read(C.highWaterMark), C.sync = !1, C.reading || (I = re(K, C)));
    var we;
    return I > 0 ? we = P(I, C) : we = null, we === null ? (C.needReadable = C.length <= C.highWaterMark, I = 0) : (C.length -= I, C.awaitDrain = 0), C.length === 0 && (C.ended || (C.needReadable = !0), K !== I && C.ended && V(this)), we !== null && this.emit("data", we), we;
  };
  function ie(I, C) {
    if (p("onEofChunk"), !C.ended) {
      if (C.decoder) {
        var K = C.decoder.end();
        K && K.length && (C.buffer.push(K), C.length += C.objectMode ? 1 : K.length);
      }
      C.ended = !0, C.sync ? N(I) : (C.needReadable = !1, C.emittedReadable || (C.emittedReadable = !0, ye(I)));
    }
  }
  function N(I) {
    var C = I._readableState;
    p("emitReadable", C.needReadable, C.emittedReadable), C.needReadable = !1, C.emittedReadable || (p("emitReadable", C.flowing), C.emittedReadable = !0, xe.nextTick(ye, I));
  }
  function ye(I) {
    var C = I._readableState;
    p("emitReadable_", C.destroyed, C.length, C.ended), !C.destroyed && (C.length || C.ended) && (I.emit("readable"), C.emittedReadable = !1), C.needReadable = !C.flowing && !C.ended && C.length <= C.highWaterMark, L(I);
  }
  function ge(I, C) {
    C.readingMore || (C.readingMore = !0, xe.nextTick(Me, I, C));
  }
  function Me(I, C) {
    for (; !C.reading && !C.ended && (C.length < C.highWaterMark || C.flowing && C.length === 0); ) {
      var K = C.length;
      if (p("maybeReadMore read 0"), I.read(0), K === C.length)
        break;
    }
    C.readingMore = !1;
  }
  G.prototype._read = function(I) {
    T(this, new S("_read()"));
  }, G.prototype.pipe = function(I, C) {
    var K = this, $ = this._readableState;
    switch ($.pipesCount) {
      case 0:
        $.pipes = I;
        break;
      case 1:
        $.pipes = [$.pipes, I];
        break;
      default:
        $.pipes.push(I);
        break;
    }
    $.pipesCount += 1, p("pipe count=%d opts=%j", $.pipesCount, C);
    var we = (!C || C.end !== !1) && I !== xe.stdout && I !== xe.stderr, ne = we ? Qe : dt;
    $.endEmitted ? xe.nextTick(ne) : K.once("end", ne), I.on("unpipe", ce);
    function ce(_, l) {
      p("onunpipe"), _ === K && l && l.hasUnpiped === !1 && (l.hasUnpiped = !0, Ue());
    }
    function Qe() {
      p("onend"), I.end();
    }
    var mt = ue(K);
    I.on("drain", mt);
    var st = !1;
    function Ue() {
      p("cleanup"), I.removeListener("close", at), I.removeListener("finish", Fe), I.removeListener("drain", mt), I.removeListener("error", et), I.removeListener("unpipe", ce), K.removeListener("end", Qe), K.removeListener("end", dt), K.removeListener("data", Ce), st = !0, $.awaitDrain && (!I._writableState || I._writableState.needDrain) && mt();
    }
    K.on("data", Ce);
    function Ce(_) {
      p("ondata");
      var l = I.write(_);
      p("dest.write", l), l === !1 && (($.pipesCount === 1 && $.pipes === I || $.pipesCount > 1 && Q($.pipes, I) !== -1) && !st && (p("false write response, pause", $.awaitDrain), $.awaitDrain++), K.pause());
    }
    function et(_) {
      p("onerror", _), dt(), I.removeListener("error", et), e(I, "error") === 0 && T(I, _);
    }
    q(I, "error", et);
    function at() {
      I.removeListener("finish", Fe), dt();
    }
    I.once("close", at);
    function Fe() {
      p("onfinish"), I.removeListener("close", at), dt();
    }
    I.once("finish", Fe);
    function dt() {
      p("unpipe"), K.unpipe(I);
    }
    return I.emit("pipe", K), $.flowing || (p("pipe resume"), K.resume()), I;
  };
  function ue(I) {
    return function() {
      var K = I._readableState;
      p("pipeOnDrain", K.awaitDrain), K.awaitDrain && K.awaitDrain--, K.awaitDrain === 0 && e(I, "data") && (K.flowing = !0, L(I));
    };
  }
  G.prototype.unpipe = function(I) {
    var C = this._readableState, K = {
      hasUnpiped: !1
    };
    if (C.pipesCount === 0) return this;
    if (C.pipesCount === 1)
      return I && I !== C.pipes ? this : (I || (I = C.pipes), C.pipes = null, C.pipesCount = 0, C.flowing = !1, I && I.emit("unpipe", this, K), this);
    if (!I) {
      var $ = C.pipes, we = C.pipesCount;
      C.pipes = null, C.pipesCount = 0, C.flowing = !1;
      for (var ne = 0; ne < we; ne++) $[ne].emit("unpipe", this, {
        hasUnpiped: !1
      });
      return this;
    }
    var ce = Q(C.pipes, I);
    return ce === -1 ? this : (C.pipes.splice(ce, 1), C.pipesCount -= 1, C.pipesCount === 1 && (C.pipes = C.pipes[0]), I.emit("unpipe", this, K), this);
  }, G.prototype.on = function(I, C) {
    var K = t.prototype.on.call(this, I, C), $ = this._readableState;
    return I === "data" ? ($.readableListening = this.listenerCount("readable") > 0, $.flowing !== !1 && this.resume()) : I === "readable" && !$.endEmitted && !$.readableListening && ($.readableListening = $.needReadable = !0, $.flowing = !1, $.emittedReadable = !1, p("on readable", $.length, $.reading), $.length ? N(this) : $.reading || xe.nextTick(Ee, this)), K;
  }, G.prototype.addListener = G.prototype.on, G.prototype.removeListener = function(I, C) {
    var K = t.prototype.removeListener.call(this, I, C);
    return I === "readable" && xe.nextTick(le, this), K;
  }, G.prototype.removeAllListeners = function(I) {
    var C = t.prototype.removeAllListeners.apply(this, arguments);
    return (I === "readable" || I === void 0) && xe.nextTick(le, this), C;
  };
  function le(I) {
    var C = I._readableState;
    C.readableListening = I.listenerCount("readable") > 0, C.resumeScheduled && !C.paused ? C.flowing = !0 : I.listenerCount("data") > 0 && I.resume();
  }
  function Ee(I) {
    p("readable nexttick read 0"), I.read(0);
  }
  G.prototype.resume = function() {
    var I = this._readableState;
    return I.flowing || (p("resume"), I.flowing = !I.readableListening, he(this, I)), I.paused = !1, this;
  };
  function he(I, C) {
    C.resumeScheduled || (C.resumeScheduled = !0, xe.nextTick(fe, I, C));
  }
  function fe(I, C) {
    p("resume", C.reading), C.reading || I.read(0), C.resumeScheduled = !1, I.emit("resume"), L(I), C.flowing && !C.reading && I.read(0);
  }
  G.prototype.pause = function() {
    return p("call pause flowing=%j", this._readableState.flowing), this._readableState.flowing !== !1 && (p("pause"), this._readableState.flowing = !1, this.emit("pause")), this._readableState.paused = !0, this;
  };
  function L(I) {
    var C = I._readableState;
    for (p("flow", C.flowing); C.flowing && I.read() !== null; ) ;
  }
  G.prototype.wrap = function(I) {
    var C = this, K = this._readableState, $ = !1;
    I.on("end", function() {
      if (p("wrapped end"), K.decoder && !K.ended) {
        var ce = K.decoder.end();
        ce && ce.length && C.push(ce);
      }
      C.push(null);
    }), I.on("data", function(ce) {
      if (p("wrapped data"), K.decoder && (ce = K.decoder.write(ce)), !(K.objectMode && ce == null) && !(!K.objectMode && (!ce || !ce.length))) {
        var Qe = C.push(ce);
        Qe || ($ = !0, I.pause());
      }
    });
    for (var we in I)
      this[we] === void 0 && typeof I[we] == "function" && (this[we] = /* @__PURE__ */ function(Qe) {
        return function() {
          return I[Qe].apply(I, arguments);
        };
      }(we));
    for (var ne = 0; ne < D.length; ne++)
      I.on(D[ne], this.emit.bind(this, D[ne]));
    return this._read = function(ce) {
      p("wrapped _read", ce), $ && ($ = !1, I.resume());
    }, this;
  }, typeof Symbol == "function" && (G.prototype[Symbol.asyncIterator] = function() {
    return A === void 0 && (A = hh()), A(this);
  }), Object.defineProperty(G.prototype, "readableHighWaterMark", {
    // making it explicit this property is not enumerable
    // because otherwise some prototype manipulation in
    // userland will fail
    enumerable: !1,
    get: function() {
      return this._readableState.highWaterMark;
    }
  }), Object.defineProperty(G.prototype, "readableBuffer", {
    // making it explicit this property is not enumerable
    // because otherwise some prototype manipulation in
    // userland will fail
    enumerable: !1,
    get: function() {
      return this._readableState && this._readableState.buffer;
    }
  }), Object.defineProperty(G.prototype, "readableFlowing", {
    // making it explicit this property is not enumerable
    // because otherwise some prototype manipulation in
    // userland will fail
    enumerable: !1,
    get: function() {
      return this._readableState.flowing;
    },
    set: function(C) {
      this._readableState && (this._readableState.flowing = C);
    }
  }), G._fromList = P, Object.defineProperty(G.prototype, "readableLength", {
    // making it explicit this property is not enumerable
    // because otherwise some prototype manipulation in
    // userland will fail
    enumerable: !1,
    get: function() {
      return this._readableState.length;
    }
  });
  function P(I, C) {
    if (C.length === 0) return null;
    var K;
    return C.objectMode ? K = C.buffer.shift() : !I || I >= C.length ? (C.decoder ? K = C.buffer.join("") : C.buffer.length === 1 ? K = C.buffer.first() : K = C.buffer.concat(C.length), C.buffer.clear()) : K = C.buffer.consume(I, C.decoder), K;
  }
  function V(I) {
    var C = I._readableState;
    p("endReadable", C.endEmitted), C.endEmitted || (C.ended = !0, xe.nextTick(z, C, I));
  }
  function z(I, C) {
    if (p("endReadableNT", I.endEmitted, I.length), !I.endEmitted && I.length === 0 && (I.endEmitted = !0, C.readable = !1, C.emit("end"), I.autoDestroy)) {
      var K = C._writableState;
      (!K || K.autoDestroy && K.finished) && C.destroy();
    }
  }
  typeof Symbol == "function" && (G.from = function(I, C) {
    return k === void 0 && (k = uh()), k(G, I, C);
  });
  function Q(I, C) {
    for (var K = 0, $ = I.length; K < $; K++)
      if (I[K] === C) return K;
    return -1;
  }
  return Si;
}
var Ii, gs;
function En() {
  if (gs) return Ii;
  gs = 1, Ii = o;
  var i = or().codes, e = i.ERR_METHOD_NOT_IMPLEMENTED, t = i.ERR_MULTIPLE_CALLBACK, r = i.ERR_TRANSFORM_ALREADY_TRANSFORMING, s = i.ERR_TRANSFORM_WITH_LENGTH_0, n = $t();
  Te()(o, n);
  function u(f, v) {
    var m = this._transformState;
    m.transforming = !1;
    var w = m.writecb;
    if (w === null)
      return this.emit("error", new t());
    m.writechunk = null, m.writecb = null, v != null && this.push(v), w(f);
    var b = this._readableState;
    b.reading = !1, (b.needReadable || b.length < b.highWaterMark) && this._read(b.highWaterMark);
  }
  function o(f) {
    if (!(this instanceof o)) return new o(f);
    n.call(this, f), this._transformState = {
      afterTransform: u.bind(this),
      needTransform: !1,
      transforming: !1,
      writecb: null,
      writechunk: null,
      writeencoding: null
    }, this._readableState.needReadable = !0, this._readableState.sync = !1, f && (typeof f.transform == "function" && (this._transform = f.transform), typeof f.flush == "function" && (this._flush = f.flush)), this.on("prefinish", p);
  }
  function p() {
    var f = this;
    typeof this._flush == "function" && !this._readableState.destroyed ? this._flush(function(v, m) {
      y(f, v, m);
    }) : y(this, null, null);
  }
  o.prototype.push = function(f, v) {
    return this._transformState.needTransform = !1, n.prototype.push.call(this, f, v);
  }, o.prototype._transform = function(f, v, m) {
    m(new e("_transform()"));
  }, o.prototype._write = function(f, v, m) {
    var w = this._transformState;
    if (w.writecb = m, w.writechunk = f, w.writeencoding = v, !w.transforming) {
      var b = this._readableState;
      (w.needTransform || b.needReadable || b.length < b.highWaterMark) && this._read(b.highWaterMark);
    }
  }, o.prototype._read = function(f) {
    var v = this._transformState;
    v.writechunk !== null && !v.transforming ? (v.transforming = !0, this._transform(v.writechunk, v.writeencoding, v.afterTransform)) : v.needTransform = !0;
  }, o.prototype._destroy = function(f, v) {
    n.prototype._destroy.call(this, f, function(m) {
      v(m);
    });
  };
  function y(f, v, m) {
    if (v) return f.emit("error", v);
    if (m != null && f.push(m), f._writableState.length) throw new s();
    if (f._transformState.transforming) throw new r();
    return f.push(null);
  }
  return Ii;
}
var Ci, ws;
function ba() {
  if (ws) return Ci;
  ws = 1, Ci = e;
  var i = En();
  Te()(e, i);
  function e(t) {
    if (!(this instanceof e)) return new e(t);
    i.call(this, t);
  }
  return e.prototype._transform = function(t, r, s) {
    s(null, t);
  }, Ci;
}
var Ai, ms;
function xa() {
  if (ms) return Ai;
  ms = 1;
  var i;
  function e(m) {
    var w = !1;
    return function() {
      w || (w = !0, m.apply(void 0, arguments));
    };
  }
  var t = or().codes, r = t.ERR_MISSING_ARGS, s = t.ERR_STREAM_DESTROYED;
  function n(m) {
    if (m) throw m;
  }
  function u(m) {
    return m.setHeader && typeof m.abort == "function";
  }
  function o(m, w, b, x) {
    x = e(x);
    var S = !1;
    m.on("close", function() {
      S = !0;
    }), i === void 0 && (i = Xr()), i(m, {
      readable: w,
      writable: b
    }, function(B) {
      if (B) return x(B);
      S = !0, x();
    });
    var E = !1;
    return function(B) {
      if (!S && !E) {
        if (E = !0, u(m)) return m.abort();
        if (typeof m.destroy == "function") return m.destroy();
        x(B || new s("pipe"));
      }
    };
  }
  function p(m) {
    m();
  }
  function y(m, w) {
    return m.pipe(w);
  }
  function f(m) {
    return !m.length || typeof m[m.length - 1] != "function" ? n : m.pop();
  }
  function v() {
    for (var m = arguments.length, w = new Array(m), b = 0; b < m; b++)
      w[b] = arguments[b];
    var x = f(w);
    if (Array.isArray(w[0]) && (w = w[0]), w.length < 2)
      throw new r("streams");
    var S, E = w.map(function(B, A) {
      var k = A < w.length - 1, T = A > 0;
      return o(B, k, T, function(D) {
        S || (S = D), D && E.forEach(p), !k && (E.forEach(p), x(S));
      });
    });
    return w.reduce(y);
  }
  return Ai = v, Ai;
}
var ki, _s;
function fh() {
  if (_s) return ki;
  _s = 1, ki = t;
  var i = bn().EventEmitter, e = Te();
  e(t, i), t.Readable = vn(), t.Writable = xn(), t.Duplex = $t(), t.Transform = En(), t.PassThrough = ba(), t.finished = Xr(), t.pipeline = xa(), t.Stream = t;
  function t() {
    i.call(this);
  }
  return t.prototype.pipe = function(r, s) {
    var n = this;
    function u(w) {
      r.writable && r.write(w) === !1 && n.pause && n.pause();
    }
    n.on("data", u);
    function o() {
      n.readable && n.resume && n.resume();
    }
    r.on("drain", o), !r._isStdio && (!s || s.end !== !1) && (n.on("end", y), n.on("close", f));
    var p = !1;
    function y() {
      p || (p = !0, r.end());
    }
    function f() {
      p || (p = !0, typeof r.destroy == "function" && r.destroy());
    }
    function v(w) {
      if (m(), i.listenerCount(this, "error") === 0)
        throw w;
    }
    n.on("error", v), r.on("error", v);
    function m() {
      n.removeListener("data", u), r.removeListener("drain", o), n.removeListener("end", y), n.removeListener("close", f), n.removeListener("error", v), r.removeListener("error", v), n.removeListener("end", m), n.removeListener("close", m), r.removeListener("close", m);
    }
    return n.on("end", m), n.on("close", m), r.on("close", m), r.emit("pipe", n), r;
  }, ki;
}
var Fi, bs;
function Xt() {
  if (bs) return Fi;
  bs = 1;
  var i = ke().Buffer, e = fh().Transform, t = hn().StringDecoder, r = Te();
  function s(p) {
    e.call(this), this.hashMode = typeof p == "string", this.hashMode ? this[p] = this._finalOrDigest : this.final = this._finalOrDigest, this._final && (this.__final = this._final, this._final = null), this._decoder = null, this._encoding = null;
  }
  r(s, e);
  var n = typeof Uint8Array < "u", u = typeof ArrayBuffer < "u" && typeof Uint8Array < "u" && ArrayBuffer.isView && (i.prototype instanceof Uint8Array || i.TYPED_ARRAY_SUPPORT);
  function o(p, y) {
    if (p instanceof i)
      return p;
    if (typeof p == "string")
      return i.from(p, y);
    if (u && ArrayBuffer.isView(p)) {
      if (p.byteLength === 0)
        return i.alloc(0);
      var f = i.from(p.buffer, p.byteOffset, p.byteLength);
      if (f.byteLength === p.byteLength)
        return f;
    }
    if (n && p instanceof Uint8Array || i.isBuffer(p) && p.constructor && typeof p.constructor.isBuffer == "function" && p.constructor.isBuffer(p))
      return i.from(p);
    throw new TypeError('The "data" argument must be of type string or an instance of Buffer, TypedArray, or DataView.');
  }
  return s.prototype.update = function(p, y, f) {
    var v = o(p, y), m = this._update(v);
    return this.hashMode ? this : (f && (m = this._toString(m, f)), m);
  }, s.prototype.setAutoPadding = function() {
  }, s.prototype.getAuthTag = function() {
    throw new Error("trying to get auth tag in unsupported state");
  }, s.prototype.setAuthTag = function() {
    throw new Error("trying to set auth tag in unsupported state");
  }, s.prototype.setAAD = function() {
    throw new Error("trying to set aad in unsupported state");
  }, s.prototype._transform = function(p, y, f) {
    var v;
    try {
      this.hashMode ? this._update(p) : this.push(this._update(p));
    } catch (m) {
      v = m;
    } finally {
      f(v);
    }
  }, s.prototype._flush = function(p) {
    var y;
    try {
      this.push(this.__final());
    } catch (f) {
      y = f;
    }
    p(y);
  }, s.prototype._finalOrDigest = function(p) {
    var y = this.__final() || i.alloc(0);
    return p && (y = this._toString(y, p, !0)), y;
  }, s.prototype._toString = function(p, y, f) {
    if (this._decoder || (this._decoder = new t(y), this._encoding = y), this._encoding !== y)
      throw new Error("can’t switch encodings");
    var v = this._decoder.write(p);
    return f && (v += this._decoder.end()), v;
  }, Fi = s, Fi;
}
var Ti, xs;
function ch() {
  if (xs) return Ti;
  xs = 1;
  var i = ke().Buffer, e = i.alloc(16, 0);
  function t(n) {
    return [
      n.readUInt32BE(0),
      n.readUInt32BE(4),
      n.readUInt32BE(8),
      n.readUInt32BE(12)
    ];
  }
  function r(n) {
    var u = i.allocUnsafe(16);
    return u.writeUInt32BE(n[0] >>> 0, 0), u.writeUInt32BE(n[1] >>> 0, 4), u.writeUInt32BE(n[2] >>> 0, 8), u.writeUInt32BE(n[3] >>> 0, 12), u;
  }
  function s(n) {
    this.h = n, this.state = i.alloc(16, 0), this.cache = i.allocUnsafe(0);
  }
  return s.prototype.ghash = function(n) {
    for (var u = -1; ++u < n.length; )
      this.state[u] ^= n[u];
    this._multiply();
  }, s.prototype._multiply = function() {
    for (var n = t(this.h), u = [0, 0, 0, 0], o, p, y, f = -1; ++f < 128; ) {
      for (p = (this.state[~~(f / 8)] & 1 << 7 - f % 8) !== 0, p && (u[0] ^= n[0], u[1] ^= n[1], u[2] ^= n[2], u[3] ^= n[3]), y = (n[3] & 1) !== 0, o = 3; o > 0; o--)
        n[o] = n[o] >>> 1 | (n[o - 1] & 1) << 31;
      n[0] = n[0] >>> 1, y && (n[0] = n[0] ^ 225 << 24);
    }
    this.state = r(u);
  }, s.prototype.update = function(n) {
    this.cache = i.concat([this.cache, n]);
    for (var u; this.cache.length >= 16; )
      u = this.cache.slice(0, 16), this.cache = this.cache.slice(16), this.ghash(u);
  }, s.prototype.final = function(n, u) {
    return this.cache.length && this.ghash(i.concat([this.cache, e], 16)), this.ghash(r([0, n, 0, u])), this.state;
  }, Ti = s, Ti;
}
var Ui, vs;
function va() {
  if (vs) return Ui;
  vs = 1;
  var i = $r(), e = ke().Buffer, t = Xt(), r = Te(), s = ch(), n = br(), u = ua();
  function o(f, v) {
    var m = 0;
    f.length !== v.length && m++;
    for (var w = Math.min(f.length, v.length), b = 0; b < w; ++b)
      m += f[b] ^ v[b];
    return m;
  }
  function p(f, v, m) {
    if (v.length === 12)
      return f._finID = e.concat([v, e.from([0, 0, 0, 1])]), e.concat([v, e.from([0, 0, 0, 2])]);
    var w = new s(m), b = v.length, x = b % 16;
    w.update(v), x && (x = 16 - x, w.update(e.alloc(x, 0))), w.update(e.alloc(8, 0));
    var S = b * 8, E = e.alloc(8);
    E.writeUIntBE(S, 0, 8), w.update(E), f._finID = w.state;
    var B = e.from(f._finID);
    return u(B), B;
  }
  function y(f, v, m, w) {
    t.call(this);
    var b = e.alloc(4, 0);
    this._cipher = new i.AES(v);
    var x = this._cipher.encryptBlock(b);
    this._ghash = new s(x), m = p(this, m, x), this._prev = e.from(m), this._cache = e.allocUnsafe(0), this._secCache = e.allocUnsafe(0), this._decrypt = w, this._alen = 0, this._len = 0, this._mode = f, this._authTag = null, this._called = !1;
  }
  return r(y, t), y.prototype._update = function(f) {
    if (!this._called && this._alen) {
      var v = 16 - this._alen % 16;
      v < 16 && (v = e.alloc(v, 0), this._ghash.update(v));
    }
    this._called = !0;
    var m = this._mode.encrypt(this, f);
    return this._decrypt ? this._ghash.update(f) : this._ghash.update(m), this._len += f.length, m;
  }, y.prototype._final = function() {
    if (this._decrypt && !this._authTag) throw new Error("Unsupported state or unable to authenticate data");
    var f = n(this._ghash.final(this._alen * 8, this._len * 8), this._cipher.encryptBlock(this._finID));
    if (this._decrypt && o(f, this._authTag)) throw new Error("Unsupported state or unable to authenticate data");
    this._authTag = f, this._cipher.scrub();
  }, y.prototype.getAuthTag = function() {
    if (this._decrypt || !e.isBuffer(this._authTag)) throw new Error("Attempting to get auth tag in unsupported state");
    return this._authTag;
  }, y.prototype.setAuthTag = function(v) {
    if (!this._decrypt) throw new Error("Attempting to set auth tag in unsupported state");
    this._authTag = v;
  }, y.prototype.setAAD = function(v) {
    if (this._called) throw new Error("Attempting to set AAD in unsupported state");
    this._ghash.update(v), this._alen += v.length;
  }, Ui = y, Ui;
}
var Ri, Es;
function Ea() {
  if (Es) return Ri;
  Es = 1;
  var i = $r(), e = ke().Buffer, t = Xt(), r = Te();
  function s(n, u, o, p) {
    t.call(this), this._cipher = new i.AES(u), this._prev = e.from(o), this._cache = e.allocUnsafe(0), this._secCache = e.allocUnsafe(0), this._decrypt = p, this._mode = n;
  }
  return r(s, t), s.prototype._update = function(n) {
    return this._mode.encrypt(this, n, this._decrypt);
  }, s.prototype._final = function() {
    this._cipher.scrub();
  }, Ri = s, Ri;
}
var Fr = { exports: {} }, Bs;
function lh() {
  return Bs || (Bs = 1, function(i, e) {
    e = i.exports = vn(), e.Stream = e, e.Readable = e, e.Writable = xn(), e.Duplex = $t(), e.Transform = En(), e.PassThrough = ba(), e.finished = Xr(), e.pipeline = xa();
  }(Fr, Fr.exports)), Fr.exports;
}
var Di, Ss;
function Ba() {
  if (Ss) return Di;
  Ss = 1;
  var i = ke().Buffer, e = lh().Transform, t = Te();
  function r(n, u) {
    if (!i.isBuffer(n) && typeof n != "string")
      throw new TypeError(u + " must be a string or a buffer");
  }
  function s(n) {
    e.call(this), this._block = i.allocUnsafe(n), this._blockSize = n, this._blockOffset = 0, this._length = [0, 0, 0, 0], this._finalized = !1;
  }
  return t(s, e), s.prototype._transform = function(n, u, o) {
    var p = null;
    try {
      this.update(n, u);
    } catch (y) {
      p = y;
    }
    o(p);
  }, s.prototype._flush = function(n) {
    var u = null;
    try {
      this.push(this.digest());
    } catch (o) {
      u = o;
    }
    n(u);
  }, s.prototype.update = function(n, u) {
    if (r(n, "Data"), this._finalized) throw new Error("Digest already called");
    i.isBuffer(n) || (n = i.from(n, u));
    for (var o = this._block, p = 0; this._blockOffset + n.length - p >= this._blockSize; ) {
      for (var y = this._blockOffset; y < this._blockSize; ) o[y++] = n[p++];
      this._update(), this._blockOffset = 0;
    }
    for (; p < n.length; ) o[this._blockOffset++] = n[p++];
    for (var f = 0, v = n.length * 8; v > 0; ++f)
      this._length[f] += v, v = this._length[f] / 4294967296 | 0, v > 0 && (this._length[f] -= 4294967296 * v);
    return this;
  }, s.prototype._update = function() {
    throw new Error("_update is not implemented");
  }, s.prototype.digest = function(n) {
    if (this._finalized) throw new Error("Digest already called");
    this._finalized = !0;
    var u = this._digest();
    n !== void 0 && (u = u.toString(n)), this._block.fill(0), this._blockOffset = 0;
    for (var o = 0; o < 4; ++o) this._length[o] = 0;
    return u;
  }, s.prototype._digest = function() {
    throw new Error("_digest is not implemented");
  }, Di = s, Di;
}
var Mi, Is;
function Bn() {
  if (Is) return Mi;
  Is = 1;
  var i = Te(), e = Ba(), t = ke().Buffer, r = new Array(16);
  function s() {
    e.call(this, 64), this._a = 1732584193, this._b = 4023233417, this._c = 2562383102, this._d = 271733878;
  }
  i(s, e), s.prototype._update = function() {
    for (var f = r, v = 0; v < 16; ++v) f[v] = this._block.readInt32LE(v * 4);
    var m = this._a, w = this._b, b = this._c, x = this._d;
    m = u(m, w, b, x, f[0], 3614090360, 7), x = u(x, m, w, b, f[1], 3905402710, 12), b = u(b, x, m, w, f[2], 606105819, 17), w = u(w, b, x, m, f[3], 3250441966, 22), m = u(m, w, b, x, f[4], 4118548399, 7), x = u(x, m, w, b, f[5], 1200080426, 12), b = u(b, x, m, w, f[6], 2821735955, 17), w = u(w, b, x, m, f[7], 4249261313, 22), m = u(m, w, b, x, f[8], 1770035416, 7), x = u(x, m, w, b, f[9], 2336552879, 12), b = u(b, x, m, w, f[10], 4294925233, 17), w = u(w, b, x, m, f[11], 2304563134, 22), m = u(m, w, b, x, f[12], 1804603682, 7), x = u(x, m, w, b, f[13], 4254626195, 12), b = u(b, x, m, w, f[14], 2792965006, 17), w = u(w, b, x, m, f[15], 1236535329, 22), m = o(m, w, b, x, f[1], 4129170786, 5), x = o(x, m, w, b, f[6], 3225465664, 9), b = o(b, x, m, w, f[11], 643717713, 14), w = o(w, b, x, m, f[0], 3921069994, 20), m = o(m, w, b, x, f[5], 3593408605, 5), x = o(x, m, w, b, f[10], 38016083, 9), b = o(b, x, m, w, f[15], 3634488961, 14), w = o(w, b, x, m, f[4], 3889429448, 20), m = o(m, w, b, x, f[9], 568446438, 5), x = o(x, m, w, b, f[14], 3275163606, 9), b = o(b, x, m, w, f[3], 4107603335, 14), w = o(w, b, x, m, f[8], 1163531501, 20), m = o(m, w, b, x, f[13], 2850285829, 5), x = o(x, m, w, b, f[2], 4243563512, 9), b = o(b, x, m, w, f[7], 1735328473, 14), w = o(w, b, x, m, f[12], 2368359562, 20), m = p(m, w, b, x, f[5], 4294588738, 4), x = p(x, m, w, b, f[8], 2272392833, 11), b = p(b, x, m, w, f[11], 1839030562, 16), w = p(w, b, x, m, f[14], 4259657740, 23), m = p(m, w, b, x, f[1], 2763975236, 4), x = p(x, m, w, b, f[4], 1272893353, 11), b = p(b, x, m, w, f[7], 4139469664, 16), w = p(w, b, x, m, f[10], 3200236656, 23), m = p(m, w, b, x, f[13], 681279174, 4), x = p(x, m, w, b, f[0], 3936430074, 11), b = p(b, x, m, w, f[3], 3572445317, 16), w = p(w, b, x, m, f[6], 76029189, 23), m = p(m, w, b, x, f[9], 3654602809, 4), x = p(x, m, w, b, f[12], 3873151461, 11), b = p(b, x, m, w, f[15], 530742520, 16), w = p(w, b, x, m, f[2], 3299628645, 23), m = y(m, w, b, x, f[0], 4096336452, 6), x = y(x, m, w, b, f[7], 1126891415, 10), b = y(b, x, m, w, f[14], 2878612391, 15), w = y(w, b, x, m, f[5], 4237533241, 21), m = y(m, w, b, x, f[12], 1700485571, 6), x = y(x, m, w, b, f[3], 2399980690, 10), b = y(b, x, m, w, f[10], 4293915773, 15), w = y(w, b, x, m, f[1], 2240044497, 21), m = y(m, w, b, x, f[8], 1873313359, 6), x = y(x, m, w, b, f[15], 4264355552, 10), b = y(b, x, m, w, f[6], 2734768916, 15), w = y(w, b, x, m, f[13], 1309151649, 21), m = y(m, w, b, x, f[4], 4149444226, 6), x = y(x, m, w, b, f[11], 3174756917, 10), b = y(b, x, m, w, f[2], 718787259, 15), w = y(w, b, x, m, f[9], 3951481745, 21), this._a = this._a + m | 0, this._b = this._b + w | 0, this._c = this._c + b | 0, this._d = this._d + x | 0;
  }, s.prototype._digest = function() {
    this._block[this._blockOffset++] = 128, this._blockOffset > 56 && (this._block.fill(0, this._blockOffset, 64), this._update(), this._blockOffset = 0), this._block.fill(0, this._blockOffset, 56), this._block.writeUInt32LE(this._length[0], 56), this._block.writeUInt32LE(this._length[1], 60), this._update();
    var f = t.allocUnsafe(16);
    return f.writeInt32LE(this._a, 0), f.writeInt32LE(this._b, 4), f.writeInt32LE(this._c, 8), f.writeInt32LE(this._d, 12), f;
  };
  function n(f, v) {
    return f << v | f >>> 32 - v;
  }
  function u(f, v, m, w, b, x, S) {
    return n(f + (v & m | ~v & w) + b + x | 0, S) + v | 0;
  }
  function o(f, v, m, w, b, x, S) {
    return n(f + (v & w | m & ~w) + b + x | 0, S) + v | 0;
  }
  function p(f, v, m, w, b, x, S) {
    return n(f + (v ^ m ^ w) + b + x | 0, S) + v | 0;
  }
  function y(f, v, m, w, b, x, S) {
    return n(f + (m ^ (v | ~w)) + b + x | 0, S) + v | 0;
  }
  return Mi = s, Mi;
}
var Pi, Cs;
function Sa() {
  if (Cs) return Pi;
  Cs = 1;
  var i = ke().Buffer, e = Bn();
  function t(r, s, n, u) {
    if (i.isBuffer(r) || (r = i.from(r, "binary")), s && (i.isBuffer(s) || (s = i.from(s, "binary")), s.length !== 8))
      throw new RangeError("salt should be Buffer with 8 byte length");
    for (var o = n / 8, p = i.alloc(o), y = i.alloc(u || 0), f = i.alloc(0); o > 0 || u > 0; ) {
      var v = new e();
      v.update(f), v.update(r), s && v.update(s), f = v.digest();
      var m = 0;
      if (o > 0) {
        var w = p.length - o;
        m = Math.min(o, f.length), f.copy(p, w, 0, m), o -= m;
      }
      if (m < f.length && u > 0) {
        var b = y.length - u, x = Math.min(u, f.length - m);
        f.copy(y, b, m, m + x), u -= x;
      }
    }
    return f.fill(0), { key: p, iv: y };
  }
  return Pi = t, Pi;
}
var As;
function dh() {
  if (As) return Er;
  As = 1;
  var i = ca(), e = va(), t = ke().Buffer, r = Ea(), s = Xt(), n = $r(), u = Sa(), o = Te();
  function p(w, b, x) {
    s.call(this), this._cache = new f(), this._cipher = new n.AES(b), this._prev = t.from(x), this._mode = w, this._autopadding = !0;
  }
  o(p, s), p.prototype._update = function(w) {
    this._cache.add(w);
    for (var b, x, S = []; b = this._cache.get(); )
      x = this._mode.encrypt(this, b), S.push(x);
    return t.concat(S);
  };
  var y = t.alloc(16, 16);
  p.prototype._final = function() {
    var w = this._cache.flush();
    if (this._autopadding)
      return w = this._mode.encrypt(this, w), this._cipher.scrub(), w;
    if (!w.equals(y))
      throw this._cipher.scrub(), new Error("data not multiple of block length");
  }, p.prototype.setAutoPadding = function(w) {
    return this._autopadding = !!w, this;
  };
  function f() {
    this.cache = t.allocUnsafe(0);
  }
  f.prototype.add = function(w) {
    this.cache = t.concat([this.cache, w]);
  }, f.prototype.get = function() {
    if (this.cache.length > 15) {
      var w = this.cache.slice(0, 16);
      return this.cache = this.cache.slice(16), w;
    }
    return null;
  }, f.prototype.flush = function() {
    for (var w = 16 - this.cache.length, b = t.allocUnsafe(w), x = -1; ++x < w; )
      b.writeUInt8(w, x);
    return t.concat([this.cache, b]);
  };
  function v(w, b, x) {
    var S = i[w.toLowerCase()];
    if (!S) throw new TypeError("invalid suite type");
    if (typeof b == "string" && (b = t.from(b)), b.length !== S.key / 8) throw new TypeError("invalid key length " + b.length);
    if (typeof x == "string" && (x = t.from(x)), S.mode !== "GCM" && x.length !== S.iv) throw new TypeError("invalid iv length " + x.length);
    return S.type === "stream" ? new r(S.module, b, x) : S.type === "auth" ? new e(S.module, b, x) : new p(S.module, b, x);
  }
  function m(w, b) {
    var x = i[w.toLowerCase()];
    if (!x) throw new TypeError("invalid suite type");
    var S = u(b, !1, x.key, x.iv);
    return v(w, S.key, S.iv);
  }
  return Er.createCipheriv = v, Er.createCipher = m, Er;
}
var Tr = {}, ks;
function ph() {
  if (ks) return Tr;
  ks = 1;
  var i = va(), e = ke().Buffer, t = ca(), r = Ea(), s = Xt(), n = $r(), u = Sa(), o = Te();
  function p(w, b, x) {
    s.call(this), this._cache = new y(), this._last = void 0, this._cipher = new n.AES(b), this._prev = e.from(x), this._mode = w, this._autopadding = !0;
  }
  o(p, s), p.prototype._update = function(w) {
    this._cache.add(w);
    for (var b, x, S = []; b = this._cache.get(this._autopadding); )
      x = this._mode.decrypt(this, b), S.push(x);
    return e.concat(S);
  }, p.prototype._final = function() {
    var w = this._cache.flush();
    if (this._autopadding)
      return f(this._mode.decrypt(this, w));
    if (w)
      throw new Error("data not multiple of block length");
  }, p.prototype.setAutoPadding = function(w) {
    return this._autopadding = !!w, this;
  };
  function y() {
    this.cache = e.allocUnsafe(0);
  }
  y.prototype.add = function(w) {
    this.cache = e.concat([this.cache, w]);
  }, y.prototype.get = function(w) {
    var b;
    if (w) {
      if (this.cache.length > 16)
        return b = this.cache.slice(0, 16), this.cache = this.cache.slice(16), b;
    } else if (this.cache.length >= 16)
      return b = this.cache.slice(0, 16), this.cache = this.cache.slice(16), b;
    return null;
  }, y.prototype.flush = function() {
    if (this.cache.length) return this.cache;
  };
  function f(w) {
    var b = w[15];
    if (b < 1 || b > 16)
      throw new Error("unable to decrypt data");
    for (var x = -1; ++x < b; )
      if (w[x + (16 - b)] !== b)
        throw new Error("unable to decrypt data");
    if (b !== 16)
      return w.slice(0, 16 - b);
  }
  function v(w, b, x) {
    var S = t[w.toLowerCase()];
    if (!S) throw new TypeError("invalid suite type");
    if (typeof x == "string" && (x = e.from(x)), S.mode !== "GCM" && x.length !== S.iv) throw new TypeError("invalid iv length " + x.length);
    if (typeof b == "string" && (b = e.from(b)), b.length !== S.key / 8) throw new TypeError("invalid key length " + b.length);
    return S.type === "stream" ? new r(S.module, b, x, !0) : S.type === "auth" ? new i(S.module, b, x, !0) : new p(S.module, b, x);
  }
  function m(w, b) {
    var x = t[w.toLowerCase()];
    if (!x) throw new TypeError("invalid suite type");
    var S = u(b, !1, x.key, x.iv);
    return v(w, S.key, S.iv);
  }
  return Tr.createDecipher = m, Tr.createDecipheriv = v, Tr;
}
var Fs;
function yh() {
  if (Fs) return ot;
  Fs = 1;
  var i = dh(), e = ph(), t = fa;
  function r() {
    return Object.keys(t);
  }
  return ot.createCipher = ot.Cipher = i.createCipher, ot.createCipheriv = ot.Cipheriv = i.createCipheriv, ot.createDecipher = ot.Decipher = e.createDecipher, ot.createDecipheriv = ot.Decipheriv = e.createDecipheriv, ot.listCiphers = ot.getCiphers = r, ot;
}
var gh = yh();
const Ia = /* @__PURE__ */ _n(gh);
var Li, Ts;
function Ca() {
  if (Ts) return Li;
  Ts = 1;
  var i = xr().Buffer, e = Te(), t = Ba(), r = new Array(16), s = [
    0,
    1,
    2,
    3,
    4,
    5,
    6,
    7,
    8,
    9,
    10,
    11,
    12,
    13,
    14,
    15,
    7,
    4,
    13,
    1,
    10,
    6,
    15,
    3,
    12,
    0,
    9,
    5,
    2,
    14,
    11,
    8,
    3,
    10,
    14,
    4,
    9,
    15,
    8,
    1,
    2,
    7,
    0,
    6,
    13,
    11,
    5,
    12,
    1,
    9,
    11,
    10,
    0,
    8,
    12,
    4,
    13,
    3,
    7,
    15,
    14,
    5,
    6,
    2,
    4,
    0,
    5,
    9,
    7,
    12,
    2,
    10,
    14,
    1,
    3,
    8,
    11,
    6,
    15,
    13
  ], n = [
    5,
    14,
    7,
    0,
    9,
    2,
    11,
    4,
    13,
    6,
    15,
    8,
    1,
    10,
    3,
    12,
    6,
    11,
    3,
    7,
    0,
    13,
    5,
    10,
    14,
    15,
    8,
    12,
    4,
    9,
    1,
    2,
    15,
    5,
    1,
    3,
    7,
    14,
    6,
    9,
    11,
    8,
    12,
    2,
    10,
    0,
    4,
    13,
    8,
    6,
    4,
    1,
    3,
    11,
    15,
    0,
    5,
    12,
    2,
    13,
    9,
    7,
    10,
    14,
    12,
    15,
    10,
    4,
    1,
    5,
    8,
    7,
    6,
    2,
    13,
    14,
    0,
    3,
    9,
    11
  ], u = [
    11,
    14,
    15,
    12,
    5,
    8,
    7,
    9,
    11,
    13,
    14,
    15,
    6,
    7,
    9,
    8,
    7,
    6,
    8,
    13,
    11,
    9,
    7,
    15,
    7,
    12,
    15,
    9,
    11,
    7,
    13,
    12,
    11,
    13,
    6,
    7,
    14,
    9,
    13,
    15,
    14,
    8,
    13,
    6,
    5,
    12,
    7,
    5,
    11,
    12,
    14,
    15,
    14,
    15,
    9,
    8,
    9,
    14,
    5,
    6,
    8,
    6,
    5,
    12,
    9,
    15,
    5,
    11,
    6,
    8,
    13,
    12,
    5,
    12,
    13,
    14,
    11,
    8,
    5,
    6
  ], o = [
    8,
    9,
    9,
    11,
    13,
    15,
    15,
    5,
    7,
    7,
    8,
    11,
    14,
    14,
    12,
    6,
    9,
    13,
    15,
    7,
    12,
    8,
    9,
    11,
    7,
    7,
    12,
    7,
    6,
    15,
    13,
    11,
    9,
    7,
    15,
    11,
    8,
    6,
    6,
    14,
    12,
    13,
    5,
    14,
    13,
    13,
    7,
    5,
    15,
    5,
    8,
    11,
    14,
    14,
    6,
    14,
    6,
    9,
    12,
    9,
    12,
    5,
    15,
    8,
    8,
    5,
    12,
    9,
    12,
    5,
    14,
    6,
    8,
    13,
    6,
    5,
    15,
    13,
    11,
    11
  ], p = [0, 1518500249, 1859775393, 2400959708, 2840853838], y = [1352829926, 1548603684, 1836072691, 2053994217, 0];
  function f() {
    t.call(this, 64), this._a = 1732584193, this._b = 4023233417, this._c = 2562383102, this._d = 271733878, this._e = 3285377520;
  }
  e(f, t), f.prototype._update = function() {
    for (var E = r, B = 0; B < 16; ++B) E[B] = this._block.readInt32LE(B * 4);
    for (var A = this._a | 0, k = this._b | 0, T = this._c | 0, D = this._d | 0, q = this._e | 0, R = this._a | 0, G = this._b | 0, J = this._c | 0, se = this._d | 0, Z = this._e | 0, X = 0; X < 80; X += 1) {
      var ee, re;
      X < 16 ? (ee = m(A, k, T, D, q, E[s[X]], p[0], u[X]), re = S(R, G, J, se, Z, E[n[X]], y[0], o[X])) : X < 32 ? (ee = w(A, k, T, D, q, E[s[X]], p[1], u[X]), re = x(R, G, J, se, Z, E[n[X]], y[1], o[X])) : X < 48 ? (ee = b(A, k, T, D, q, E[s[X]], p[2], u[X]), re = b(R, G, J, se, Z, E[n[X]], y[2], o[X])) : X < 64 ? (ee = x(A, k, T, D, q, E[s[X]], p[3], u[X]), re = w(R, G, J, se, Z, E[n[X]], y[3], o[X])) : (ee = S(A, k, T, D, q, E[s[X]], p[4], u[X]), re = m(R, G, J, se, Z, E[n[X]], y[4], o[X])), A = q, q = D, D = v(T, 10), T = k, k = ee, R = Z, Z = se, se = v(J, 10), J = G, G = re;
    }
    var ie = this._b + T + se | 0;
    this._b = this._c + D + Z | 0, this._c = this._d + q + R | 0, this._d = this._e + A + G | 0, this._e = this._a + k + J | 0, this._a = ie;
  }, f.prototype._digest = function() {
    this._block[this._blockOffset++] = 128, this._blockOffset > 56 && (this._block.fill(0, this._blockOffset, 64), this._update(), this._blockOffset = 0), this._block.fill(0, this._blockOffset, 56), this._block.writeUInt32LE(this._length[0], 56), this._block.writeUInt32LE(this._length[1], 60), this._update();
    var E = i.alloc ? i.alloc(20) : new i(20);
    return E.writeInt32LE(this._a, 0), E.writeInt32LE(this._b, 4), E.writeInt32LE(this._c, 8), E.writeInt32LE(this._d, 12), E.writeInt32LE(this._e, 16), E;
  };
  function v(E, B) {
    return E << B | E >>> 32 - B;
  }
  function m(E, B, A, k, T, D, q, R) {
    return v(E + (B ^ A ^ k) + D + q | 0, R) + T | 0;
  }
  function w(E, B, A, k, T, D, q, R) {
    return v(E + (B & A | ~B & k) + D + q | 0, R) + T | 0;
  }
  function b(E, B, A, k, T, D, q, R) {
    return v(E + ((B | ~A) ^ k) + D + q | 0, R) + T | 0;
  }
  function x(E, B, A, k, T, D, q, R) {
    return v(E + (B & k | A & ~k) + D + q | 0, R) + T | 0;
  }
  function S(E, B, A, k, T, D, q, R) {
    return v(E + (B ^ (A | ~k)) + D + q | 0, R) + T | 0;
  }
  return Li = f, Li;
}
var Ni = { exports: {} }, Oi, Us;
function hr() {
  if (Us) return Oi;
  Us = 1;
  var i = ke().Buffer;
  function e(t, r) {
    this._block = i.alloc(t), this._finalSize = r, this._blockSize = t, this._len = 0;
  }
  return e.prototype.update = function(t, r) {
    typeof t == "string" && (r = r || "utf8", t = i.from(t, r));
    for (var s = this._block, n = this._blockSize, u = t.length, o = this._len, p = 0; p < u; ) {
      for (var y = o % n, f = Math.min(u - p, n - y), v = 0; v < f; v++)
        s[y + v] = t[p + v];
      o += f, p += f, o % n === 0 && this._update(s);
    }
    return this._len += u, this;
  }, e.prototype.digest = function(t) {
    var r = this._len % this._blockSize;
    this._block[r] = 128, this._block.fill(0, r + 1), r >= this._finalSize && (this._update(this._block), this._block.fill(0));
    var s = this._len * 8;
    if (s <= 4294967295)
      this._block.writeUInt32BE(s, this._blockSize - 4);
    else {
      var n = (s & 4294967295) >>> 0, u = (s - n) / 4294967296;
      this._block.writeUInt32BE(u, this._blockSize - 8), this._block.writeUInt32BE(n, this._blockSize - 4);
    }
    this._update(this._block);
    var o = this._hash();
    return t ? o.toString(t) : o;
  }, e.prototype._update = function() {
    throw new Error("_update must be implemented by subclass");
  }, Oi = e, Oi;
}
var ji, Rs;
function wh() {
  if (Rs) return ji;
  Rs = 1;
  var i = Te(), e = hr(), t = ke().Buffer, r = [
    1518500249,
    1859775393,
    -1894007588,
    -899497514
  ], s = new Array(80);
  function n() {
    this.init(), this._w = s, e.call(this, 64, 56);
  }
  i(n, e), n.prototype.init = function() {
    return this._a = 1732584193, this._b = 4023233417, this._c = 2562383102, this._d = 271733878, this._e = 3285377520, this;
  };
  function u(y) {
    return y << 5 | y >>> 27;
  }
  function o(y) {
    return y << 30 | y >>> 2;
  }
  function p(y, f, v, m) {
    return y === 0 ? f & v | ~f & m : y === 2 ? f & v | f & m | v & m : f ^ v ^ m;
  }
  return n.prototype._update = function(y) {
    for (var f = this._w, v = this._a | 0, m = this._b | 0, w = this._c | 0, b = this._d | 0, x = this._e | 0, S = 0; S < 16; ++S) f[S] = y.readInt32BE(S * 4);
    for (; S < 80; ++S) f[S] = f[S - 3] ^ f[S - 8] ^ f[S - 14] ^ f[S - 16];
    for (var E = 0; E < 80; ++E) {
      var B = ~~(E / 20), A = u(v) + p(B, m, w, b) + x + f[E] + r[B] | 0;
      x = b, b = w, w = o(m), m = v, v = A;
    }
    this._a = v + this._a | 0, this._b = m + this._b | 0, this._c = w + this._c | 0, this._d = b + this._d | 0, this._e = x + this._e | 0;
  }, n.prototype._hash = function() {
    var y = t.allocUnsafe(20);
    return y.writeInt32BE(this._a | 0, 0), y.writeInt32BE(this._b | 0, 4), y.writeInt32BE(this._c | 0, 8), y.writeInt32BE(this._d | 0, 12), y.writeInt32BE(this._e | 0, 16), y;
  }, ji = n, ji;
}
var Hi, Ds;
function mh() {
  if (Ds) return Hi;
  Ds = 1;
  var i = Te(), e = hr(), t = ke().Buffer, r = [
    1518500249,
    1859775393,
    -1894007588,
    -899497514
  ], s = new Array(80);
  function n() {
    this.init(), this._w = s, e.call(this, 64, 56);
  }
  i(n, e), n.prototype.init = function() {
    return this._a = 1732584193, this._b = 4023233417, this._c = 2562383102, this._d = 271733878, this._e = 3285377520, this;
  };
  function u(f) {
    return f << 1 | f >>> 31;
  }
  function o(f) {
    return f << 5 | f >>> 27;
  }
  function p(f) {
    return f << 30 | f >>> 2;
  }
  function y(f, v, m, w) {
    return f === 0 ? v & m | ~v & w : f === 2 ? v & m | v & w | m & w : v ^ m ^ w;
  }
  return n.prototype._update = function(f) {
    for (var v = this._w, m = this._a | 0, w = this._b | 0, b = this._c | 0, x = this._d | 0, S = this._e | 0, E = 0; E < 16; ++E) v[E] = f.readInt32BE(E * 4);
    for (; E < 80; ++E) v[E] = u(v[E - 3] ^ v[E - 8] ^ v[E - 14] ^ v[E - 16]);
    for (var B = 0; B < 80; ++B) {
      var A = ~~(B / 20), k = o(m) + y(A, w, b, x) + S + v[B] + r[A] | 0;
      S = x, x = b, b = p(w), w = m, m = k;
    }
    this._a = m + this._a | 0, this._b = w + this._b | 0, this._c = b + this._c | 0, this._d = x + this._d | 0, this._e = S + this._e | 0;
  }, n.prototype._hash = function() {
    var f = t.allocUnsafe(20);
    return f.writeInt32BE(this._a | 0, 0), f.writeInt32BE(this._b | 0, 4), f.writeInt32BE(this._c | 0, 8), f.writeInt32BE(this._d | 0, 12), f.writeInt32BE(this._e | 0, 16), f;
  }, Hi = n, Hi;
}
var qi, Ms;
function Aa() {
  if (Ms) return qi;
  Ms = 1;
  var i = Te(), e = hr(), t = ke().Buffer, r = [
    1116352408,
    1899447441,
    3049323471,
    3921009573,
    961987163,
    1508970993,
    2453635748,
    2870763221,
    3624381080,
    310598401,
    607225278,
    1426881987,
    1925078388,
    2162078206,
    2614888103,
    3248222580,
    3835390401,
    4022224774,
    264347078,
    604807628,
    770255983,
    1249150122,
    1555081692,
    1996064986,
    2554220882,
    2821834349,
    2952996808,
    3210313671,
    3336571891,
    3584528711,
    113926993,
    338241895,
    666307205,
    773529912,
    1294757372,
    1396182291,
    1695183700,
    1986661051,
    2177026350,
    2456956037,
    2730485921,
    2820302411,
    3259730800,
    3345764771,
    3516065817,
    3600352804,
    4094571909,
    275423344,
    430227734,
    506948616,
    659060556,
    883997877,
    958139571,
    1322822218,
    1537002063,
    1747873779,
    1955562222,
    2024104815,
    2227730452,
    2361852424,
    2428436474,
    2756734187,
    3204031479,
    3329325298
  ], s = new Array(64);
  function n() {
    this.init(), this._w = s, e.call(this, 64, 56);
  }
  i(n, e), n.prototype.init = function() {
    return this._a = 1779033703, this._b = 3144134277, this._c = 1013904242, this._d = 2773480762, this._e = 1359893119, this._f = 2600822924, this._g = 528734635, this._h = 1541459225, this;
  };
  function u(m, w, b) {
    return b ^ m & (w ^ b);
  }
  function o(m, w, b) {
    return m & w | b & (m | w);
  }
  function p(m) {
    return (m >>> 2 | m << 30) ^ (m >>> 13 | m << 19) ^ (m >>> 22 | m << 10);
  }
  function y(m) {
    return (m >>> 6 | m << 26) ^ (m >>> 11 | m << 21) ^ (m >>> 25 | m << 7);
  }
  function f(m) {
    return (m >>> 7 | m << 25) ^ (m >>> 18 | m << 14) ^ m >>> 3;
  }
  function v(m) {
    return (m >>> 17 | m << 15) ^ (m >>> 19 | m << 13) ^ m >>> 10;
  }
  return n.prototype._update = function(m) {
    for (var w = this._w, b = this._a | 0, x = this._b | 0, S = this._c | 0, E = this._d | 0, B = this._e | 0, A = this._f | 0, k = this._g | 0, T = this._h | 0, D = 0; D < 16; ++D) w[D] = m.readInt32BE(D * 4);
    for (; D < 64; ++D) w[D] = v(w[D - 2]) + w[D - 7] + f(w[D - 15]) + w[D - 16] | 0;
    for (var q = 0; q < 64; ++q) {
      var R = T + y(B) + u(B, A, k) + r[q] + w[q] | 0, G = p(b) + o(b, x, S) | 0;
      T = k, k = A, A = B, B = E + R | 0, E = S, S = x, x = b, b = R + G | 0;
    }
    this._a = b + this._a | 0, this._b = x + this._b | 0, this._c = S + this._c | 0, this._d = E + this._d | 0, this._e = B + this._e | 0, this._f = A + this._f | 0, this._g = k + this._g | 0, this._h = T + this._h | 0;
  }, n.prototype._hash = function() {
    var m = t.allocUnsafe(32);
    return m.writeInt32BE(this._a, 0), m.writeInt32BE(this._b, 4), m.writeInt32BE(this._c, 8), m.writeInt32BE(this._d, 12), m.writeInt32BE(this._e, 16), m.writeInt32BE(this._f, 20), m.writeInt32BE(this._g, 24), m.writeInt32BE(this._h, 28), m;
  }, qi = n, qi;
}
var Vi, Ps;
function _h() {
  if (Ps) return Vi;
  Ps = 1;
  var i = Te(), e = Aa(), t = hr(), r = ke().Buffer, s = new Array(64);
  function n() {
    this.init(), this._w = s, t.call(this, 64, 56);
  }
  return i(n, e), n.prototype.init = function() {
    return this._a = 3238371032, this._b = 914150663, this._c = 812702999, this._d = 4144912697, this._e = 4290775857, this._f = 1750603025, this._g = 1694076839, this._h = 3204075428, this;
  }, n.prototype._hash = function() {
    var u = r.allocUnsafe(28);
    return u.writeInt32BE(this._a, 0), u.writeInt32BE(this._b, 4), u.writeInt32BE(this._c, 8), u.writeInt32BE(this._d, 12), u.writeInt32BE(this._e, 16), u.writeInt32BE(this._f, 20), u.writeInt32BE(this._g, 24), u;
  }, Vi = n, Vi;
}
var Gi, Ls;
function ka() {
  if (Ls) return Gi;
  Ls = 1;
  var i = Te(), e = hr(), t = ke().Buffer, r = [
    1116352408,
    3609767458,
    1899447441,
    602891725,
    3049323471,
    3964484399,
    3921009573,
    2173295548,
    961987163,
    4081628472,
    1508970993,
    3053834265,
    2453635748,
    2937671579,
    2870763221,
    3664609560,
    3624381080,
    2734883394,
    310598401,
    1164996542,
    607225278,
    1323610764,
    1426881987,
    3590304994,
    1925078388,
    4068182383,
    2162078206,
    991336113,
    2614888103,
    633803317,
    3248222580,
    3479774868,
    3835390401,
    2666613458,
    4022224774,
    944711139,
    264347078,
    2341262773,
    604807628,
    2007800933,
    770255983,
    1495990901,
    1249150122,
    1856431235,
    1555081692,
    3175218132,
    1996064986,
    2198950837,
    2554220882,
    3999719339,
    2821834349,
    766784016,
    2952996808,
    2566594879,
    3210313671,
    3203337956,
    3336571891,
    1034457026,
    3584528711,
    2466948901,
    113926993,
    3758326383,
    338241895,
    168717936,
    666307205,
    1188179964,
    773529912,
    1546045734,
    1294757372,
    1522805485,
    1396182291,
    2643833823,
    1695183700,
    2343527390,
    1986661051,
    1014477480,
    2177026350,
    1206759142,
    2456956037,
    344077627,
    2730485921,
    1290863460,
    2820302411,
    3158454273,
    3259730800,
    3505952657,
    3345764771,
    106217008,
    3516065817,
    3606008344,
    3600352804,
    1432725776,
    4094571909,
    1467031594,
    275423344,
    851169720,
    430227734,
    3100823752,
    506948616,
    1363258195,
    659060556,
    3750685593,
    883997877,
    3785050280,
    958139571,
    3318307427,
    1322822218,
    3812723403,
    1537002063,
    2003034995,
    1747873779,
    3602036899,
    1955562222,
    1575990012,
    2024104815,
    1125592928,
    2227730452,
    2716904306,
    2361852424,
    442776044,
    2428436474,
    593698344,
    2756734187,
    3733110249,
    3204031479,
    2999351573,
    3329325298,
    3815920427,
    3391569614,
    3928383900,
    3515267271,
    566280711,
    3940187606,
    3454069534,
    4118630271,
    4000239992,
    116418474,
    1914138554,
    174292421,
    2731055270,
    289380356,
    3203993006,
    460393269,
    320620315,
    685471733,
    587496836,
    852142971,
    1086792851,
    1017036298,
    365543100,
    1126000580,
    2618297676,
    1288033470,
    3409855158,
    1501505948,
    4234509866,
    1607167915,
    987167468,
    1816402316,
    1246189591
  ], s = new Array(160);
  function n() {
    this.init(), this._w = s, e.call(this, 128, 112);
  }
  i(n, e), n.prototype.init = function() {
    return this._ah = 1779033703, this._bh = 3144134277, this._ch = 1013904242, this._dh = 2773480762, this._eh = 1359893119, this._fh = 2600822924, this._gh = 528734635, this._hh = 1541459225, this._al = 4089235720, this._bl = 2227873595, this._cl = 4271175723, this._dl = 1595750129, this._el = 2917565137, this._fl = 725511199, this._gl = 4215389547, this._hl = 327033209, this;
  };
  function u(x, S, E) {
    return E ^ x & (S ^ E);
  }
  function o(x, S, E) {
    return x & S | E & (x | S);
  }
  function p(x, S) {
    return (x >>> 28 | S << 4) ^ (S >>> 2 | x << 30) ^ (S >>> 7 | x << 25);
  }
  function y(x, S) {
    return (x >>> 14 | S << 18) ^ (x >>> 18 | S << 14) ^ (S >>> 9 | x << 23);
  }
  function f(x, S) {
    return (x >>> 1 | S << 31) ^ (x >>> 8 | S << 24) ^ x >>> 7;
  }
  function v(x, S) {
    return (x >>> 1 | S << 31) ^ (x >>> 8 | S << 24) ^ (x >>> 7 | S << 25);
  }
  function m(x, S) {
    return (x >>> 19 | S << 13) ^ (S >>> 29 | x << 3) ^ x >>> 6;
  }
  function w(x, S) {
    return (x >>> 19 | S << 13) ^ (S >>> 29 | x << 3) ^ (x >>> 6 | S << 26);
  }
  function b(x, S) {
    return x >>> 0 < S >>> 0 ? 1 : 0;
  }
  return n.prototype._update = function(x) {
    for (var S = this._w, E = this._ah | 0, B = this._bh | 0, A = this._ch | 0, k = this._dh | 0, T = this._eh | 0, D = this._fh | 0, q = this._gh | 0, R = this._hh | 0, G = this._al | 0, J = this._bl | 0, se = this._cl | 0, Z = this._dl | 0, X = this._el | 0, ee = this._fl | 0, re = this._gl | 0, ie = this._hl | 0, N = 0; N < 32; N += 2)
      S[N] = x.readInt32BE(N * 4), S[N + 1] = x.readInt32BE(N * 4 + 4);
    for (; N < 160; N += 2) {
      var ye = S[N - 30], ge = S[N - 15 * 2 + 1], Me = f(ye, ge), ue = v(ge, ye);
      ye = S[N - 2 * 2], ge = S[N - 2 * 2 + 1];
      var le = m(ye, ge), Ee = w(ge, ye), he = S[N - 7 * 2], fe = S[N - 7 * 2 + 1], L = S[N - 16 * 2], P = S[N - 16 * 2 + 1], V = ue + fe | 0, z = Me + he + b(V, ue) | 0;
      V = V + Ee | 0, z = z + le + b(V, Ee) | 0, V = V + P | 0, z = z + L + b(V, P) | 0, S[N] = z, S[N + 1] = V;
    }
    for (var Q = 0; Q < 160; Q += 2) {
      z = S[Q], V = S[Q + 1];
      var I = o(E, B, A), C = o(G, J, se), K = p(E, G), $ = p(G, E), we = y(T, X), ne = y(X, T), ce = r[Q], Qe = r[Q + 1], mt = u(T, D, q), st = u(X, ee, re), Ue = ie + ne | 0, Ce = R + we + b(Ue, ie) | 0;
      Ue = Ue + st | 0, Ce = Ce + mt + b(Ue, st) | 0, Ue = Ue + Qe | 0, Ce = Ce + ce + b(Ue, Qe) | 0, Ue = Ue + V | 0, Ce = Ce + z + b(Ue, V) | 0;
      var et = $ + C | 0, at = K + I + b(et, $) | 0;
      R = q, ie = re, q = D, re = ee, D = T, ee = X, X = Z + Ue | 0, T = k + Ce + b(X, Z) | 0, k = A, Z = se, A = B, se = J, B = E, J = G, G = Ue + et | 0, E = Ce + at + b(G, Ue) | 0;
    }
    this._al = this._al + G | 0, this._bl = this._bl + J | 0, this._cl = this._cl + se | 0, this._dl = this._dl + Z | 0, this._el = this._el + X | 0, this._fl = this._fl + ee | 0, this._gl = this._gl + re | 0, this._hl = this._hl + ie | 0, this._ah = this._ah + E + b(this._al, G) | 0, this._bh = this._bh + B + b(this._bl, J) | 0, this._ch = this._ch + A + b(this._cl, se) | 0, this._dh = this._dh + k + b(this._dl, Z) | 0, this._eh = this._eh + T + b(this._el, X) | 0, this._fh = this._fh + D + b(this._fl, ee) | 0, this._gh = this._gh + q + b(this._gl, re) | 0, this._hh = this._hh + R + b(this._hl, ie) | 0;
  }, n.prototype._hash = function() {
    var x = t.allocUnsafe(64);
    function S(E, B, A) {
      x.writeInt32BE(E, A), x.writeInt32BE(B, A + 4);
    }
    return S(this._ah, this._al, 0), S(this._bh, this._bl, 8), S(this._ch, this._cl, 16), S(this._dh, this._dl, 24), S(this._eh, this._el, 32), S(this._fh, this._fl, 40), S(this._gh, this._gl, 48), S(this._hh, this._hl, 56), x;
  }, Gi = n, Gi;
}
var Ki, Ns;
function bh() {
  if (Ns) return Ki;
  Ns = 1;
  var i = Te(), e = ka(), t = hr(), r = ke().Buffer, s = new Array(160);
  function n() {
    this.init(), this._w = s, t.call(this, 128, 112);
  }
  return i(n, e), n.prototype.init = function() {
    return this._ah = 3418070365, this._bh = 1654270250, this._ch = 2438529370, this._dh = 355462360, this._eh = 1731405415, this._fh = 2394180231, this._gh = 3675008525, this._hh = 1203062813, this._al = 3238371032, this._bl = 914150663, this._cl = 812702999, this._dl = 4144912697, this._el = 4290775857, this._fl = 1750603025, this._gl = 1694076839, this._hl = 3204075428, this;
  }, n.prototype._hash = function() {
    var u = r.allocUnsafe(48);
    function o(p, y, f) {
      u.writeInt32BE(p, f), u.writeInt32BE(y, f + 4);
    }
    return o(this._ah, this._al, 0), o(this._bh, this._bl, 8), o(this._ch, this._cl, 16), o(this._dh, this._dl, 24), o(this._eh, this._el, 32), o(this._fh, this._fl, 40), u;
  }, Ki = n, Ki;
}
var Os;
function Fa() {
  if (Os) return Ni.exports;
  Os = 1;
  var i = Ni.exports = function(t) {
    t = t.toLowerCase();
    var r = i[t];
    if (!r) throw new Error(t + " is not supported (we accept pull requests)");
    return new r();
  };
  return i.sha = wh(), i.sha1 = mh(), i.sha224 = _h(), i.sha256 = Aa(), i.sha384 = bh(), i.sha512 = ka(), Ni.exports;
}
var Wi, js;
function xh() {
  if (js) return Wi;
  js = 1;
  var i = Te(), e = Bn(), t = Ca(), r = Fa(), s = Xt();
  function n(u) {
    s.call(this, "digest"), this._hash = u;
  }
  return i(n, s), n.prototype._update = function(u) {
    this._hash.update(u);
  }, n.prototype._final = function() {
    return this._hash.digest();
  }, Wi = function(o) {
    return o = o.toLowerCase(), o === "md5" ? new e() : o === "rmd160" || o === "ripemd160" ? new t() : new n(r(o));
  }, Wi;
}
var vh = xh();
const Ta = /* @__PURE__ */ _n(vh);
var $i, Hs;
function Eh() {
  if (Hs) return $i;
  Hs = 1;
  var i = Te(), e = ke().Buffer, t = Xt(), r = e.alloc(128), s = 64;
  function n(u, o) {
    t.call(this, "digest"), typeof o == "string" && (o = e.from(o)), this._alg = u, this._key = o, o.length > s ? o = u(o) : o.length < s && (o = e.concat([o, r], s));
    for (var p = this._ipad = e.allocUnsafe(s), y = this._opad = e.allocUnsafe(s), f = 0; f < s; f++)
      p[f] = o[f] ^ 54, y[f] = o[f] ^ 92;
    this._hash = [p];
  }
  return i(n, t), n.prototype._update = function(u) {
    this._hash.push(u);
  }, n.prototype._final = function() {
    var u = this._alg(e.concat(this._hash));
    return this._alg(e.concat([this._opad, u]));
  }, $i = n, $i;
}
var Xi, qs;
function Bh() {
  if (qs) return Xi;
  qs = 1;
  var i = Bn();
  return Xi = function(e) {
    return new i().update(e).digest();
  }, Xi;
}
var zi, Vs;
function Sh() {
  if (Vs) return zi;
  Vs = 1;
  var i = Te(), e = Eh(), t = Xt(), r = ke().Buffer, s = Bh(), n = Ca(), u = Fa(), o = r.alloc(128);
  function p(y, f) {
    t.call(this, "digest"), typeof f == "string" && (f = r.from(f));
    var v = y === "sha512" || y === "sha384" ? 128 : 64;
    if (this._alg = y, this._key = f, f.length > v) {
      var m = y === "rmd160" ? new n() : u(y);
      f = m.update(f).digest();
    } else f.length < v && (f = r.concat([f, o], v));
    for (var w = this._ipad = r.allocUnsafe(v), b = this._opad = r.allocUnsafe(v), x = 0; x < v; x++)
      w[x] = f[x] ^ 54, b[x] = f[x] ^ 92;
    this._hash = y === "rmd160" ? new n() : u(y), this._hash.update(w);
  }
  return i(p, t), p.prototype._update = function(y) {
    this._hash.update(y);
  }, p.prototype._final = function() {
    var y = this._hash.digest(), f = this._alg === "rmd160" ? new n() : u(this._alg);
    return f.update(this._opad).update(y).digest();
  }, zi = function(f, v) {
    return f = f.toLowerCase(), f === "rmd160" || f === "ripemd160" ? new p("rmd160", v) : f === "md5" ? new e(s, v) : new p(f, v);
  }, zi;
}
var Ih = Sh();
const Ch = /* @__PURE__ */ _n(Ih), Ah = Ia.createCipheriv, kh = Ia.createDecipheriv, Fh = "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", Th = "cf83e1357eefb8bdf1542850d66d8007d620e4050b5715dc83f4a921d36ce9ce47d0d13c5d85f2b0ff8318d2877eec2f63b931bd47417a81a538327af927da3e", Gs = 65536;
function Ge(i) {
  return i.byteLength ? new Promise((e) => {
    const r = Ta("sha256").update(qe.from(i)).digest();
    e(r.buffer);
  }) : Promise.resolve(ae(gr(Fh)));
}
function zr(i) {
  return i.byteLength ? new Promise((e) => {
    const r = Ta("sha512").update(qe.from(i)).digest();
    e(r.buffer);
  }) : Promise.resolve(ae(gr(Th)));
}
function Sn(i, e) {
  return new Promise((t) => {
    const s = Ch("sha256", qe.from(i)).update(qe.from(e)).digest();
    t(s.buffer);
  });
}
class Ua {
}
class Uh extends Ua {
  get key() {
    if (!this._key)
      throw new H(j.InvalidState, "no key");
    return this._key;
  }
  importKey(e) {
    return this._key = e, Promise.resolve();
  }
  encrypt(e, t) {
    return Promise.resolve().then(() => {
      const r = Ah(
        "aes-256-cbc",
        qe.from(this.key),
        qe.from(t)
      ), s = r.update(qe.from(e));
      return ae(qe.concat([s, r.final()]));
    });
  }
  decrypt(e, t) {
    return Promise.resolve().then(() => {
      const r = kh(
        "aes-256-cbc",
        qe.from(this.key),
        qe.from(t)
      ), s = r.update(qe.from(e));
      return ae(qe.concat([s, r.final()]));
    }).catch(() => {
      throw new H(j.InvalidKey, "invalid key");
    });
  }
}
function In() {
  return new Uh();
}
function He(i) {
  const e = new Uint8Array(i);
  for (; i > 0; ) {
    let t = i % Gs;
    t = t > 0 ? t : Gs;
    const r = new Uint8Array(t);
    wx.getRandomValues(r), i -= t, e.set(r, i);
  }
  return e;
}
function Ra(i, e, t) {
  return Promise.resolve().then(() => {
    const r = new Qs(new Uint8Array(e), new Uint8Array(t));
    return ae(r.encrypt(new Uint8Array(i)));
  });
}
const Da = 0, Ma = 2;
let un;
function Pa(i, e, t, r, s, n, u, o) {
  return un ? un(
    i,
    e,
    t,
    r,
    s,
    n,
    u,
    o
  ).then(ae) : Promise.reject(new H(j.NotImplemented, "argon2 not implemented"));
}
function Rh(i) {
  un = i;
}
const _u = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  AesCbc: Ua,
  Argon2TypeArgon2d: Da,
  Argon2TypeArgon2id: Ma,
  argon2: Pa,
  chacha20: Ra,
  createAesCbc: In,
  hmacSha256: Sn,
  random: He,
  setArgon2Impl: Rh,
  sha256: Ge,
  sha512: zr
}, Symbol.toStringTag, { value: "Module" }));
class nt {
  constructor(e) {
    this._arrayBuffer = e || new ArrayBuffer(1024), this._dataView = new DataView(this._arrayBuffer), this._pos = 0, this._canExpand = !e;
  }
  get pos() {
    return this._pos;
  }
  get byteLength() {
    return this._arrayBuffer.byteLength;
  }
  readBytes(e) {
    const t = this._arrayBuffer.slice(this._pos, this._pos + e);
    return this._pos += e, t;
  }
  readBytesToEnd() {
    const e = this._arrayBuffer.byteLength - this._pos;
    return this.readBytes(e);
  }
  readBytesNoAdvance(e, t) {
    return this._arrayBuffer.slice(e, t);
  }
  writeBytes(e) {
    const t = e instanceof ArrayBuffer ? new Uint8Array(e) : e;
    this.checkCapacity(t.length), new Uint8Array(this._arrayBuffer).set(t, this._pos), this._pos += t.length;
  }
  getWrittenBytes() {
    return this._arrayBuffer.slice(0, this._pos);
  }
  checkCapacity(e) {
    const t = this._arrayBuffer.byteLength - this._pos;
    if (this._canExpand && t < e) {
      let r = this._arrayBuffer.byteLength;
      const s = this._pos + e;
      for (; r < s; )
        r *= 2;
      const n = new Uint8Array(r);
      n.set(new Uint8Array(this._arrayBuffer)), this._arrayBuffer = n.buffer, this._dataView = new DataView(this._arrayBuffer);
    }
  }
  getInt8() {
    const e = this._dataView.getInt8(this._pos);
    return this._pos += 1, e;
  }
  setInt8(e) {
    this.checkCapacity(1), this._dataView.setInt8(this._pos, e), this._pos += 1;
  }
  getUint8() {
    const e = this._dataView.getUint8(this._pos);
    return this._pos += 1, e;
  }
  setUint8(e) {
    this.checkCapacity(1), this._dataView.setUint8(this._pos, e), this._pos += 1;
  }
  getInt16(e) {
    const t = this._dataView.getInt16(this._pos, e);
    return this._pos += 2, t;
  }
  setInt16(e, t) {
    this.checkCapacity(2), this._dataView.setInt16(this._pos, e, t), this._pos += 2;
  }
  getUint16(e) {
    const t = this._dataView.getUint16(this._pos, e);
    return this._pos += 2, t;
  }
  setUint16(e, t) {
    this.checkCapacity(2), this._dataView.setUint16(this._pos, e, t), this._pos += 2;
  }
  getInt32(e) {
    const t = this._dataView.getInt32(this._pos, e);
    return this._pos += 4, t;
  }
  setInt32(e, t) {
    this.checkCapacity(4), this._dataView.setInt32(this._pos, e, t), this._pos += 4;
  }
  getUint32(e) {
    const t = this._dataView.getUint32(this._pos, e);
    return this._pos += 4, t;
  }
  setUint32(e, t) {
    this.checkCapacity(4), this._dataView.setUint32(this._pos, e, t), this._pos += 4;
  }
  getFloat32(e) {
    const t = this._dataView.getFloat32(this._pos, e);
    return this._pos += 4, t;
  }
  setFloat32(e, t) {
    this.checkCapacity(4), this._dataView.setFloat32(this._pos, e, t), this._pos += 4;
  }
  getFloat64(e) {
    const t = this._dataView.getFloat64(this._pos, e);
    return this._pos += 8, t;
  }
  setFloat64(e, t) {
    this.checkCapacity(8), this._dataView.setFloat64(this._pos, e, t), this._pos += 8;
  }
  getUint64(e) {
    let t = this.getUint32(e), r = this.getUint32(e);
    return e ? r *= 4294967296 : t *= 4294967296, t + r;
  }
  setUint64(e, t) {
    t ? (this.setUint32(e & 4294967295, !0), this.setUint32(Math.floor(e / 4294967296), !0)) : (this.checkCapacity(8), this.setUint32(Math.floor(e / 4294967296), !1), this.setUint32(e & 4294967295, !1));
  }
}
const Dh = 1024 * 1024;
function La(i) {
  return Promise.resolve().then(() => {
    const e = new nt(i), t = [];
    let r = 0, s, n = 0;
    const u = () => {
      if (e.getUint32(!0), s = e.readBytes(32), r = e.getUint32(!0), r > 0) {
        n += r;
        const o = e.readBytes(r);
        return Ge(o).then((p) => {
          if (jt(p, s))
            return t.push(o), u();
          throw new H(j.FileCorrupt, "invalid hash block");
        });
      } else {
        const o = new Uint8Array(n);
        let p = 0;
        for (let y = 0; y < t.length; y++)
          o.set(new Uint8Array(t[y]), p), p += t[y].byteLength;
        return Promise.resolve(o.buffer);
      }
    };
    return u();
  });
}
function Na(i) {
  return Promise.resolve().then(() => {
    let e = i.byteLength, t = 0, r = 0, s = 0;
    const n = [], u = () => {
      if (e > 0) {
        const o = Math.min(Dh, e);
        e -= o;
        const p = i.slice(t, t + o);
        return Ge(p).then((y) => {
          const f = new ArrayBuffer(40), v = new nt(f);
          return v.setUint32(r, !0), v.writeBytes(y), v.setUint32(o, !0), n.push(f), s += f.byteLength, n.push(p), s += p.byteLength, r++, t += o, u();
        });
      } else {
        const o = new ArrayBuffer(40);
        new DataView(o).setUint32(0, r, !0), n.push(o), s += o.byteLength;
        const y = new Uint8Array(s);
        let f = 0;
        for (let v = 0; v < n.length; v++)
          y.set(new Uint8Array(n[v]), f), f += n[v].byteLength;
        return Promise.resolve(y.buffer);
      }
    };
    return u();
  });
}
const bu = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  decrypt: La,
  encrypt: Na
}, Symbol.toStringTag, { value: "Module" }));
class Ze {
  constructor(e = 0, t = 0) {
    this.lo = e, this.hi = t;
  }
  get value() {
    if (this.hi) {
      if (this.hi >= 2097152)
        throw new Error("too large number");
      return this.hi * 4294967296 + this.lo;
    }
    return this.lo;
  }
  valueOf() {
    return this.value;
  }
  static from(e) {
    if (e > 9007199254740991)
      throw new Error("too large number");
    const t = e >>> 0, r = (e - t) / 4294967296 >>> 0;
    return new Ze(t, r);
  }
}
const Mh = 1024 * 1024;
function Cn(i, e) {
  const t = new Uint8Array(8 + i.byteLength);
  t.set(new Uint8Array(i), 8);
  const r = new DataView(t.buffer);
  return r.setUint32(0, e.lo, !0), r.setUint32(4, e.hi, !0), zr(ae(t)).then((s) => (me(t), s));
}
function Oa(i, e, t, r) {
  return Cn(i, new Ze(e)).then((s) => {
    const n = new Uint8Array(r.byteLength + 4 + 8), u = new DataView(n.buffer);
    return n.set(new Uint8Array(r), 12), u.setInt32(0, e, !0), u.setInt32(8, t, !0), Sn(s, n.buffer);
  });
}
function ja(i, e) {
  const t = new nt(i);
  return Promise.resolve().then(() => {
    const r = [];
    let s = 0, n = 0, u, o = 0;
    const p = () => {
      if (u = t.readBytes(32), n = t.getUint32(!0), n > 0) {
        o += n;
        const y = t.readBytes(n);
        return Oa(e, s, n, y).then(
          (f) => {
            if (jt(f, u))
              return r.push(y), s++, p();
            throw new H(j.FileCorrupt, "invalid hash block");
          }
        );
      } else {
        const y = new Uint8Array(o);
        let f = 0;
        for (let v = 0; v < r.length; v++)
          y.set(new Uint8Array(r[v]), f), f += r[v].byteLength;
        return Promise.resolve(y.buffer);
      }
    };
    return p();
  });
}
function Ha(i, e) {
  return Promise.resolve().then(() => {
    let t = i.byteLength, r = 0, s = 0, n = 0;
    const u = [], o = () => {
      const p = Math.min(Mh, t);
      t -= p;
      const y = i.slice(r, r + p);
      return Oa(e, s, p, y).then((f) => {
        const v = new ArrayBuffer(36), m = new nt(v);
        if (m.writeBytes(f), m.setUint32(p, !0), u.push(v), n += v.byteLength, y.byteLength > 0)
          return u.push(y), n += y.byteLength, s++, r += p, o();
        {
          const w = new Uint8Array(n);
          let b = 0;
          for (let x = 0; x < u.length; x++)
            w.set(new Uint8Array(u[x]), b), b += u[x].byteLength;
          return w.buffer;
        }
      });
    };
    return o();
  });
}
const xu = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  decrypt: ja,
  encrypt: Ha,
  getHmacKey: Cn
}, Symbol.toStringTag, { value: "Module" })), Ks = 1e4, qt = 16, Ws = 32;
function An(i, e, t) {
  const r = In();
  return r.importKey(ae(e)).then(() => {
    const s = [];
    for (let n = 0; n < Ws; n += qt)
      s.push(
        Ph(r, i.subarray(n, n + qt), t)
      );
    return Promise.all(s);
  }).then((s) => {
    const n = new Uint8Array(Ws);
    return s.forEach((u, o) => {
      const p = o * qt;
      for (let y = 0; y < qt; ++y)
        n[y + p] = u[y];
      me(u);
    }), n;
  });
}
function Ph(i, e, t) {
  let r = Promise.resolve(ae(e));
  const s = new Uint8Array(qt * Math.min(t, Ks));
  for (; t > 0; ) {
    const n = Math.min(t, Ks);
    t -= n;
    const u = qt * n, o = s.length === u ? s.buffer : ae(s.subarray(0, u));
    r = Lh(i, r, o);
  }
  return r.then((n) => new Uint8Array(n));
}
function Lh(i, e, t) {
  return e.then((r) => i.encrypt(t, r)).then((r) => {
    const s = ae(
      new Uint8Array(r).subarray(-2 * qt, -16)
    );
    return me(r), s;
  });
}
const vu = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  encrypt: An
}, Symbol.toStringTag, { value: "Module" }));
function qa(i, e) {
  const t = e.get("$UUID");
  if (!t || !(t instanceof ArrayBuffer))
    return Promise.reject(new H(j.FileCorrupt, "no kdf uuid"));
  switch (Mt(t)) {
    case At.Argon2d:
      return $s(i, e, Da);
    case At.Argon2id:
      return $s(i, e, Ma);
    case At.Aes:
      return Nh(i, e);
    default:
      return Promise.reject(new H(j.Unsupported, "bad kdf"));
  }
}
function $s(i, e, t) {
  const r = e.get("S");
  if (!(r instanceof ArrayBuffer) || r.byteLength !== 32)
    return Promise.reject(new H(j.FileCorrupt, "bad argon2 salt"));
  const s = Lr(e.get("P"));
  if (typeof s != "number" || s < 1)
    return Promise.reject(new H(j.FileCorrupt, "bad argon2 parallelism"));
  const n = Lr(e.get("I"));
  if (typeof n != "number" || n < 1)
    return Promise.reject(new H(j.FileCorrupt, "bad argon2 iterations"));
  const u = Lr(e.get("M"));
  if (typeof u != "number" || u < 1 || u % 1024 !== 0)
    return Promise.reject(new H(j.FileCorrupt, "bad argon2 memory"));
  const o = e.get("V");
  return o !== 19 && o !== 16 ? Promise.reject(new H(j.FileCorrupt, "bad argon2 version")) : e.get("K") ? Promise.reject(new H(j.Unsupported, "argon2 secret key")) : e.get("A") ? Promise.reject(new H(j.Unsupported, "argon2 assoc data")) : Pa(
    i,
    r,
    u / 1024,
    n,
    32,
    s,
    t,
    o
  );
}
function Nh(i, e) {
  const t = e.get("S");
  if (!(t instanceof ArrayBuffer) || t.byteLength !== 32)
    return Promise.reject(new H(j.FileCorrupt, "bad aes salt"));
  const r = Lr(e.get("R"));
  return typeof r != "number" || r < 1 ? Promise.reject(new H(j.FileCorrupt, "bad aes rounds")) : An(new Uint8Array(i), new Uint8Array(t), r).then(
    (s) => Ge(s).then((n) => (me(s), n))
  );
}
function Lr(i) {
  if (typeof i == "number")
    return i;
  if (i instanceof Ze)
    return i.value;
}
const Eu = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  encrypt: qa
}, Symbol.toStringTag, { value: "Module" }));
class Oh {
  // number of block bytes used
  constructor(e, t) {
    this._rounds = 20, this._sigmaWords = [1634760805, 857760878, 2036477234, 1797285236], this._keyWords = [], this._nonceWords = [0, 0], this._counterWords = [0, 0], this._block = new Uint8Array(64), this._blockUsed = 64, this.setKey(e), this.setNonce(t);
  }
  // setKey sets the key to the given 32-byte array.
  setKey(e) {
    for (let t = 0, r = 0; t < 8; t++, r += 4)
      this._keyWords[t] = e[r] & 255 | (e[r + 1] & 255) << 8 | (e[r + 2] & 255) << 16 | (e[r + 3] & 255) << 24;
    this.reset();
  }
  // setNonce sets the nonce to the given 8-byte array.
  setNonce(e) {
    this._nonceWords[0] = e[0] & 255 | (e[1] & 255) << 8 | (e[2] & 255) << 16 | (e[3] & 255) << 24, this._nonceWords[1] = e[4] & 255 | (e[5] & 255) << 8 | (e[6] & 255) << 16 | (e[7] & 255) << 24, this.reset();
  }
  // getBytes returns the next numberOfBytes bytes of stream.
  getBytes(e) {
    const t = new Uint8Array(e);
    for (let r = 0; r < e; r++)
      this._blockUsed === 64 && (this.generateBlock(), this.incrementCounter(), this._blockUsed = 0), t[r] = this._block[this._blockUsed], this._blockUsed++;
    return t;
  }
  getHexString(e) {
    const t = [
      "0",
      "1",
      "2",
      "3",
      "4",
      "5",
      "6",
      "7",
      "8",
      "9",
      "a",
      "b",
      "c",
      "d",
      "e",
      "f"
    ], r = [], s = this.getBytes(e);
    for (let n = 0; n < s.length; n++)
      r.push(t[s[n] >> 4 & 15]), r.push(t[s[n] & 15]);
    return r.join("");
  }
  reset() {
    this._counterWords[0] = 0, this._counterWords[1] = 0, this._blockUsed = 64;
  }
  incrementCounter() {
    this._counterWords[0] = this._counterWords[0] + 1 & 4294967295, this._counterWords[0] === 0 && (this._counterWords[1] = this._counterWords[1] + 1 & 4294967295);
  }
  // _generateBlock generates 64 bytes from key, nonce, and counter,
  // and puts the result into this.block.
  generateBlock() {
    const e = this._sigmaWords[0], t = this._keyWords[0], r = this._keyWords[1], s = this._keyWords[2], n = this._keyWords[3], u = this._sigmaWords[1], o = this._nonceWords[0], p = this._nonceWords[1], y = this._counterWords[0], f = this._counterWords[1], v = this._sigmaWords[2], m = this._keyWords[4], w = this._keyWords[5], b = this._keyWords[6], x = this._keyWords[7], S = this._sigmaWords[3];
    let E = e, B = t, A = r, k = s, T = n, D = u, q = o, R = p, G = y, J = f, se = v, Z = m, X = w, ee = b, re = x, ie = S, N;
    for (let ye = 0; ye < this._rounds; ye += 2)
      N = E + X, T ^= N << 7 | N >>> 25, N = T + E, G ^= N << 9 | N >>> 23, N = G + T, X ^= N << 13 | N >>> 19, N = X + G, E ^= N << 18 | N >>> 14, N = D + B, J ^= N << 7 | N >>> 25, N = J + D, ee ^= N << 9 | N >>> 23, N = ee + J, B ^= N << 13 | N >>> 19, N = B + ee, D ^= N << 18 | N >>> 14, N = se + q, re ^= N << 7 | N >>> 25, N = re + se, A ^= N << 9 | N >>> 23, N = A + re, q ^= N << 13 | N >>> 19, N = q + A, se ^= N << 18 | N >>> 14, N = ie + Z, k ^= N << 7 | N >>> 25, N = k + ie, R ^= N << 9 | N >>> 23, N = R + k, Z ^= N << 13 | N >>> 19, N = Z + R, ie ^= N << 18 | N >>> 14, N = E + k, B ^= N << 7 | N >>> 25, N = B + E, A ^= N << 9 | N >>> 23, N = A + B, k ^= N << 13 | N >>> 19, N = k + A, E ^= N << 18 | N >>> 14, N = D + T, q ^= N << 7 | N >>> 25, N = q + D, R ^= N << 9 | N >>> 23, N = R + q, T ^= N << 13 | N >>> 19, N = T + R, D ^= N << 18 | N >>> 14, N = se + J, Z ^= N << 7 | N >>> 25, N = Z + se, G ^= N << 9 | N >>> 23, N = G + Z, J ^= N << 13 | N >>> 19, N = J + G, se ^= N << 18 | N >>> 14, N = ie + re, X ^= N << 7 | N >>> 25, N = X + ie, ee ^= N << 9 | N >>> 23, N = ee + X, re ^= N << 13 | N >>> 19, N = re + ee, ie ^= N << 18 | N >>> 14;
    E += e, B += t, A += r, k += s, T += n, D += u, q += o, R += p, G += y, J += f, se += v, Z += m, X += w, ee += b, re += x, ie += S, this._block[0] = E >>> 0 & 255, this._block[1] = E >>> 8 & 255, this._block[2] = E >>> 16 & 255, this._block[3] = E >>> 24 & 255, this._block[4] = B >>> 0 & 255, this._block[5] = B >>> 8 & 255, this._block[6] = B >>> 16 & 255, this._block[7] = B >>> 24 & 255, this._block[8] = A >>> 0 & 255, this._block[9] = A >>> 8 & 255, this._block[10] = A >>> 16 & 255, this._block[11] = A >>> 24 & 255, this._block[12] = k >>> 0 & 255, this._block[13] = k >>> 8 & 255, this._block[14] = k >>> 16 & 255, this._block[15] = k >>> 24 & 255, this._block[16] = T >>> 0 & 255, this._block[17] = T >>> 8 & 255, this._block[18] = T >>> 16 & 255, this._block[19] = T >>> 24 & 255, this._block[20] = D >>> 0 & 255, this._block[21] = D >>> 8 & 255, this._block[22] = D >>> 16 & 255, this._block[23] = D >>> 24 & 255, this._block[24] = q >>> 0 & 255, this._block[25] = q >>> 8 & 255, this._block[26] = q >>> 16 & 255, this._block[27] = q >>> 24 & 255, this._block[28] = R >>> 0 & 255, this._block[29] = R >>> 8 & 255, this._block[30] = R >>> 16 & 255, this._block[31] = R >>> 24 & 255, this._block[32] = G >>> 0 & 255, this._block[33] = G >>> 8 & 255, this._block[34] = G >>> 16 & 255, this._block[35] = G >>> 24 & 255, this._block[36] = J >>> 0 & 255, this._block[37] = J >>> 8 & 255, this._block[38] = J >>> 16 & 255, this._block[39] = J >>> 24 & 255, this._block[40] = se >>> 0 & 255, this._block[41] = se >>> 8 & 255, this._block[42] = se >>> 16 & 255, this._block[43] = se >>> 24 & 255, this._block[44] = Z >>> 0 & 255, this._block[45] = Z >>> 8 & 255, this._block[46] = Z >>> 16 & 255, this._block[47] = Z >>> 24 & 255, this._block[48] = X >>> 0 & 255, this._block[49] = X >>> 8 & 255, this._block[50] = X >>> 16 & 255, this._block[51] = X >>> 24 & 255, this._block[52] = ee >>> 0 & 255, this._block[53] = ee >>> 8 & 255, this._block[54] = ee >>> 16 & 255, this._block[55] = ee >>> 24 & 255, this._block[56] = re >>> 0 & 255, this._block[57] = re >>> 8 & 255, this._block[58] = re >>> 16 & 255, this._block[59] = re >>> 24 & 255, this._block[60] = ie >>> 0 & 255, this._block[61] = ie >>> 8 & 255, this._block[62] = ie >>> 16 & 255, this._block[63] = ie >>> 24 & 255;
  }
}
const jh = new Uint8Array([232, 48, 9, 75, 151, 32, 93, 42]);
class qr {
  constructor(e) {
    this._algo = e;
  }
  getSalt(e) {
    return ae(this._algo.getBytes(e));
  }
  static create(e, t) {
    switch (t) {
      case Qt.Salsa20:
        return Ge(ae(e)).then((r) => {
          const s = new Uint8Array(r), n = new Oh(s, jh);
          return new qr(n);
        });
      case Qt.ChaCha20:
        return zr(ae(e)).then((r) => {
          const s = new Uint8Array(r, 0, 32), n = new Uint8Array(r, 32, 12), u = new Qs(s, n);
          return new qr(u);
        });
      default:
        return Promise.reject(new H(j.Unsupported, "crsAlgorithm"));
    }
  }
}
class De {
  constructor(e, t) {
    this.value = new Uint8Array(e), this.salt = new Uint8Array(t);
  }
  toString() {
    return Mt(this.value);
  }
  static fromString(e) {
    const t = Dt(e), r = He(t.length);
    for (let s = 0, n = t.length; s < n; s++)
      t[s] ^= r[s];
    return new De(ae(t), ae(r));
  }
  toBase64() {
    const e = this.getBinary(), t = Mt(e);
    return me(e), t;
  }
  static fromBase64(e) {
    const t = lt(e);
    return De.fromBinary(t);
  }
  /**
   * Keep in mind that you're passing the ownership of this array, the contents will be destroyed
   */
  static fromBinary(e) {
    const t = new Uint8Array(e), r = He(t.length);
    for (let s = 0, n = t.length; s < n; s++)
      t[s] ^= r[s];
    return new De(ae(t), ae(r));
  }
  includes(e) {
    if (e.length === 0)
      return !1;
    const t = this.value, r = this.salt, s = Dt(e), n = t.length, u = s.length, o = n - u;
    e: for (let p = 0; p <= o; p++) {
      for (let y = 0; y < u; y++)
        if ((t[p + y] ^ r[p + y]) !== s[y])
          continue e;
      return !0;
    }
    return !1;
  }
  getHash() {
    const e = ae(this.getBinary());
    return Ge(e).then((t) => (me(e), t));
  }
  getText() {
    return Wt(this.getBinary());
  }
  getBinary() {
    const e = this.value, t = this.salt, r = new Uint8Array(e.byteLength);
    for (let s = r.length - 1; s >= 0; s--)
      r[s] = e[s] ^ t[s];
    return r;
  }
  setSalt(e) {
    const t = new Uint8Array(e), r = this.value, s = this.salt;
    for (let n = 0, u = r.length; n < u; n++)
      r[n] = r[n] ^ s[n] ^ t[n], s[n] = t[n];
  }
  clone() {
    return new De(this.value, this.salt);
  }
  get byteLength() {
    return this.value.byteLength;
  }
}
const U = {
  DocNode: "KeePassFile",
  Meta: "Meta",
  Root: "Root",
  Group: "Group",
  Entry: "Entry",
  Generator: "Generator",
  HeaderHash: "HeaderHash",
  SettingsChanged: "SettingsChanged",
  DbName: "DatabaseName",
  DbNameChanged: "DatabaseNameChanged",
  DbDesc: "DatabaseDescription",
  DbDescChanged: "DatabaseDescriptionChanged",
  DbDefaultUser: "DefaultUserName",
  DbDefaultUserChanged: "DefaultUserNameChanged",
  DbMntncHistoryDays: "MaintenanceHistoryDays",
  DbColor: "Color",
  DbKeyChanged: "MasterKeyChanged",
  DbKeyChangeRec: "MasterKeyChangeRec",
  DbKeyChangeForce: "MasterKeyChangeForce",
  RecycleBinEnabled: "RecycleBinEnabled",
  RecycleBinUuid: "RecycleBinUUID",
  RecycleBinChanged: "RecycleBinChanged",
  EntryTemplatesGroup: "EntryTemplatesGroup",
  EntryTemplatesGroupChanged: "EntryTemplatesGroupChanged",
  HistoryMaxItems: "HistoryMaxItems",
  HistoryMaxSize: "HistoryMaxSize",
  LastSelectedGroup: "LastSelectedGroup",
  LastTopVisibleGroup: "LastTopVisibleGroup",
  MemoryProt: "MemoryProtection",
  ProtTitle: "ProtectTitle",
  ProtUserName: "ProtectUserName",
  ProtPassword: "ProtectPassword",
  ProtUrl: "ProtectURL",
  ProtNotes: "ProtectNotes",
  CustomIcons: "CustomIcons",
  CustomIconItem: "Icon",
  CustomIconItemID: "UUID",
  CustomIconItemData: "Data",
  CustomIconItemName: "Name",
  AutoType: "AutoType",
  History: "History",
  Name: "Name",
  Notes: "Notes",
  Uuid: "UUID",
  Icon: "IconID",
  CustomIconID: "CustomIconUUID",
  FgColor: "ForegroundColor",
  BgColor: "BackgroundColor",
  OverrideUrl: "OverrideURL",
  Times: "Times",
  Tags: "Tags",
  QualityCheck: "QualityCheck",
  PreviousParentGroup: "PreviousParentGroup",
  CreationTime: "CreationTime",
  LastModTime: "LastModificationTime",
  LastAccessTime: "LastAccessTime",
  ExpiryTime: "ExpiryTime",
  Expires: "Expires",
  UsageCount: "UsageCount",
  LocationChanged: "LocationChanged",
  GroupDefaultAutoTypeSeq: "DefaultAutoTypeSequence",
  EnableAutoType: "EnableAutoType",
  EnableSearching: "EnableSearching",
  String: "String",
  Binary: "Binary",
  Key: "Key",
  Value: "Value",
  AutoTypeEnabled: "Enabled",
  AutoTypeObfuscation: "DataTransferObfuscation",
  AutoTypeDefaultSeq: "DefaultSequence",
  AutoTypeItem: "Association",
  Window: "Window",
  KeystrokeSequence: "KeystrokeSequence",
  Binaries: "Binaries",
  IsExpanded: "IsExpanded",
  LastTopVisibleEntry: "LastTopVisibleEntry",
  DeletedObjects: "DeletedObjects",
  DeletedObject: "DeletedObject",
  DeletionTime: "DeletionTime",
  CustomData: "CustomData",
  StringDictExItem: "Item"
}, Le = {
  Id: "ID",
  Ref: "Ref",
  Protected: "Protected",
  ProtectedInMemPlainXml: "ProtectInMemory",
  Compressed: "Compressed"
}, Hh = {
  False: "False",
  True: "True"
}, Bu = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  Attr: Le,
  Elem: U,
  Val: Hh
}, Symbol.toStringTag, { value: "Module" }));
var Ke = Uint8Array, $e = Uint16Array, wr = Uint32Array, Jr = new Ke([
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  1,
  1,
  1,
  1,
  2,
  2,
  2,
  2,
  3,
  3,
  3,
  3,
  4,
  4,
  4,
  4,
  5,
  5,
  5,
  5,
  0,
  /* unused */
  0,
  0,
  /* impossible */
  0
]), Yr = new Ke([
  0,
  0,
  0,
  0,
  1,
  1,
  2,
  2,
  3,
  3,
  4,
  4,
  5,
  5,
  6,
  6,
  7,
  7,
  8,
  8,
  9,
  9,
  10,
  10,
  11,
  11,
  12,
  12,
  13,
  13,
  /* unused */
  0,
  0
]), fn = new Ke([16, 17, 18, 0, 8, 7, 9, 6, 10, 5, 11, 4, 12, 3, 13, 2, 14, 1, 15]), Va = function(i, e) {
  for (var t = new $e(31), r = 0; r < 31; ++r)
    t[r] = e += 1 << i[r - 1];
  for (var s = new wr(t[30]), r = 1; r < 30; ++r)
    for (var n = t[r]; n < t[r + 1]; ++n)
      s[n] = n - t[r] << 5 | r;
  return [t, s];
}, Ga = Va(Jr, 2), Ka = Ga[0], cn = Ga[1];
Ka[28] = 258, cn[258] = 28;
var Wa = Va(Yr, 0), qh = Wa[0], Xs = Wa[1], ln = new $e(32768);
for (var Ie = 0; Ie < 32768; ++Ie) {
  var Ut = (Ie & 43690) >>> 1 | (Ie & 21845) << 1;
  Ut = (Ut & 52428) >>> 2 | (Ut & 13107) << 2, Ut = (Ut & 61680) >>> 4 | (Ut & 3855) << 4, ln[Ie] = ((Ut & 65280) >>> 8 | (Ut & 255) << 8) >>> 1;
}
var Bt = function(i, e, t) {
  for (var r = i.length, s = 0, n = new $e(e); s < r; ++s)
    ++n[i[s] - 1];
  var u = new $e(e);
  for (s = 0; s < e; ++s)
    u[s] = u[s - 1] + n[s - 1] << 1;
  var o;
  if (t) {
    o = new $e(1 << e);
    var p = 15 - e;
    for (s = 0; s < r; ++s)
      if (i[s])
        for (var y = s << 4 | i[s], f = e - i[s], v = u[i[s] - 1]++ << f, m = v | (1 << f) - 1; v <= m; ++v)
          o[ln[v] >>> p] = y;
  } else
    for (o = new $e(r), s = 0; s < r; ++s)
      i[s] && (o[s] = ln[u[i[s] - 1]++] >>> 15 - i[s]);
  return o;
}, Pt = new Ke(288);
for (var Ie = 0; Ie < 144; ++Ie)
  Pt[Ie] = 8;
for (var Ie = 144; Ie < 256; ++Ie)
  Pt[Ie] = 9;
for (var Ie = 256; Ie < 280; ++Ie)
  Pt[Ie] = 7;
for (var Ie = 280; Ie < 288; ++Ie)
  Pt[Ie] = 8;
var mr = new Ke(32);
for (var Ie = 0; Ie < 32; ++Ie)
  mr[Ie] = 5;
var Vh = /* @__PURE__ */ Bt(Pt, 9, 0), Gh = /* @__PURE__ */ Bt(Pt, 9, 1), Kh = /* @__PURE__ */ Bt(mr, 5, 0), Wh = /* @__PURE__ */ Bt(mr, 5, 1), Ji = function(i) {
  for (var e = i[0], t = 1; t < i.length; ++t)
    i[t] > e && (e = i[t]);
  return e;
}, pt = function(i, e, t) {
  var r = e / 8 | 0;
  return (i[r] | i[r + 1] << 8) >> (e & 7) & t;
}, Yi = function(i, e) {
  var t = e / 8 | 0;
  return (i[t] | i[t + 1] << 8 | i[t + 2] << 16) >> (e & 7);
}, kn = function(i) {
  return (i + 7) / 8 | 0;
}, $a = function(i, e, t) {
  (t == null || t > i.length) && (t = i.length);
  var r = new (i instanceof $e ? $e : i instanceof wr ? wr : Ke)(t - e);
  return r.set(i.subarray(e, t)), r;
}, $h = [
  "unexpected EOF",
  "invalid block type",
  "invalid length/literal",
  "invalid distance",
  "stream finished",
  "no stream handler",
  ,
  "no callback",
  "invalid UTF-8 data",
  "extra field too long",
  "date not in range 1980-2099",
  "filename too long",
  "stream finishing",
  "invalid zip data"
  // determined by unknown compression method
], Ct = function(i, e, t) {
  var r = new Error(e || $h[i]);
  if (r.code = i, Error.captureStackTrace && Error.captureStackTrace(r, Ct), !t)
    throw r;
  return r;
}, Xh = function(i, e, t) {
  var r = i.length;
  if (!r || t && t.f && !t.l)
    return e || new Ke(0);
  var s = !e || t, n = !t || t.i;
  t || (t = {}), e || (e = new Ke(r * 3));
  var u = function(L) {
    var P = e.length;
    if (L > P) {
      var V = new Ke(Math.max(P * 2, L));
      V.set(e), e = V;
    }
  }, o = t.f || 0, p = t.p || 0, y = t.b || 0, f = t.l, v = t.d, m = t.m, w = t.n, b = r * 8;
  do {
    if (!f) {
      o = pt(i, p, 1);
      var x = pt(i, p + 1, 3);
      if (p += 3, x)
        if (x == 1)
          f = Gh, v = Wh, m = 9, w = 5;
        else if (x == 2) {
          var A = pt(i, p, 31) + 257, k = pt(i, p + 10, 15) + 4, T = A + pt(i, p + 5, 31) + 1;
          p += 14;
          for (var D = new Ke(T), q = new Ke(19), R = 0; R < k; ++R)
            q[fn[R]] = pt(i, p + R * 3, 7);
          p += k * 3;
          for (var G = Ji(q), J = (1 << G) - 1, se = Bt(q, G, 1), R = 0; R < T; ) {
            var Z = se[pt(i, p, J)];
            p += Z & 15;
            var S = Z >>> 4;
            if (S < 16)
              D[R++] = S;
            else {
              var X = 0, ee = 0;
              for (S == 16 ? (ee = 3 + pt(i, p, 3), p += 2, X = D[R - 1]) : S == 17 ? (ee = 3 + pt(i, p, 7), p += 3) : S == 18 && (ee = 11 + pt(i, p, 127), p += 7); ee--; )
                D[R++] = X;
            }
          }
          var re = D.subarray(0, A), ie = D.subarray(A);
          m = Ji(re), w = Ji(ie), f = Bt(re, m, 1), v = Bt(ie, w, 1);
        } else
          Ct(1);
      else {
        var S = kn(p) + 4, E = i[S - 4] | i[S - 3] << 8, B = S + E;
        if (B > r) {
          n && Ct(0);
          break;
        }
        s && u(y + E), e.set(i.subarray(S, B), y), t.b = y += E, t.p = p = B * 8, t.f = o;
        continue;
      }
      if (p > b) {
        n && Ct(0);
        break;
      }
    }
    s && u(y + 131072);
    for (var N = (1 << m) - 1, ye = (1 << w) - 1, ge = p; ; ge = p) {
      var X = f[Yi(i, p) & N], Me = X >>> 4;
      if (p += X & 15, p > b) {
        n && Ct(0);
        break;
      }
      if (X || Ct(2), Me < 256)
        e[y++] = Me;
      else if (Me == 256) {
        ge = p, f = null;
        break;
      } else {
        var ue = Me - 254;
        if (Me > 264) {
          var R = Me - 257, le = Jr[R];
          ue = pt(i, p, (1 << le) - 1) + Ka[R], p += le;
        }
        var Ee = v[Yi(i, p) & ye], he = Ee >>> 4;
        Ee || Ct(3), p += Ee & 15;
        var ie = qh[he];
        if (he > 3) {
          var le = Yr[he];
          ie += Yi(i, p) & (1 << le) - 1, p += le;
        }
        if (p > b) {
          n && Ct(0);
          break;
        }
        s && u(y + 131072);
        for (var fe = y + ue; y < fe; y += 4)
          e[y] = e[y - ie], e[y + 1] = e[y + 1 - ie], e[y + 2] = e[y + 2 - ie], e[y + 3] = e[y + 3 - ie];
        y = fe;
      }
    }
    t.l = f, t.p = ge, t.b = y, t.f = o, f && (o = 1, t.m = m, t.d = v, t.n = w);
  } while (!o);
  return y == e.length ? e : $a(e, 0, y);
}, It = function(i, e, t) {
  t <<= e & 7;
  var r = e / 8 | 0;
  i[r] |= t, i[r + 1] |= t >>> 8;
}, fr = function(i, e, t) {
  t <<= e & 7;
  var r = e / 8 | 0;
  i[r] |= t, i[r + 1] |= t >>> 8, i[r + 2] |= t >>> 16;
}, Zi = function(i, e) {
  for (var t = [], r = 0; r < i.length; ++r)
    i[r] && t.push({ s: r, f: i[r] });
  var s = t.length, n = t.slice();
  if (!s)
    return [Xa, 0];
  if (s == 1) {
    var u = new Ke(t[0].s + 1);
    return u[t[0].s] = 1, [u, 1];
  }
  t.sort(function(T, D) {
    return T.f - D.f;
  }), t.push({ s: -1, f: 25001 });
  var o = t[0], p = t[1], y = 0, f = 1, v = 2;
  for (t[0] = { s: -1, f: o.f + p.f, l: o, r: p }; f != s - 1; )
    o = t[t[y].f < t[v].f ? y++ : v++], p = t[y != f && t[y].f < t[v].f ? y++ : v++], t[f++] = { s: -1, f: o.f + p.f, l: o, r: p };
  for (var m = n[0].s, r = 1; r < s; ++r)
    n[r].s > m && (m = n[r].s);
  var w = new $e(m + 1), b = dn(t[f - 1], w, 0);
  if (b > e) {
    var r = 0, x = 0, S = b - e, E = 1 << S;
    for (n.sort(function(D, q) {
      return w[q.s] - w[D.s] || D.f - q.f;
    }); r < s; ++r) {
      var B = n[r].s;
      if (w[B] > e)
        x += E - (1 << b - w[B]), w[B] = e;
      else
        break;
    }
    for (x >>>= S; x > 0; ) {
      var A = n[r].s;
      w[A] < e ? x -= 1 << e - w[A]++ - 1 : ++r;
    }
    for (; r >= 0 && x; --r) {
      var k = n[r].s;
      w[k] == e && (--w[k], ++x);
    }
    b = e;
  }
  return [new Ke(w), b];
}, dn = function(i, e, t) {
  return i.s == -1 ? Math.max(dn(i.l, e, t + 1), dn(i.r, e, t + 1)) : e[i.s] = t;
}, zs = function(i) {
  for (var e = i.length; e && !i[--e]; )
    ;
  for (var t = new $e(++e), r = 0, s = i[0], n = 1, u = function(p) {
    t[r++] = p;
  }, o = 1; o <= e; ++o)
    if (i[o] == s && o != e)
      ++n;
    else {
      if (!s && n > 2) {
        for (; n > 138; n -= 138)
          u(32754);
        n > 2 && (u(n > 10 ? n - 11 << 5 | 28690 : n - 3 << 5 | 12305), n = 0);
      } else if (n > 3) {
        for (u(s), --n; n > 6; n -= 6)
          u(8304);
        n > 2 && (u(n - 3 << 5 | 8208), n = 0);
      }
      for (; n--; )
        u(s);
      n = 1, s = i[o];
    }
  return [t.subarray(0, r), e];
}, cr = function(i, e) {
  for (var t = 0, r = 0; r < e.length; ++r)
    t += i[r] * e[r];
  return t;
}, pn = function(i, e, t) {
  var r = t.length, s = kn(e + 2);
  i[s] = r & 255, i[s + 1] = r >>> 8, i[s + 2] = i[s] ^ 255, i[s + 3] = i[s + 1] ^ 255;
  for (var n = 0; n < r; ++n)
    i[s + n + 4] = t[n];
  return (s + 4 + r) * 8;
}, Js = function(i, e, t, r, s, n, u, o, p, y, f) {
  It(e, f++, t), ++s[256];
  for (var v = Zi(s, 15), m = v[0], w = v[1], b = Zi(n, 15), x = b[0], S = b[1], E = zs(m), B = E[0], A = E[1], k = zs(x), T = k[0], D = k[1], q = new $e(19), R = 0; R < B.length; ++R)
    q[B[R] & 31]++;
  for (var R = 0; R < T.length; ++R)
    q[T[R] & 31]++;
  for (var G = Zi(q, 7), J = G[0], se = G[1], Z = 19; Z > 4 && !J[fn[Z - 1]]; --Z)
    ;
  var X = y + 5 << 3, ee = cr(s, Pt) + cr(n, mr) + u, re = cr(s, m) + cr(n, x) + u + 14 + 3 * Z + cr(q, J) + (2 * q[16] + 3 * q[17] + 7 * q[18]);
  if (X <= ee && X <= re)
    return pn(e, f, i.subarray(p, p + y));
  var ie, N, ye, ge;
  if (It(e, f, 1 + (re < ee)), f += 2, re < ee) {
    ie = Bt(m, w, 0), N = m, ye = Bt(x, S, 0), ge = x;
    var Me = Bt(J, se, 0);
    It(e, f, A - 257), It(e, f + 5, D - 1), It(e, f + 10, Z - 4), f += 14;
    for (var R = 0; R < Z; ++R)
      It(e, f + 3 * R, J[fn[R]]);
    f += 3 * Z;
    for (var ue = [B, T], le = 0; le < 2; ++le)
      for (var Ee = ue[le], R = 0; R < Ee.length; ++R) {
        var he = Ee[R] & 31;
        It(e, f, Me[he]), f += J[he], he > 15 && (It(e, f, Ee[R] >>> 5 & 127), f += Ee[R] >>> 12);
      }
  } else
    ie = Vh, N = Pt, ye = Kh, ge = mr;
  for (var R = 0; R < o; ++R)
    if (r[R] > 255) {
      var he = r[R] >>> 18 & 31;
      fr(e, f, ie[he + 257]), f += N[he + 257], he > 7 && (It(e, f, r[R] >>> 23 & 31), f += Jr[he]);
      var fe = r[R] & 31;
      fr(e, f, ye[fe]), f += ge[fe], fe > 3 && (fr(e, f, r[R] >>> 5 & 8191), f += Yr[fe]);
    } else
      fr(e, f, ie[r[R]]), f += N[r[R]];
  return fr(e, f, ie[256]), f + N[256];
}, zh = /* @__PURE__ */ new wr([65540, 131080, 131088, 131104, 262176, 1048704, 1048832, 2114560, 2117632]), Xa = /* @__PURE__ */ new Ke(0), Jh = function(i, e, t, r, s, n) {
  var u = i.length, o = new Ke(r + u + 5 * (1 + Math.ceil(u / 7e3)) + s), p = o.subarray(r, o.length - s), y = 0;
  if (!e || u < 8)
    for (var f = 0; f <= u; f += 65535) {
      var v = f + 65535;
      v < u ? y = pn(p, y, i.subarray(f, v)) : (p[f] = n, y = pn(p, y, i.subarray(f, u)));
    }
  else {
    for (var m = zh[e - 1], w = m >>> 13, b = m & 8191, x = (1 << t) - 1, S = new $e(32768), E = new $e(x + 1), B = Math.ceil(t / 3), A = 2 * B, k = function($) {
      return (i[$] ^ i[$ + 1] << B ^ i[$ + 2] << A) & x;
    }, T = new wr(25e3), D = new $e(288), q = new $e(32), R = 0, G = 0, f = 0, J = 0, se = 0, Z = 0; f < u; ++f) {
      var X = k(f), ee = f & 32767, re = E[X];
      if (S[ee] = re, E[X] = ee, se <= f) {
        var ie = u - f;
        if ((R > 7e3 || J > 24576) && ie > 423) {
          y = Js(i, p, 0, T, D, q, G, J, Z, f - Z, y), J = R = G = 0, Z = f;
          for (var N = 0; N < 286; ++N)
            D[N] = 0;
          for (var N = 0; N < 30; ++N)
            q[N] = 0;
        }
        var ye = 2, ge = 0, Me = b, ue = ee - re & 32767;
        if (ie > 2 && X == k(f - ue))
          for (var le = Math.min(w, ie) - 1, Ee = Math.min(32767, f), he = Math.min(258, ie); ue <= Ee && --Me && ee != re; ) {
            if (i[f + ye] == i[f + ye - ue]) {
              for (var fe = 0; fe < he && i[f + fe] == i[f + fe - ue]; ++fe)
                ;
              if (fe > ye) {
                if (ye = fe, ge = ue, fe > le)
                  break;
                for (var L = Math.min(ue, fe - 2), P = 0, N = 0; N < L; ++N) {
                  var V = f - ue + N + 32768 & 32767, z = S[V], Q = V - z + 32768 & 32767;
                  Q > P && (P = Q, re = V);
                }
              }
            }
            ee = re, re = S[ee], ue += ee - re + 32768 & 32767;
          }
        if (ge) {
          T[J++] = 268435456 | cn[ye] << 18 | Xs[ge];
          var I = cn[ye] & 31, C = Xs[ge] & 31;
          G += Jr[I] + Yr[C], ++D[257 + I], ++q[C], se = f + ye, ++R;
        } else
          T[J++] = i[f], ++D[i[f]];
      }
    }
    y = Js(i, p, n, T, D, q, G, J, Z, f - Z, y);
  }
  return $a(o, 0, r + kn(y) + s);
}, Yh = /* @__PURE__ */ function() {
  for (var i = new Int32Array(256), e = 0; e < 256; ++e) {
    for (var t = e, r = 9; --r; )
      t = (t & 1 && -306674912) ^ t >>> 1;
    i[e] = t;
  }
  return i;
}(), Zh = function() {
  var i = -1;
  return {
    p: function(e) {
      for (var t = i, r = 0; r < e.length; ++r)
        t = Yh[t & 255 ^ e[r]] ^ t >>> 8;
      i = t;
    },
    d: function() {
      return ~i;
    }
  };
}, Qh = function(i, e, t, r, s) {
  return Jh(i, e.level == null ? 6 : e.level, e.mem == null ? Math.ceil(Math.max(8, Math.min(13, Math.log(i.length))) * 1.5) : 12 + e.mem, t, r, !0);
}, yn = function(i, e, t) {
  for (; t; ++e)
    i[e] = t, t >>>= 8;
}, eu = function(i, e) {
  var t = e.filename;
  if (i[0] = 31, i[1] = 139, i[2] = 8, i[8] = e.level < 2 ? 4 : e.level == 9 ? 2 : 0, i[9] = 3, e.mtime != 0 && yn(i, 4, Math.floor(new Date(e.mtime || Date.now()) / 1e3)), t) {
    i[3] = 8;
    for (var r = 0; r <= t.length; ++r)
      i[r + 10] = t.charCodeAt(r);
  }
}, tu = function(i) {
  (i[0] != 31 || i[1] != 139 || i[2] != 8) && Ct(6, "invalid gzip data");
  var e = i[3], t = 10;
  e & 4 && (t += i[10] | (i[11] << 8) + 2);
  for (var r = (e >> 3 & 1) + (e >> 4 & 1); r > 0; r -= !i[t++])
    ;
  return t + (e & 2);
}, ru = function(i) {
  var e = i.length;
  return (i[e - 4] | i[e - 3] << 8 | i[e - 2] << 16 | i[e - 1] << 24) >>> 0;
}, iu = function(i) {
  return 10 + (i.filename && i.filename.length + 1 || 0);
};
function Ys(i, e) {
  e || (e = {});
  var t = Zh(), r = i.length;
  t.p(i);
  var s = Qh(i, e, iu(e), 8), n = s.length;
  return eu(s, e), yn(s, n - 8, t.d()), yn(s, n - 4, r), s;
}
function gn(i, e) {
  return Xh(i.subarray(tu(i), -8), new Ke(ru(i)));
}
var nu = typeof Hr < "u" && /* @__PURE__ */ new Hr(), su = 0;
try {
  nu.decode(Xa, { stream: !0 }), su = 1;
} catch {
}
const Qi = 16, au = "AAAAAAAAAAAAAAAAAAAAAA==";
class Ne {
  constructor(e) {
    if (e === void 0 ? e = new ArrayBuffer(Qi) : typeof e == "string" && (e = lt(e)), e.byteLength !== Qi)
      throw new H(j.FileCorrupt, `bad UUID length: ${e.byteLength}`);
    this.id = Mt(e), this.empty = this.id === au;
  }
  equals(e) {
    return e && e.toString() === this.toString() || !1;
  }
  get bytes() {
    return this.toBytes();
  }
  static random() {
    return new Ne(He(Qi));
  }
  toString() {
    return this.id;
  }
  valueOf() {
    return this.id;
  }
  toBytes() {
    return lt(this.id);
  }
}
class gt {
  constructor() {
    this._mapById = /* @__PURE__ */ new Map(), this._mapByHash = /* @__PURE__ */ new Map(), this._idToHash = /* @__PURE__ */ new Map();
  }
  computeHashes() {
    const e = [...this._mapById].map(
      ([t, r]) => gt.getBinaryHash(r).then((s) => {
        this._idToHash.set(t, s), this._mapByHash.set(s, r);
      })
    );
    return Promise.all(e).then(() => {
      this._mapById.clear();
    });
  }
  static getBinaryHash(e) {
    let t;
    return e instanceof De ? t = e.getHash() : (e = ae(e), t = Ge(e)), t.then(pr);
  }
  add(e) {
    return e instanceof Uint8Array && (e = ae(e)), gt.getBinaryHash(e).then((t) => (this._mapByHash.set(t, e), { hash: t, value: e }));
  }
  addWithNextId(e) {
    const t = this._mapById.size.toString();
    this.addWithId(t, e);
  }
  addWithId(e, t) {
    t instanceof Uint8Array && (t = ae(t)), this._mapById.set(e, t);
  }
  addWithHash(e) {
    this._mapByHash.set(e.hash, e.value);
  }
  deleteWithHash(e) {
    this._mapByHash.delete(e);
  }
  getByRef(e) {
    const t = this._idToHash.get(e.ref);
    if (!t)
      return;
    const r = this._mapByHash.get(t);
    if (r)
      return { hash: t, value: r };
  }
  getRefByHash(e) {
    const t = [...this._mapByHash.keys()].indexOf(e);
    if (!(t < 0))
      return { ref: t.toString() };
  }
  getAll() {
    return [...this._mapByHash.values()].map((e, t) => ({ ref: t.toString(), value: e }));
  }
  getAllWithHashes() {
    return [...this._mapByHash].map(([e, t]) => ({
      hash: e,
      value: t
    }));
  }
  getValueByHash(e) {
    return this._mapByHash.get(e);
  }
  static isKdbxBinaryRef(e) {
    return !!(e != null && e.ref);
  }
  static isKdbxBinaryWithHash(e) {
    return !!(e != null && e.hash);
  }
}
const ou = /\.\d\d\d/, za = 62135596800, hu = /\s*[;,:]\s*/;
function uu() {
  if (Rt.DOMParser)
    return new Rt.DOMParser();
  const i = {
    errorHandler: {
      warning: (t) => {
        throw t;
      },
      error: (t) => {
        throw t;
      },
      fatalError: (t) => {
        throw t;
      }
    }
  }, { DOMParser: e } = require("@xmldom/xmldom");
  return new e(i);
}
function fu() {
  if (Rt.XMLSerializer)
    return new Rt.XMLSerializer();
  const { XMLSerializer: i } = require("@xmldom/xmldom");
  return new i();
}
function tr(i) {
  const e = uu();
  let t;
  i = i.replace(/[\x00-\x09\x0B-\x0C\x0E-\x1F]/g, "");
  try {
    t = e.parseFromString(i, "application/xml");
  } catch (s) {
    const n = s instanceof Error ? s.message : String(s);
    throw new H(j.FileCorrupt, `bad xml: ${n}`);
  }
  if (!t.documentElement)
    throw new H(j.FileCorrupt, "bad xml");
  const r = t.getElementsByTagName("parsererror")[0];
  if (r)
    throw new H(j.FileCorrupt, `bad xml: ${r.textContent}`);
  return t;
}
function Nr(i, e = !1) {
  e && Ja(i, 0);
  let t = fu().serializeToString(i);
  return e && t.startsWith("<?") && (t = t.replace(/^(<\?.*?\?>)</, `$1
<`)), t;
}
function Ja(i, e) {
  const t = i.childNodes.length;
  if (t === 0)
    return;
  const r = `
` + "    ".repeat(e), s = e > 0 ? `
` + "    ".repeat(e - 1) : "", n = i.ownerDocument || i, u = [];
  let o;
  for (let p = 0; p < t; p++)
    o = i.childNodes[p], o.nodeType !== n.TEXT_NODE && o.nodeType !== n.PROCESSING_INSTRUCTION_NODE && u.push(o);
  for (let p = 0; p < u.length; p++) {
    if (o = u[p], !(e === 0 && p === 0)) {
      const f = n.createTextNode(r);
      i.insertBefore(f, o);
    }
    if (!o.nextSibling && e > 0) {
      const f = n.createTextNode(s);
      i.appendChild(f);
    }
    Ja(o, e + 1);
  }
}
function Ya(i) {
  return tr('<?xml version="1.0" encoding="utf-8" standalone="yes"?><' + i + "/>");
}
function yt(i, e, t) {
  if (i && i.childNodes) {
    for (let r = 0, s = i.childNodes, n = s.length; r < n; r++)
      if (s[r].tagName === e)
        return s[r];
  }
  if (t)
    throw new H(j.FileCorrupt, t);
  return null;
}
function W(i, e) {
  return i.appendChild((i.ownerDocument || i).createElement(e));
}
function Se(i) {
  if (i != null && i.childNodes)
    return i.protectedValue ? i.protectedValue.getText() : i.textContent ?? void 0;
}
function _e(i, e) {
  i.textContent = e || "";
}
function Fn(i) {
  const e = Se(i);
  return e ? e.split(hu).map((t) => t.trim()).filter((t) => t) : [];
}
function Tn(i, e) {
  _e(i, e.join(", "));
}
function Vr(i) {
  const e = Se(i);
  return e ? ae(lt(e)) : void 0;
}
function _r(i, e) {
  typeof e == "string" && (e = lt(e)), _e(i, e ? Mt(ae(e)) : void 0);
}
function Ve(i) {
  const e = Se(i);
  if (!e)
    return;
  if (e.indexOf(":") > 0)
    return new Date(e);
  const t = new DataView(ae(lt(e))), s = (new Ze(t.getUint32(0, !0), t.getUint32(4, !0)).value - za) * 1e3;
  return new Date(s);
}
function Zr(i, e, t = !1) {
  if (e)
    if (t) {
      const r = Math.floor(e.getTime() / 1e3) + za, s = new DataView(new ArrayBuffer(8)), n = Ze.from(r);
      s.setUint32(0, n.lo, !0), s.setUint32(4, n.hi, !0), _e(i, Mt(s.buffer));
    } else
      _e(i, e.toISOString().replace(ou, ""));
  else
    _e(i, "");
}
function vt(i) {
  const e = Se(i);
  return e ? +e : void 0;
}
function Et(i, e) {
  _e(i, typeof e == "number" && !isNaN(e) ? e.toString() : void 0);
}
function rt(i) {
  const e = Se(i);
  return e ? Lt(e) : void 0;
}
function it(i, e) {
  _e(
    i,
    e === void 0 ? "" : e === null ? "null" : e ? "True" : "False"
  );
}
function Lt(i) {
  switch (i == null ? void 0 : i.toLowerCase()) {
    case "true":
      return !0;
    case "false":
      return !1;
    case "null":
      return null;
  }
}
function Je(i) {
  const e = Vr(i);
  return e ? new Ne(e) : void 0;
}
function Ye(i, e) {
  const t = e instanceof Ne ? e.toBytes() : e;
  _r(i, t);
}
function Za(i) {
  return (i.protectedValue || i.textContent) ?? void 0;
}
function Qa(i, e) {
  e instanceof De ? (i.protectedValue = e, i.setAttribute(Le.Protected, "True")) : _e(i, e);
}
function Un(i) {
  if (i.protectedValue)
    return i.protectedValue;
  const e = i.textContent, t = i.getAttribute(Le.Ref);
  if (t)
    return { ref: t };
  if (!e)
    return;
  const r = Lt(i.getAttribute(Le.Compressed));
  let s = lt(e);
  return r && (s = gn(s)), ae(s);
}
function Rn(i, e) {
  e instanceof De ? (i.protectedValue = e, i.setAttribute(Le.Protected, "True")) : gt.isKdbxBinaryRef(e) ? i.setAttribute(Le.Ref, e.ref) : _r(i, e);
}
function zt(i, e) {
  e(i);
  for (let t = 0, r = i.childNodes, s = r.length; t < s; t++) {
    const n = r[t];
    n.tagName && zt(n, e);
  }
}
function eo(i, e) {
  zt(i, (t) => {
    if (Lt(t.getAttribute(Le.Protected)))
      try {
        const r = ae(lt(t.textContent || ""));
        if (r.byteLength) {
          const s = e.getSalt(r.byteLength);
          t.protectedValue = new De(r, s);
        }
      } catch (r) {
        throw new H(
          j.FileCorrupt,
          `bad protected value at line ${t.lineNumber}: ${r}`
        );
      }
  });
}
function wn(i, e) {
  zt(i, (t) => {
    if (Lt(t.getAttribute(Le.Protected)) && t.protectedValue) {
      const r = e.getSalt(t.protectedValue.byteLength);
      t.protectedValue.setSalt(r), t.textContent = t.protectedValue.toString();
    }
  });
}
function to(i) {
  zt(i, (e) => {
    Lt(e.getAttribute(Le.Protected)) && e.protectedValue && (e.removeAttribute(Le.Protected), e.setAttribute(Le.ProtectedInMemPlainXml, "True"), e.textContent = e.protectedValue.getText());
  });
}
function ro(i) {
  zt(i, (e) => {
    Lt(e.getAttribute(Le.ProtectedInMemPlainXml)) && e.protectedValue && (e.removeAttribute(Le.ProtectedInMemPlainXml), e.setAttribute(Le.Protected, "True"), e.textContent = e.protectedValue.toString());
  });
}
function io(i) {
  zt(i, (e) => {
    Lt(e.getAttribute(Le.ProtectedInMemPlainXml)) && (e.protectedValue = De.fromString(e.textContent || ""), e.textContent = e.protectedValue.toString(), e.removeAttribute(Le.ProtectedInMemPlainXml), e.setAttribute(Le.Protected, "True"));
  });
}
const Su = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  addChildNode: W,
  create: Ya,
  getBoolean: rt,
  getBytes: Vr,
  getChildNode: yt,
  getDate: Ve,
  getNumber: vt,
  getProtectedBinary: Un,
  getProtectedText: Za,
  getTags: Fn,
  getText: Se,
  getUuid: Je,
  parse: tr,
  protectPlainValues: io,
  protectUnprotectedValues: ro,
  serialize: Nr,
  setBoolean: it,
  setBytes: _r,
  setDate: Zr,
  setNumber: Et,
  setProtectedBinary: Rn,
  setProtectedText: Qa,
  setProtectedValues: eo,
  setTags: Tn,
  setText: _e,
  setUuid: Ye,
  strToBoolean: Lt,
  traverse: zt,
  unprotectValues: to,
  updateProtectedValuesSalt: wn
}, Symbol.toStringTag, { value: "Module" }));
class Gr {
  readNode(e) {
    switch (e.tagName) {
      case U.Uuid:
        this.uuid = Je(e);
        break;
      case U.DeletionTime:
        this.deletionTime = Ve(e);
        break;
    }
  }
  write(e, t) {
    const r = W(e, U.DeletedObject);
    Ye(W(r, U.Uuid), this.uuid), t.setXmlDate(W(r, U.DeletionTime), this.deletionTime);
  }
  static read(e) {
    const t = new Gr();
    for (let r = 0, s = e.childNodes, n = s.length; r < n; r++) {
      const u = s[r];
      u.tagName && t.readNode(u);
    }
    return t;
  }
}
class St {
  readNode(e) {
    switch (e.tagName) {
      case U.CreationTime:
        this.creationTime = Ve(e);
        break;
      case U.LastModTime:
        this.lastModTime = Ve(e);
        break;
      case U.LastAccessTime:
        this.lastAccessTime = Ve(e);
        break;
      case U.ExpiryTime:
        this.expiryTime = Ve(e);
        break;
      case U.Expires:
        this.expires = rt(e);
        break;
      case U.UsageCount:
        this.usageCount = vt(e);
        break;
      case U.LocationChanged:
        this.locationChanged = Ve(e);
        break;
    }
  }
  clone() {
    const e = new St();
    return e.creationTime = this.creationTime, e.lastModTime = this.lastModTime, e.lastAccessTime = this.lastAccessTime, e.expiryTime = this.expiryTime, e.expires = this.expires, e.usageCount = this.usageCount, e.locationChanged = this.locationChanged, e;
  }
  update() {
    const e = /* @__PURE__ */ new Date();
    this.lastModTime = e, this.lastAccessTime = e;
  }
  write(e, t) {
    const r = W(e, U.Times);
    t.setXmlDate(W(r, U.CreationTime), this.creationTime), t.setXmlDate(W(r, U.LastModTime), this.lastModTime), t.setXmlDate(
      W(r, U.LastAccessTime),
      this.lastAccessTime
    ), t.setXmlDate(W(r, U.ExpiryTime), this.expiryTime), it(W(r, U.Expires), this.expires), Et(W(r, U.UsageCount), this.usageCount), t.setXmlDate(
      W(r, U.LocationChanged),
      this.locationChanged
    );
  }
  static create() {
    const e = new St(), t = /* @__PURE__ */ new Date();
    return e.creationTime = t, e.lastModTime = t, e.lastAccessTime = t, e.expiryTime = t, e.expires = !1, e.usageCount = 0, e.locationChanged = t, e;
  }
  static read(e) {
    const t = new St();
    for (let r = 0, s = e.childNodes, n = s.length; r < n; r++) {
      const u = s[r];
      u.tagName && t.readNode(u);
    }
    return t;
  }
}
class nr {
  static read(e) {
    const t = /* @__PURE__ */ new Map();
    for (let r = 0, s = e.childNodes, n = s.length; r < n; r++) {
      const u = s[r];
      u.tagName === U.StringDictExItem && this.readItem(u, t);
    }
    return t;
  }
  static write(e, t, r) {
    if (!r)
      return;
    const s = W(e, U.CustomData);
    for (const [n, u] of r)
      if (u != null && u.value) {
        const o = W(s, U.StringDictExItem);
        _e(W(o, U.Key), n), _e(W(o, U.Value), u.value), u.lastModified && t.kdbx.versionIsAtLeast(4, 1) && Zr(
          W(o, U.LastModTime),
          u.lastModified
        );
      }
  }
  static readItem(e, t) {
    let r, s, n;
    for (let u = 0, o = e.childNodes, p = o.length; u < p; u++) {
      const y = o[u];
      switch (y.tagName) {
        case U.Key:
          r = Se(y);
          break;
        case U.Value:
          s = Se(y);
          break;
        case U.LastModTime:
          n = Ve(y);
          break;
      }
    }
    if (r && s !== void 0) {
      const u = { value: s };
      n && (u.lastModified = n), t.set(r, u);
    }
  }
}
class We {
  constructor() {
    this.uuid = new Ne(), this.tags = [], this.times = new St(), this.fields = /* @__PURE__ */ new Map(), this.binaries = /* @__PURE__ */ new Map(), this.autoType = {
      enabled: !0,
      obfuscation: dr.None,
      items: []
    }, this.history = [];
  }
  get lastModTime() {
    var e;
    return ((e = this.times.lastModTime) == null ? void 0 : e.getTime()) ?? 0;
  }
  get locationChanged() {
    var e;
    return ((e = this.times.locationChanged) == null ? void 0 : e.getTime()) ?? 0;
  }
  readNode(e, t) {
    switch (e.tagName) {
      case U.Uuid:
        this.uuid = Je(e) ?? new Ne();
        break;
      case U.Icon:
        this.icon = vt(e) || Gt.Key;
        break;
      case U.CustomIconID:
        this.customIcon = Je(e);
        break;
      case U.FgColor:
        this.fgColor = Se(e);
        break;
      case U.BgColor:
        this.bgColor = Se(e);
        break;
      case U.OverrideUrl:
        this.overrideUrl = Se(e);
        break;
      case U.Tags:
        this.tags = Fn(e);
        break;
      case U.Times:
        this.times = St.read(e);
        break;
      case U.String:
        this.readField(e);
        break;
      case U.Binary:
        this.readBinary(e, t);
        break;
      case U.AutoType:
        this.readAutoType(e);
        break;
      case U.History:
        this.readHistory(e, t);
        break;
      case U.CustomData:
        this.readCustomData(e);
        break;
      case U.QualityCheck:
        this.qualityCheck = rt(e) ?? void 0;
        break;
      case U.PreviousParentGroup:
        this.previousParentGroup = Je(e);
        break;
    }
  }
  readField(e) {
    const t = yt(e, U.Key), r = yt(e, U.Value);
    if (t && r) {
      const s = Se(t), n = Za(r);
      s && this.fields.set(s, n || "");
    }
  }
  writeFields(e) {
    for (const [t, r] of this.fields)
      if (r != null) {
        const s = W(e, U.String);
        _e(W(s, U.Key), t), Qa(W(s, U.Value), r);
      }
  }
  readBinary(e, t) {
    const r = yt(e, U.Key), s = yt(e, U.Value);
    if (r && s) {
      const n = Se(r), u = Un(s);
      if (n && u)
        if (gt.isKdbxBinaryRef(u)) {
          const o = t.kdbx.binaries.getByRef(u);
          o && this.binaries.set(n, o);
        } else
          this.binaries.set(n, u);
    }
  }
  writeBinaries(e, t) {
    for (const [r, s] of this.binaries) {
      let n;
      if (gt.isKdbxBinaryWithHash(s)) {
        const o = t.kdbx.binaries.getRefByHash(s.hash);
        if (!o)
          return;
        n = o;
      } else
        n = s;
      const u = W(e, U.Binary);
      _e(W(u, U.Key), r), Rn(W(u, U.Value), n);
    }
  }
  readAutoType(e) {
    for (let t = 0, r = e.childNodes, s = r.length; t < s; t++) {
      const n = r[t];
      switch (n.tagName) {
        case U.AutoTypeEnabled:
          this.autoType.enabled = rt(n) ?? !0;
          break;
        case U.AutoTypeObfuscation:
          this.autoType.obfuscation = vt(n) || dr.None;
          break;
        case U.AutoTypeDefaultSeq:
          this.autoType.defaultSequence = Se(n);
          break;
        case U.AutoTypeItem:
          this.readAutoTypeItem(n);
          break;
      }
    }
  }
  readAutoTypeItem(e) {
    let t = "", r = "";
    for (let s = 0, n = e.childNodes, u = n.length; s < u; s++) {
      const o = n[s];
      switch (o.tagName) {
        case U.Window:
          t = Se(o) || "";
          break;
        case U.KeystrokeSequence:
          r = Se(o) || "";
          break;
      }
    }
    t && r && this.autoType.items.push({ window: t, keystrokeSequence: r });
  }
  writeAutoType(e) {
    const t = W(e, U.AutoType);
    it(
      W(t, U.AutoTypeEnabled),
      this.autoType.enabled
    ), Et(
      W(t, U.AutoTypeObfuscation),
      this.autoType.obfuscation || dr.None
    ), this.autoType.defaultSequence && _e(
      W(t, U.AutoTypeDefaultSeq),
      this.autoType.defaultSequence
    );
    for (let r = 0; r < this.autoType.items.length; r++) {
      const s = this.autoType.items[r], n = W(t, U.AutoTypeItem);
      _e(W(n, U.Window), s.window), _e(
        W(n, U.KeystrokeSequence),
        s.keystrokeSequence
      );
    }
  }
  readHistory(e, t) {
    for (let r = 0, s = e.childNodes, n = s.length; r < n; r++) {
      const u = s[r];
      switch (u.tagName) {
        case U.Entry:
          this.history.push(We.read(u, t));
          break;
      }
    }
  }
  writeHistory(e, t) {
    const r = W(e, U.History);
    for (const s of this.history)
      s.write(r, t);
  }
  readCustomData(e) {
    this.customData = nr.read(e);
  }
  writeCustomData(e, t) {
    this.customData && nr.write(e, t, this.customData);
  }
  setField(e, t, r = !1) {
    this.fields.set(e, r ? De.fromString(t) : t);
  }
  addHistoryTombstone(e, t) {
    this._editState || (this._editState = { added: [], deleted: [] }), this._editState[e ? "added" : "deleted"].push(t.getTime());
  }
  write(e, t) {
    const r = W(e, U.Entry);
    Ye(W(r, U.Uuid), this.uuid), Et(W(r, U.Icon), this.icon || Gt.Key), this.customIcon && Ye(
      W(r, U.CustomIconID),
      this.customIcon
    ), _e(W(r, U.FgColor), this.fgColor), _e(W(r, U.BgColor), this.bgColor), _e(W(r, U.OverrideUrl), this.overrideUrl), Tn(W(r, U.Tags), this.tags), typeof this.qualityCheck == "boolean" && t.kdbx.versionIsAtLeast(4, 1) && it(
      W(r, U.QualityCheck),
      this.qualityCheck
    ), this.previousParentGroup !== void 0 && t.kdbx.versionIsAtLeast(4, 1) && Ye(
      W(r, U.PreviousParentGroup),
      this.previousParentGroup
    ), this.times.write(r, t), this.writeFields(r), this.writeBinaries(r, t), this.writeAutoType(r), this.writeCustomData(r, t), e.tagName !== U.History && this.writeHistory(r, t);
  }
  pushHistory() {
    const e = new We();
    e.copyFrom(this), this.history.push(e), e.times.lastModTime && this.addHistoryTombstone(!0, e.times.lastModTime);
  }
  removeHistory(e, t = 1) {
    for (let r = e; r < e + t; r++)
      if (r < this.history.length) {
        const s = this.history[r].times.lastModTime;
        s && this.addHistoryTombstone(!1, s);
      }
    this.history.splice(e, t);
  }
  copyFrom(e) {
    this.uuid = e.uuid, this.icon = e.icon, this.customIcon = e.customIcon, this.fgColor = e.fgColor, this.bgColor = e.bgColor, this.overrideUrl = e.overrideUrl, this.tags = e.tags.slice(), this.times = e.times.clone(), this.fields = /* @__PURE__ */ new Map();
    for (const [t, r] of e.fields)
      r instanceof De ? this.fields.set(t, r.clone()) : this.fields.set(t, r);
    this.binaries = /* @__PURE__ */ new Map();
    for (const [t, r] of e.binaries)
      r instanceof De ? this.binaries.set(t, r.clone()) : gt.isKdbxBinaryWithHash(r) ? this.binaries.set(t, { hash: r.hash, value: r.value }) : this.binaries.set(t, r);
    this.autoType = JSON.parse(JSON.stringify(e.autoType));
  }
  merge(e) {
    const t = e.remoteEntries.get(this.uuid.id);
    if (!t)
      return;
    const r = t.history.slice();
    if (this.lastModTime < t.lastModTime)
      this.pushHistory(), this.copyFrom(t);
    else if (this.lastModTime > t.lastModTime && !this.history.some((n) => n.lastModTime === t.lastModTime)) {
      const n = new We();
      n.copyFrom(t), r.push(n);
    }
    this.history = this.mergeHistory(r, t.lastModTime);
  }
  /**
   * Merge entry history with remote entry history
   * Tombstones are stored locally and must be immediately discarded by replica after successful upstream push.
   * It's client responsibility, to save and load tombstones for local replica, and to clear them after successful upstream push.
   *
   * Implements remove-win OR-set CRDT with local tombstones stored in _editState.
   *
   * Format doesn't allow saving tombstones for history entries, so they are stored locally.
   * Any unmodified state from past or modifications of current state synced with central upstream will be successfully merged.
   * Assumes there's only one central upstream, may produce inconsistencies while merging outdated replica outside main upstream.
   * Phantom entries and phantom deletions will appear if remote replica checked out an old state and has just added a new state.
   * If a client is using central upstream for sync, the remote replica must first sync it state and
   * only after it update the upstream, so this should never happen.
   *
   * References:
   *
   * An Optimized Conflict-free Replicated Set arXiv:1210.3368 [cs.DC]
   * http://arxiv.org/abs/1210.3368
   *
   * Gene T. J. Wuu and Arthur J. Bernstein. Efficient solutions to the replicated log and dictionary
   * problems. In Symp. on Principles of Dist. Comp. (PODC), pages 233–242, Vancouver, BC, Canada, August 1984.
   * https://pages.lip6.fr/Marc.Shapiro/papers/RR-7687.pdf
   */
  mergeHistory(e, t) {
    this.history.sort((u, o) => u.lastModTime - o.lastModTime), e.sort((u, o) => u.lastModTime - o.lastModTime);
    let r = 0, s = 0;
    const n = [];
    for (; r < this.history.length || s < e.length; ) {
      const u = this.history[r], o = e[s], p = u && u.lastModTime, y = o && o.lastModTime;
      if (p === y) {
        n.push(u), r++, s++;
        continue;
      }
      if (!u || p > y) {
        if (!this._editState || this._editState.deleted.indexOf(y) < 0) {
          const f = new We();
          f.copyFrom(o), n.push(f);
        }
        s++;
        continue;
      }
      (this._editState && this._editState.added.indexOf(p) >= 0 || p > t) && n.push(u), r++;
    }
    return n;
  }
  static create(e, t) {
    const r = new We();
    return r.uuid = Ne.random(), r.icon = Gt.Key, r.times = St.create(), r.parentGroup = t, r.setField("Title", "", e.memoryProtection.title), r.setField("UserName", e.defaultUser || "", e.memoryProtection.userName), r.setField("Password", "", e.memoryProtection.password), r.setField("URL", "", e.memoryProtection.url), r.setField("Notes", "", e.memoryProtection.notes), r.autoType.enabled = typeof t.enableAutoType == "boolean" ? t.enableAutoType : !0, r.autoType.obfuscation = dr.None, r;
  }
  static read(e, t, r) {
    const s = new We();
    for (let n = 0, u = e.childNodes, o = u.length; n < o; n++) {
      const p = u[n];
      p.tagName && s.readNode(p, t);
    }
    if (s.uuid.empty) {
      s.uuid = Ne.random();
      for (let n = 0; n < s.history.length; n++)
        s.history[n].uuid = s.uuid;
    }
    return s.parentGroup = r, s;
  }
}
class ze {
  constructor() {
    this.uuid = new Ne(), this.tags = [], this.times = new St(), this.groups = [], this.entries = [];
  }
  get lastModTime() {
    var e;
    return ((e = this.times.lastModTime) == null ? void 0 : e.getTime()) ?? 0;
  }
  get locationChanged() {
    var e;
    return ((e = this.times.locationChanged) == null ? void 0 : e.getTime()) ?? 0;
  }
  readNode(e, t) {
    switch (e.tagName) {
      case U.Uuid:
        this.uuid = Je(e) ?? new Ne();
        break;
      case U.Name:
        this.name = Se(e);
        break;
      case U.Notes:
        this.notes = Se(e);
        break;
      case U.Icon:
        this.icon = vt(e);
        break;
      case U.CustomIconID:
        this.customIcon = Je(e);
        break;
      case U.Tags:
        this.tags = Fn(e);
        break;
      case U.Times:
        this.times = St.read(e);
        break;
      case U.IsExpanded:
        this.expanded = rt(e) ?? void 0;
        break;
      case U.GroupDefaultAutoTypeSeq:
        this.defaultAutoTypeSeq = Se(e);
        break;
      case U.EnableAutoType:
        this.enableAutoType = rt(e);
        break;
      case U.EnableSearching:
        this.enableSearching = rt(e);
        break;
      case U.LastTopVisibleEntry:
        this.lastTopVisibleEntry = Je(e);
        break;
      case U.Group:
        this.groups.push(ze.read(e, t, this));
        break;
      case U.Entry:
        this.entries.push(We.read(e, t, this));
        break;
      case U.CustomData:
        this.customData = nr.read(e);
        break;
      case U.PreviousParentGroup:
        this.previousParentGroup = Je(e);
        break;
    }
  }
  write(e, t) {
    const r = W(e, U.Group);
    Ye(W(r, U.Uuid), this.uuid), _e(W(r, U.Name), this.name), _e(W(r, U.Notes), this.notes), Et(W(r, U.Icon), this.icon), this.tags.length && t.kdbx.versionIsAtLeast(4, 1) && Tn(W(r, U.Tags), this.tags), this.customIcon && Ye(
      W(r, U.CustomIconID),
      this.customIcon
    ), this.previousParentGroup !== void 0 && t.kdbx.versionIsAtLeast(4, 1) && Ye(
      W(r, U.PreviousParentGroup),
      this.previousParentGroup
    ), this.customData && nr.write(r, t, this.customData), this.times.write(r, t), it(W(r, U.IsExpanded), this.expanded), _e(
      W(r, U.GroupDefaultAutoTypeSeq),
      this.defaultAutoTypeSeq
    ), it(
      W(r, U.EnableAutoType),
      this.enableAutoType
    ), it(
      W(r, U.EnableSearching),
      this.enableSearching
    ), Ye(
      W(r, U.LastTopVisibleEntry),
      this.lastTopVisibleEntry
    );
    for (const s of this.groups)
      s.write(r, t);
    for (const s of this.entries)
      s.write(r, t);
  }
  *allGroups() {
    yield this;
    for (const e of this.groups)
      for (const t of e.allGroups())
        yield t;
  }
  *allEntries() {
    for (const e of this.allGroups())
      for (const t of e.entries)
        yield t;
  }
  *allGroupsAndEntries() {
    yield this;
    for (const e of this.entries)
      yield e;
    for (const e of this.groups)
      for (const t of e.allGroupsAndEntries())
        yield t;
  }
  merge(e) {
    const t = e.remoteGroups.get(this.uuid.id);
    if (t) {
      t.lastModTime > this.lastModTime && this.copyFrom(t), this.groups = this.mergeCollection(
        this.groups,
        t.groups,
        e.groups,
        e.remoteGroups,
        e.deleted
      ), this.entries = this.mergeCollection(
        this.entries,
        t.entries,
        e.entries,
        e.remoteEntries,
        e.deleted
      );
      for (const r of this.groups)
        r.merge(e);
      for (const r of this.entries)
        r.merge(e);
    }
  }
  /**
   * Merge object collection with remote collection
   * Implements 2P-set CRDT with tombstones stored in objectMap.deleted
   * Assumes tombstones are already merged
   */
  mergeCollection(e, t, r, s, n) {
    const u = [];
    for (const p of e) {
      if (!p.uuid || n.has(p.uuid.id))
        continue;
      const y = s.get(p.uuid.id);
      y ? y.locationChanged <= p.locationChanged && u.push(p) : u.push(p);
    }
    let o = -1;
    for (const p of t) {
      if (o++, !p.uuid || n.has(p.uuid.id))
        continue;
      const y = r.get(p.uuid.id);
      if (y && p.locationChanged > y.locationChanged)
        y.parentGroup = this, u.splice(ze.findInsertIx(u, t, o), 0, y);
      else if (!y) {
        let f;
        if (p instanceof ze) {
          const v = new ze();
          v.copyFrom(p), f = v;
        } else if (p instanceof We) {
          const v = new We();
          v.copyFrom(p), f = v;
        } else
          continue;
        f.parentGroup = this, u.splice(ze.findInsertIx(u, t, o), 0, f);
      }
    }
    return u;
  }
  /**
   * Finds a best place to insert new item into collection
   */
  static findInsertIx(e, t, r) {
    let s = e.length, n = -1;
    for (let u = 0; u <= e.length; u++) {
      let o = 0;
      const p = r > 0 ? t[r - 1].uuid.id : void 0, y = r + 1 < t.length ? t[r + 1].uuid.id : void 0, f = u > 0 ? e[u - 1].uuid.id : void 0, v = u < e.length ? e[u].uuid.id : void 0;
      !p && !f ? o += 1 : p === f && (o += 5), !y && !v ? o += 2 : y === v && (o += 5), o > n && (s = u, n = o);
    }
    return s;
  }
  copyFrom(e) {
    this.uuid = e.uuid, this.name = e.name, this.notes = e.notes, this.icon = e.icon, this.customIcon = e.customIcon, this.times = e.times.clone(), this.expanded = e.expanded, this.defaultAutoTypeSeq = e.defaultAutoTypeSeq, this.enableAutoType = e.enableAutoType, this.enableSearching = e.enableSearching, this.lastTopVisibleEntry = e.lastTopVisibleEntry;
  }
  static create(e, t) {
    const r = new ze();
    return r.uuid = Ne.random(), r.icon = Gt.Folder, r.times = St.create(), r.name = e, r.parentGroup = t, r.expanded = !0, r.enableAutoType = null, r.enableSearching = null, r.lastTopVisibleEntry = new Ne(), r;
  }
  static read(e, t, r) {
    const s = new ze();
    for (let n = 0, u = e.childNodes, o = u.length; n < o; n++) {
      const p = u[n];
      p.tagName && s.readNode(p, t);
    }
    return s.uuid.empty && (s.uuid = Ne.random()), s.parentGroup = r, s;
  }
}
const en = {
  Generator: "KdbxWeb"
};
class rr {
  constructor() {
    this._memoryProtection = {}, this.customData = /* @__PURE__ */ new Map(), this.customIcons = /* @__PURE__ */ new Map();
  }
  get editState() {
    return this._editState;
  }
  set editState(e) {
    this._editState = e;
  }
  getOrCreateEditState() {
    return this._editState || (this._editState = {}), this._editState;
  }
  get name() {
    return this._name;
  }
  set name(e) {
    e !== this._name && (this._name = e, this.nameChanged = /* @__PURE__ */ new Date());
  }
  get desc() {
    return this._desc;
  }
  set desc(e) {
    e !== this._desc && (this._desc = e, this.descChanged = /* @__PURE__ */ new Date());
  }
  get defaultUser() {
    return this._defaultUser;
  }
  set defaultUser(e) {
    e !== this._defaultUser && (this._defaultUser = e, this.defaultUserChanged = /* @__PURE__ */ new Date());
  }
  get mntncHistoryDays() {
    return this._mntncHistoryDays;
  }
  set mntncHistoryDays(e) {
    e !== this._mntncHistoryDays && (this._mntncHistoryDays = e, this.getOrCreateEditState().mntncHistoryDaysChanged = /* @__PURE__ */ new Date());
  }
  get color() {
    return this._color;
  }
  set color(e) {
    e !== this._color && (this._color = e, this.getOrCreateEditState().colorChanged = /* @__PURE__ */ new Date());
  }
  get keyChangeRec() {
    return this._keyChangeRec;
  }
  set keyChangeRec(e) {
    e !== this._keyChangeRec && (this._keyChangeRec = e, this.getOrCreateEditState().keyChangeRecChanged = /* @__PURE__ */ new Date());
  }
  get keyChangeForce() {
    return this._keyChangeForce;
  }
  set keyChangeForce(e) {
    e !== this._keyChangeForce && (this._keyChangeForce = e, this.getOrCreateEditState().keyChangeForceChanged = /* @__PURE__ */ new Date());
  }
  get recycleBinEnabled() {
    return this._recycleBinEnabled;
  }
  set recycleBinEnabled(e) {
    e !== this._recycleBinEnabled && (this._recycleBinEnabled = e, this.recycleBinChanged = /* @__PURE__ */ new Date());
  }
  get recycleBinUuid() {
    return this._recycleBinUuid;
  }
  set recycleBinUuid(e) {
    e !== this._recycleBinUuid && (this._recycleBinUuid = e, this.recycleBinChanged = /* @__PURE__ */ new Date());
  }
  get entryTemplatesGroup() {
    return this._entryTemplatesGroup;
  }
  set entryTemplatesGroup(e) {
    e !== this._entryTemplatesGroup && (this._entryTemplatesGroup = e, this.entryTemplatesGroupChanged = /* @__PURE__ */ new Date());
  }
  get historyMaxItems() {
    return this._historyMaxItems;
  }
  set historyMaxItems(e) {
    e !== this._historyMaxItems && (this._historyMaxItems = e, this.getOrCreateEditState().historyMaxItemsChanged = /* @__PURE__ */ new Date());
  }
  get historyMaxSize() {
    return this._historyMaxSize;
  }
  set historyMaxSize(e) {
    e !== this._historyMaxSize && (this._historyMaxSize = e, this.getOrCreateEditState().historyMaxSizeChanged = /* @__PURE__ */ new Date());
  }
  get lastSelectedGroup() {
    return this._lastSelectedGroup;
  }
  set lastSelectedGroup(e) {
    e !== this._lastSelectedGroup && (this._lastSelectedGroup = e, this.getOrCreateEditState().lastSelectedGroupChanged = /* @__PURE__ */ new Date());
  }
  get lastTopVisibleGroup() {
    return this._lastTopVisibleGroup;
  }
  set lastTopVisibleGroup(e) {
    e !== this._lastTopVisibleGroup && (this._lastTopVisibleGroup = e, this.getOrCreateEditState().lastTopVisibleGroupChanged = /* @__PURE__ */ new Date());
  }
  get memoryProtection() {
    return this._memoryProtection;
  }
  set memoryProtection(e) {
    e !== this._memoryProtection && (this._memoryProtection = e, this.getOrCreateEditState().memoryProtectionChanged = /* @__PURE__ */ new Date());
  }
  readNode(e, t) {
    switch (e.tagName) {
      case U.Generator:
        this.generator = Se(e);
        break;
      case U.HeaderHash:
        this.headerHash = Vr(e);
        break;
      case U.SettingsChanged:
        this.settingsChanged = Ve(e);
        break;
      case U.DbName:
        this._name = Se(e);
        break;
      case U.DbNameChanged:
        this.nameChanged = Ve(e);
        break;
      case U.DbDesc:
        this._desc = Se(e);
        break;
      case U.DbDescChanged:
        this.descChanged = Ve(e);
        break;
      case U.DbDefaultUser:
        this._defaultUser = Se(e);
        break;
      case U.DbDefaultUserChanged:
        this.defaultUserChanged = Ve(e);
        break;
      case U.DbMntncHistoryDays:
        this._mntncHistoryDays = vt(e);
        break;
      case U.DbColor:
        this._color = Se(e);
        break;
      case U.DbKeyChanged:
        this.keyChanged = Ve(e);
        break;
      case U.DbKeyChangeRec:
        this._keyChangeRec = vt(e);
        break;
      case U.DbKeyChangeForce:
        this._keyChangeForce = vt(e);
        break;
      case U.RecycleBinEnabled:
        this._recycleBinEnabled = rt(e) ?? void 0;
        break;
      case U.RecycleBinUuid:
        this._recycleBinUuid = Je(e);
        break;
      case U.RecycleBinChanged:
        this.recycleBinChanged = Ve(e);
        break;
      case U.EntryTemplatesGroup:
        this._entryTemplatesGroup = Je(e);
        break;
      case U.EntryTemplatesGroupChanged:
        this.entryTemplatesGroupChanged = Ve(e);
        break;
      case U.HistoryMaxItems:
        this._historyMaxItems = vt(e);
        break;
      case U.HistoryMaxSize:
        this._historyMaxSize = vt(e);
        break;
      case U.LastSelectedGroup:
        this._lastSelectedGroup = Je(e);
        break;
      case U.LastTopVisibleGroup:
        this._lastTopVisibleGroup = Je(e);
        break;
      case U.MemoryProt:
        this.readMemoryProtection(e);
        break;
      case U.CustomIcons:
        this.readCustomIcons(e);
        break;
      case U.Binaries:
        this.readBinaries(e, t);
        break;
      case U.CustomData:
        this.readCustomData(e);
        break;
    }
  }
  readMemoryProtection(e) {
    for (let t = 0, r = e.childNodes, s = r.length; t < s; t++) {
      const n = r[t];
      switch (n.tagName) {
        case U.ProtTitle:
          this.memoryProtection.title = rt(n) ?? void 0;
          break;
        case U.ProtUserName:
          this.memoryProtection.userName = rt(n) ?? void 0;
          break;
        case U.ProtPassword:
          this.memoryProtection.password = rt(n) ?? void 0;
          break;
        case U.ProtUrl:
          this.memoryProtection.url = rt(n) ?? void 0;
          break;
        case U.ProtNotes:
          this.memoryProtection.notes = rt(n) ?? void 0;
          break;
      }
    }
  }
  writeMemoryProtection(e) {
    const t = W(e, U.MemoryProt);
    it(
      W(t, U.ProtTitle),
      this.memoryProtection.title
    ), it(
      W(t, U.ProtUserName),
      this.memoryProtection.userName
    ), it(
      W(t, U.ProtPassword),
      this.memoryProtection.password
    ), it(
      W(t, U.ProtUrl),
      this.memoryProtection.url
    ), it(
      W(t, U.ProtNotes),
      this.memoryProtection.notes
    );
  }
  readCustomIcons(e) {
    for (let t = 0, r = e.childNodes, s = r.length; t < s; t++) {
      const n = r[t];
      n.tagName === U.CustomIconItem && this.readCustomIcon(n);
    }
  }
  readCustomIcon(e) {
    let t, r, s, n;
    for (let u = 0, o = e.childNodes, p = o.length; u < p; u++) {
      const y = o[u];
      switch (y.tagName) {
        case U.CustomIconItemID:
          t = Je(y);
          break;
        case U.CustomIconItemData:
          r = Vr(y);
          break;
        case U.CustomIconItemName:
          s = Se(y) ?? void 0;
          break;
        case U.LastModTime:
          n = Ve(y);
          break;
      }
    }
    t && r && this.customIcons.set(t.id, { data: r, name: s, lastModified: n });
  }
  writeCustomIcons(e, t) {
    const r = W(e, U.CustomIcons);
    for (const [s, { data: n, name: u, lastModified: o }] of this.customIcons)
      if (n) {
        const p = W(r, U.CustomIconItem);
        Ye(
          W(p, U.CustomIconItemID),
          s
        ), _r(
          W(p, U.CustomIconItemData),
          n
        ), t.kdbx.versionIsAtLeast(4, 1) && (u && _e(
          W(p, U.CustomIconItemName),
          u
        ), o && Zr(
          W(p, U.LastModTime),
          o
        ));
      }
  }
  readBinaries(e, t) {
    for (let r = 0, s = e.childNodes, n = s.length; r < n; r++) {
      const u = s[r];
      u.tagName === U.Binary && this.readBinary(u, t);
    }
  }
  readBinary(e, t) {
    const r = e.getAttribute(Le.Id), s = Un(e);
    if (r && s) {
      if (gt.isKdbxBinaryRef(s))
        throw new H(j.FileCorrupt, "binary ref in meta");
      t.kdbx.binaries.addWithId(r, s);
    }
  }
  writeBinaries(e, t) {
    const r = W(e, U.Binaries), s = t.kdbx.binaries.getAll();
    for (const n of s) {
      const u = W(r, U.Binary);
      u.setAttribute(Le.Id, n.ref), Rn(u, n.value);
    }
  }
  readCustomData(e) {
    this.customData = nr.read(e);
  }
  writeCustomData(e, t) {
    nr.write(e, t, this.customData);
  }
  write(e, t) {
    this.generator = en.Generator;
    const r = W(e, U.Meta);
    _e(W(r, U.Generator), en.Generator), t.kdbx.versionMajor < 4 ? _r(
      W(r, U.HeaderHash),
      this.headerHash
    ) : this.settingsChanged && t.setXmlDate(
      W(r, U.SettingsChanged),
      this.settingsChanged
    ), _e(W(r, U.DbName), this.name), t.setXmlDate(W(r, U.DbNameChanged), this.nameChanged), _e(W(r, U.DbDesc), this.desc), t.setXmlDate(W(r, U.DbDescChanged), this.descChanged), _e(
      W(r, U.DbDefaultUser),
      this.defaultUser
    ), t.setXmlDate(
      W(r, U.DbDefaultUserChanged),
      this.defaultUserChanged
    ), Et(
      W(r, U.DbMntncHistoryDays),
      this.mntncHistoryDays
    ), _e(W(r, U.DbColor), this.color), t.setXmlDate(W(r, U.DbKeyChanged), this.keyChanged), Et(
      W(r, U.DbKeyChangeRec),
      this.keyChangeRec
    ), Et(
      W(r, U.DbKeyChangeForce),
      this.keyChangeForce
    ), it(
      W(r, U.RecycleBinEnabled),
      this.recycleBinEnabled
    ), Ye(
      W(r, U.RecycleBinUuid),
      this.recycleBinUuid
    ), t.setXmlDate(
      W(r, U.RecycleBinChanged),
      this.recycleBinChanged
    ), Ye(
      W(r, U.EntryTemplatesGroup),
      this.entryTemplatesGroup
    ), t.setXmlDate(
      W(r, U.EntryTemplatesGroupChanged),
      this.entryTemplatesGroupChanged
    ), Et(
      W(r, U.HistoryMaxItems),
      this.historyMaxItems
    ), Et(
      W(r, U.HistoryMaxSize),
      this.historyMaxSize
    ), Ye(
      W(r, U.LastSelectedGroup),
      this.lastSelectedGroup
    ), Ye(
      W(r, U.LastTopVisibleGroup),
      this.lastTopVisibleGroup
    ), this.writeMemoryProtection(r), this.writeCustomIcons(r, t), (t.exportXml || t.kdbx.versionMajor < 4) && this.writeBinaries(r, t), this.writeCustomData(r, t);
  }
  merge(e, t) {
    var r, s, n, u, o, p;
    this.needUpdate(e.nameChanged, this.nameChanged) && (this._name = e.name, this.nameChanged = e.nameChanged), this.needUpdate(e.descChanged, this.descChanged) && (this._desc = e.desc, this.descChanged = e.descChanged), this.needUpdate(e.defaultUserChanged, this.defaultUserChanged) && (this._defaultUser = e.defaultUser, this.defaultUserChanged = e.defaultUserChanged), this.needUpdate(e.keyChanged, this.keyChanged) && (this.keyChanged = e.keyChanged), this.needUpdate(e.settingsChanged, this.settingsChanged) && (this.settingsChanged = e.settingsChanged), this.needUpdate(e.recycleBinChanged, this.recycleBinChanged) && (this._recycleBinEnabled = e.recycleBinEnabled, this._recycleBinUuid = e.recycleBinUuid, this.recycleBinChanged = e.recycleBinChanged), this.needUpdate(e.entryTemplatesGroupChanged, this.entryTemplatesGroupChanged) && (this._entryTemplatesGroup = e.entryTemplatesGroup, this.entryTemplatesGroupChanged = e.entryTemplatesGroupChanged), this.mergeMapWithDates(this.customData, e.customData, t), this.mergeMapWithDates(this.customIcons, e.customIcons, t), (r = this._editState) != null && r.historyMaxItemsChanged || (this.historyMaxItems = e.historyMaxItems), (s = this._editState) != null && s.historyMaxSizeChanged || (this.historyMaxSize = e.historyMaxSize), (n = this._editState) != null && n.keyChangeRecChanged || (this.keyChangeRec = e.keyChangeRec), (u = this._editState) != null && u.keyChangeForceChanged || (this.keyChangeForce = e.keyChangeForce), (o = this._editState) != null && o.mntncHistoryDaysChanged || (this.mntncHistoryDays = e.mntncHistoryDays), (p = this._editState) != null && p.colorChanged || (this.color = e.color);
  }
  mergeMapWithDates(e, t, r) {
    for (const [s, n] of t) {
      const u = e.get(s);
      u ? u.lastModified && n.lastModified && n.lastModified > u.lastModified && e.set(s, n) : r.deleted.has(s) || e.set(s, n);
    }
  }
  needUpdate(e, t) {
    return e ? t ? e > t : !0 : !1;
  }
  /**
   * Creates new meta
   * @returns {KdbxMeta}
   */
  static create() {
    const e = /* @__PURE__ */ new Date(), t = new rr();
    return t.generator = en.Generator, t.settingsChanged = e, t.mntncHistoryDays = Vt.MntncHistoryDays, t.recycleBinEnabled = !0, t.historyMaxItems = Vt.HistoryMaxItems, t.historyMaxSize = Vt.HistoryMaxSize, t.nameChanged = e, t.descChanged = e, t.defaultUserChanged = e, t.recycleBinChanged = e, t.keyChangeRec = -1, t.keyChangeForce = -1, t.entryTemplatesGroup = new Ne(), t.entryTemplatesGroupChanged = e, t.memoryProtection = {
      title: !1,
      userName: !1,
      password: !0,
      url: !1,
      notes: !1
    }, t;
  }
  static read(e, t) {
    const r = new rr();
    for (let s = 0, n = e.childNodes, u = n.length; s < u; s++) {
      const o = n[s];
      o.tagName && r.readNode(o, t);
    }
    return r;
  }
}
class Yt {
  constructor(e, t, r) {
    this.ready = Promise.all([
      this.setPassword(e),
      this.setKeyFile(t),
      this.setChallengeResponse(r)
    ]).then(() => this);
  }
  setPassword(e) {
    return e ? e instanceof De ? e.getHash().then((t) => {
      this.passwordHash = De.fromBinary(t);
    }) : Promise.reject(new H(j.InvalidArg, "password")) : (this.passwordHash = void 0, Promise.resolve());
  }
  setKeyFile(e) {
    if (e && !(e instanceof ArrayBuffer) && !(e instanceof Uint8Array))
      return Promise.reject(new H(j.InvalidArg, "keyFile"));
    if (e) {
      if (e.byteLength === 32)
        return this.keyFileHash = De.fromBinary(ae(e)), Promise.resolve();
      let t, r;
      try {
        const s = Wt(ae(e));
        if (/^[a-f\d]{64}$/i.exec(s)) {
          const y = gr(s);
          return this.keyFileHash = De.fromBinary(y), Promise.resolve();
        }
        const n = tr(s.trim()), u = yt(n.documentElement, "Meta");
        if (!u)
          return Promise.reject(
            new H(j.InvalidArg, "key file without meta")
          );
        const o = yt(u, "Version");
        if (!(o != null && o.textContent))
          return Promise.reject(
            new H(j.InvalidArg, "key file without version")
          );
        t = +o.textContent.split(".")[0];
        const p = yt(n.documentElement, "Key");
        if (!p)
          return Promise.reject(
            new H(j.InvalidArg, "key file without key")
          );
        if (r = yt(p, "Data"), !(r != null && r.textContent))
          return Promise.reject(
            new H(j.InvalidArg, "key file without key data")
          );
      } catch {
        return Ge(e).then((n) => {
          this.keyFileHash = De.fromBinary(n);
        });
      }
      switch (t) {
        case 1:
          this.keyFileHash = De.fromBinary(lt(r.textContent));
          break;
        case 2: {
          const s = gr(r.textContent.replace(/\s+/g, "")), n = r.getAttribute("Hash");
          return Ge(s).then((u) => {
            if (pr(
              new Uint8Array(u).subarray(0, 4)
            ).toUpperCase() !== n)
              throw new H(
                j.FileCorrupt,
                "key file data hash mismatch"
              );
            this.keyFileHash = De.fromBinary(s);
          });
        }
        default:
          return Promise.reject(
            new H(j.FileCorrupt, "bad keyfile version")
          );
      }
    } else
      this.keyFileHash = void 0;
    return Promise.resolve();
  }
  setChallengeResponse(e) {
    return this._challengeResponse = e, Promise.resolve();
  }
  getHash(e) {
    return this.ready.then(() => this.getChallengeResponse(e).then((t) => {
      const r = [];
      this.passwordHash && r.push(this.passwordHash.getBinary()), this.keyFileHash && r.push(this.keyFileHash.getBinary()), t && r.push(new Uint8Array(t));
      const s = r.reduce((o, p) => o + p.byteLength, 0), n = new Uint8Array(s);
      let u = 0;
      for (const o of r)
        n.set(o, u), me(o), u += o.length;
      return Ge(ae(n)).then((o) => (me(n), o));
    }));
  }
  getChallengeResponse(e) {
    return Promise.resolve().then(() => !this._challengeResponse || !e ? null : this._challengeResponse(e).then((t) => Ge(ae(t)).then((r) => (me(t), r))));
  }
  static createRandomKeyFile(e = 1) {
    const r = He(32), s = He(32);
    for (let n = 0; n < 32; n++)
      r[n] ^= s[n], r[n] ^= Math.random() * 1e3 % 255;
    return Yt.createKeyFileWithHash(r, e);
  }
  static createKeyFileWithHash(e, t = 1) {
    const r = t === 2 ? "2.0" : "1.00", s = "        ";
    let n;
    if (t === 2) {
      const u = s + "    ";
      n = Ge(e).then((o) => {
        const p = pr(
          new Uint8Array(o).subarray(0, 4)
        ).toUpperCase(), y = pr(e).toUpperCase();
        let f = s + '<Data Hash="' + p + `">
`;
        for (let v = 0; v < 2; v++) {
          const m = [0, 1, 2, 3].map((w) => y.substr(v * 32 + w * 8, 8));
          f += u, f += m.join(" "), f += `
`;
        }
        return f += s + `</Data>
`, f;
      });
    } else {
      const u = s + "<Data>" + Mt(e) + `</Data>
`;
      n = Promise.resolve(u);
    }
    return n.then((u) => {
      const o = `<?xml version="1.0" encoding="utf-8"?>
<KeyFile>
    <Meta>
        <Version>` + r + `</Version>
    </Meta>
    <Key>
` + u + `    </Key>
</KeyFile>`;
      return Dt(o);
    });
  }
}
const cu = 1, lu = 256;
var ht = /* @__PURE__ */ ((i) => (i[i.UInt32 = 4] = "UInt32", i[i.UInt64 = 5] = "UInt64", i[i.Bool = 8] = "Bool", i[i.Int32 = 12] = "Int32", i[i.Int64 = 13] = "Int64", i[i.String = 24] = "String", i[i.Bytes = 66] = "Bytes", i))(ht || {});
const Kr = class Kr {
  constructor() {
    this._items = [], this._map = /* @__PURE__ */ new Map();
  }
  keys() {
    return this._items.map((e) => e.key);
  }
  get length() {
    return this._items.length;
  }
  get(e) {
    const t = this._map.get(e);
    return t ? t.value : void 0;
  }
  set(e, t, r) {
    let s;
    switch (t) {
      case 4:
        if (typeof r != "number" || r < 0)
          throw new H(j.InvalidArg);
        s = { key: e, type: t, value: r };
        break;
      case 5:
        if (!(r instanceof Ze))
          throw new H(j.InvalidArg);
        s = { key: e, type: t, value: r };
        break;
      case 8:
        if (typeof r != "boolean")
          throw new H(j.InvalidArg);
        s = { key: e, type: t, value: r };
        break;
      case 12:
        if (typeof r != "number")
          throw new H(j.InvalidArg);
        s = { key: e, type: t, value: r };
        break;
      case 13:
        if (!(r instanceof Ze))
          throw new H(j.InvalidArg);
        s = { key: e, type: t, value: r };
        break;
      case 24:
        if (typeof r != "string")
          throw new H(j.InvalidArg);
        s = { key: e, type: t, value: r };
        break;
      case 66:
        if (r instanceof Uint8Array && (r = ae(r)), !(r instanceof ArrayBuffer))
          throw new H(j.InvalidArg);
        s = { key: e, type: t, value: r };
        break;
      default:
        throw new H(j.InvalidArg);
    }
    const n = this._map.get(e);
    if (n) {
      const u = this._items.indexOf(n);
      this._items.splice(u, 1, s);
    } else
      this._items.push(s);
    this._map.set(e, s);
  }
  remove(e) {
    this._items = this._items.filter((t) => t.key !== e), this._map.delete(e);
  }
  static read(e) {
    const t = new Kr();
    t.readVersion(e);
    for (let r; r = t.readItem(e); )
      t._items.push(r), t._map.set(r.key, r);
    return t;
  }
  readVersion(e) {
    e.getUint8();
    const t = e.getUint8();
    if (t === 0 || t > cu)
      throw new H(j.InvalidVersion);
  }
  readItem(e) {
    const t = e.getUint8();
    if (!t)
      return;
    const r = e.getInt32(!0);
    if (r <= 0)
      throw new H(j.FileCorrupt, "bad key length");
    const s = Wt(e.readBytes(r)), n = e.getInt32(!0);
    if (n < 0)
      throw new H(j.FileCorrupt, "bad value length");
    switch (t) {
      case 4: {
        if (n !== 4)
          throw new H(j.FileCorrupt, "bad uint32");
        const u = e.getUint32(!0);
        return { key: s, type: t, value: u };
      }
      case 5: {
        if (n !== 8)
          throw new H(j.FileCorrupt, "bad uint64");
        const u = e.getUint32(!0), o = e.getUint32(!0), p = new Ze(u, o);
        return { key: s, type: t, value: p };
      }
      case 8: {
        if (n !== 1)
          throw new H(j.FileCorrupt, "bad bool");
        const u = e.getUint8() !== 0;
        return { key: s, type: t, value: u };
      }
      case 12: {
        if (n !== 4)
          throw new H(j.FileCorrupt, "bad int32");
        const u = e.getInt32(!0);
        return { key: s, type: t, value: u };
      }
      case 13: {
        if (n !== 8)
          throw new H(j.FileCorrupt, "bad int64");
        const u = e.getUint32(!0), o = e.getUint32(!0), p = new Ze(u, o);
        return { key: s, type: t, value: p };
      }
      case 24: {
        const u = Wt(e.readBytes(n));
        return { key: s, type: t, value: u };
      }
      case 66: {
        const u = e.readBytes(n);
        return { key: s, type: t, value: u };
      }
      default:
        throw new H(j.FileCorrupt, `bad value type: ${t}`);
    }
  }
  write(e) {
    this.writeVersion(e);
    for (const t of this._items)
      this.writeItem(e, t);
    e.setUint8(0);
  }
  writeVersion(e) {
    e.setUint16(lu, !0);
  }
  writeItem(e, t) {
    e.setUint8(t.type);
    const r = Dt(t.key);
    switch (e.setInt32(r.length, !0), e.writeBytes(r), t.type) {
      case 4:
        e.setInt32(4, !0), e.setUint32(t.value, !0);
        break;
      case 5:
        e.setInt32(8, !0), e.setUint32(t.value.lo, !0), e.setUint32(t.value.hi, !0);
        break;
      case 8:
        e.setInt32(1, !0), e.setUint8(t.value ? 1 : 0);
        break;
      case 12:
        e.setInt32(4, !0), e.setInt32(t.value, !0);
        break;
      case 13:
        e.setInt32(8, !0), e.setUint32(t.value.lo, !0), e.setUint32(t.value.hi, !0);
        break;
      case 24: {
        const s = Dt(t.value);
        e.setInt32(s.length, !0), e.writeBytes(s);
        break;
      }
      case 66: {
        const s = ae(t.value);
        e.setInt32(s.byteLength, !0), e.writeBytes(s);
        break;
      }
      default:
        throw new H(j.Unsupported);
    }
  }
};
Kr.ValueType = ht;
let Zt = Kr;
const Ur = [
  { name: "EndOfHeader" },
  { name: "Comment" },
  { name: "CipherID" },
  { name: "CompressionFlags" },
  { name: "MasterSeed" },
  { name: "TransformSeed", ver: [3] },
  { name: "TransformRounds", ver: [3] },
  { name: "EncryptionIV" },
  { name: "ProtectedStreamKey", ver: [3] },
  { name: "StreamStartBytes", ver: [3] },
  { name: "InnerRandomStreamID", ver: [3] },
  { name: "KdfParameters", ver: [4] },
  { name: "PublicCustomData", ver: [4] }
], Rr = [
  { name: "EndOfHeader" },
  { name: "InnerRandomStreamID" },
  { name: "InnerRandomStreamKey" },
  { name: "Binary", skipHeader: !0 }
], Oe = {
  DefaultFileVersionMajor: 4,
  MinSupportedVersion: 3,
  MaxSupportedVersion: 4,
  FlagBinaryProtected: 1,
  InnerHeaderBinaryFieldId: 3,
  DefaultKdfAlgo: At.Argon2d,
  DefaultKdfSaltLength: 32,
  DefaultKdfParallelism: 1,
  DefaultKdfIterations: 2,
  DefaultKdfMemory: 1024 * 1024,
  DefaultKdfVersion: 19,
  EndOfHeader: 13675786
}, Zs = {
  3: 1,
  4: 0
}, du = {
  3: 1,
  4: 1
}, yr = class yr {
  constructor() {
    this.versionMajor = 0, this.versionMinor = 0;
  }
  readSignature(e) {
    if (e.byteLength < 8)
      throw new H(j.FileCorrupt, "not enough data");
    const t = e.getUint32(!0), r = e.getUint32(!0);
    if (!(t === lr.FileMagic && r === lr.Sig2Kdbx))
      throw new H(j.BadSignature);
  }
  writeSignature(e) {
    e.setUint32(lr.FileMagic, !0), e.setUint32(lr.Sig2Kdbx, !0);
  }
  readVersion(e) {
    const t = e.getUint16(!0), r = e.getUint16(!0);
    if (r > Oe.MaxSupportedVersion || r < Oe.MinSupportedVersion)
      throw new H(j.InvalidVersion);
    if (t > du[r])
      throw new H(j.InvalidVersion);
    this.versionMinor = t, this.versionMajor = r;
  }
  writeVersion(e) {
    if (!this.versionMajor)
      throw new H(j.InvalidState, "version is not set");
    e.setUint16(this.versionMinor, !0), e.setUint16(this.versionMajor, !0);
  }
  readCipherID(e) {
    if (e.byteLength !== 16)
      throw new H(j.Unsupported, "cipher");
    this.dataCipherUuid = new Ne(e);
  }
  writeCipherID(e) {
    if (!this.dataCipherUuid)
      throw new H(j.InvalidState, "cipher id is not set");
    this.writeFieldSize(e, 16), e.writeBytes(this.dataCipherUuid.bytes);
  }
  readCompressionFlags(e) {
    const t = new DataView(e).getUint32(0, !0);
    if (t < 0 || t >= Object.keys(Nt).length)
      throw new H(j.Unsupported, "compression");
    this.compression = t;
  }
  writeCompressionFlags(e) {
    if (typeof this.compression != "number")
      throw new H(j.InvalidState, "compression is not set");
    this.writeFieldSize(e, 4), e.setUint32(this.compression, !0);
  }
  readMasterSeed(e) {
    this.masterSeed = e;
  }
  writeMasterSeed(e) {
    if (!this.masterSeed)
      throw new H(j.InvalidState, "master seed is not set");
    this.writeFieldBytes(e, this.masterSeed);
  }
  readTransformSeed(e) {
    this.transformSeed = e;
  }
  writeTransformSeed(e) {
    if (!this.transformSeed)
      throw new H(j.InvalidState, "transform seed is not set");
    this.writeFieldBytes(e, this.transformSeed);
  }
  readTransformRounds(e) {
    this.keyEncryptionRounds = new nt(e).getUint64(!0);
  }
  writeTransformRounds(e) {
    if (!this.keyEncryptionRounds)
      throw new H(j.InvalidState, "key encryption rounds is not set");
    this.writeFieldSize(e, 8), e.setUint64(this.keyEncryptionRounds, !0);
  }
  readEncryptionIV(e) {
    this.encryptionIV = e;
  }
  writeEncryptionIV(e) {
    if (!this.encryptionIV)
      throw new H(j.InvalidState, "encryption IV is not set");
    this.writeFieldBytes(e, this.encryptionIV);
  }
  readProtectedStreamKey(e) {
    this.protectedStreamKey = e;
  }
  writeProtectedStreamKey(e) {
    if (!this.protectedStreamKey)
      throw new H(j.InvalidState, "protected stream key is not set");
    this.writeFieldBytes(e, this.protectedStreamKey);
  }
  readStreamStartBytes(e) {
    this.streamStartBytes = e;
  }
  writeStreamStartBytes(e) {
    if (!this.streamStartBytes)
      throw new H(j.InvalidState, "stream start bytes is not set");
    this.writeFieldBytes(e, this.streamStartBytes);
  }
  readInnerRandomStreamID(e) {
    this.crsAlgorithm = new DataView(e).getUint32(0, !0);
  }
  writeInnerRandomStreamID(e) {
    if (!this.crsAlgorithm)
      throw new H(j.InvalidState, "CRSAlgorithm is not set");
    this.writeFieldSize(e, 4), e.setUint32(this.crsAlgorithm, !0);
  }
  readInnerRandomStreamKey(e) {
    this.protectedStreamKey = e;
  }
  writeInnerRandomStreamKey(e) {
    if (!this.protectedStreamKey)
      throw new H(j.InvalidState, "protected stream key is not set");
    this.writeFieldBytes(e, this.protectedStreamKey);
  }
  readKdfParameters(e) {
    this.kdfParameters = Zt.read(new nt(e));
  }
  writeKdfParameters(e) {
    if (!this.kdfParameters)
      throw new H(j.InvalidState, "KDF parameters are not set");
    const t = new nt();
    this.kdfParameters.write(t), this.writeFieldBytes(e, t.getWrittenBytes());
  }
  readPublicCustomData(e) {
    this.publicCustomData = Zt.read(new nt(e));
  }
  hasPublicCustomData() {
    return !!this.publicCustomData;
  }
  writePublicCustomData(e) {
    if (this.publicCustomData) {
      const t = new nt();
      this.publicCustomData.write(t), this.writeFieldBytes(e, t.getWrittenBytes());
    }
  }
  readBinary(e, t) {
    const n = new DataView(e).getUint8(0) & Oe.FlagBinaryProtected, u = e.slice(1), o = n ? De.fromBinary(u) : u;
    t.kdbx.binaries.addWithNextId(o);
  }
  writeBinary(e, t) {
    if (this.versionMajor < 4)
      return;
    const r = t.kdbx.binaries.getAll();
    for (const s of r)
      if (e.setUint8(Oe.InnerHeaderBinaryFieldId), s.value instanceof De) {
        const n = s.value.getBinary();
        this.writeFieldSize(e, n.byteLength + 1), e.setUint8(Oe.FlagBinaryProtected), e.writeBytes(n), me(n);
      } else
        this.writeFieldSize(e, s.value.byteLength + 1), e.setUint8(0), e.writeBytes(s.value);
  }
  writeEndOfHeader(e) {
    this.writeFieldSize(e, 4), e.setUint32(Oe.EndOfHeader, !1);
  }
  readField(e, t, r) {
    const s = e.getUint8(), n = this.readFieldSize(e), u = n > 0 ? e.readBytes(n) : new ArrayBuffer(0), o = t[s];
    switch (o.name) {
      case "EndOfHeader":
      case "Comment":
        break;
      case "CipherID":
        this.readCipherID(u);
        break;
      case "CompressionFlags":
        this.readCompressionFlags(u);
        break;
      case "MasterSeed":
        this.readMasterSeed(u);
        break;
      case "TransformSeed":
        this.readTransformSeed(u);
        break;
      case "TransformRounds":
        this.readTransformRounds(u);
        break;
      case "EncryptionIV":
        this.readEncryptionIV(u);
        break;
      case "ProtectedStreamKey":
        this.readProtectedStreamKey(u);
        break;
      case "StreamStartBytes":
        this.readStreamStartBytes(u);
        break;
      case "InnerRandomStreamID":
        this.readInnerRandomStreamID(u);
        break;
      case "KdfParameters":
        this.readKdfParameters(u);
        break;
      case "PublicCustomData":
        this.readPublicCustomData(u);
        break;
      case "InnerRandomStreamKey":
        this.readInnerRandomStreamKey(u);
        break;
      case "Binary":
        this.readBinary(u, r);
        break;
      default:
        throw new H(j.InvalidArg, `bad header field: ${o.name}`);
    }
    return s !== 0;
  }
  writeField(e, t, r, s) {
    const n = r[t];
    if (n) {
      if (n.ver && !n.ver.includes(this.versionMajor))
        return;
      switch (n.name) {
        case "PublicCustomData":
          if (!this.hasPublicCustomData())
            return;
          break;
        case "Comment":
          return;
      }
      switch (n.skipHeader || e.setUint8(t), n.name) {
        case "EndOfHeader":
          this.writeEndOfHeader(e);
          break;
        case "CipherID":
          this.writeCipherID(e);
          break;
        case "CompressionFlags":
          this.writeCompressionFlags(e);
          break;
        case "MasterSeed":
          this.writeMasterSeed(e);
          break;
        case "TransformSeed":
          this.writeTransformSeed(e);
          break;
        case "TransformRounds":
          this.writeTransformRounds(e);
          break;
        case "EncryptionIV":
          this.writeEncryptionIV(e);
          break;
        case "ProtectedStreamKey":
          this.writeProtectedStreamKey(e);
          break;
        case "StreamStartBytes":
          this.writeStreamStartBytes(e);
          break;
        case "InnerRandomStreamID":
          this.writeInnerRandomStreamID(e);
          break;
        case "KdfParameters":
          this.writeKdfParameters(e);
          break;
        case "PublicCustomData":
          this.writePublicCustomData(e);
          break;
        case "InnerRandomStreamKey":
          this.writeInnerRandomStreamKey(e);
          break;
        case "Binary":
          if (!s)
            throw new H(j.InvalidArg, "context is not set");
          this.writeBinary(e, s);
          break;
        default:
          throw new H(
            j.InvalidArg,
            `Bad header field: ${n.name}`
          );
      }
    }
  }
  readFieldSize(e) {
    return (this.versionMajor | 0) >= 4 ? e.getUint32(!0) : e.getUint16(!0);
  }
  writeFieldSize(e, t) {
    (this.versionMajor | 0) >= 4 ? e.setUint32(t, !0) : e.setUint16(t, !0);
  }
  writeFieldBytes(e, t) {
    this.writeFieldSize(e, t.byteLength), e.writeBytes(t);
  }
  validate() {
    if (!this.versionMajor || this.versionMinor === void 0)
      throw new H(j.FileCorrupt, "no version in header");
    if (this.dataCipherUuid === void 0)
      throw new H(j.FileCorrupt, "no cipher in header");
    if (this.compression === void 0)
      throw new H(j.FileCorrupt, "no compression in header");
    if (!this.masterSeed)
      throw new H(j.FileCorrupt, "no master seed in header");
    if (this.versionMajor < 4 && !this.transformSeed)
      throw new H(j.FileCorrupt, "no transform seed in header");
    if (this.versionMajor < 4 && !this.keyEncryptionRounds)
      throw new H(j.FileCorrupt, "no key encryption rounds in header");
    if (!this.encryptionIV)
      throw new H(j.FileCorrupt, "no encryption iv in header");
    if (this.versionMajor < 4 && !this.protectedStreamKey)
      throw new H(j.FileCorrupt, "no protected stream key in header");
    if (this.versionMajor < 4 && !this.streamStartBytes)
      throw new H(j.FileCorrupt, "no stream start bytes in header");
    if (this.versionMajor < 4 && !this.crsAlgorithm)
      throw new H(j.FileCorrupt, "no crs algorithm in header");
    if (this.versionMajor >= 4 && !this.kdfParameters)
      throw new H(j.FileCorrupt, "no kdf parameters in header");
  }
  validateInner() {
    if (!this.protectedStreamKey)
      throw new H(j.FileCorrupt, "no protected stream key in header");
    if (!this.crsAlgorithm)
      throw new H(j.FileCorrupt, "no crs algorithm in header");
  }
  createKdfParameters(e) {
    switch (e || (e = Oe.DefaultKdfAlgo), e) {
      case At.Argon2d:
      case At.Argon2id:
        this.kdfParameters = new Zt(), this.kdfParameters.set("$UUID", ht.Bytes, lt(e)), this.kdfParameters.set(
          "S",
          ht.Bytes,
          He(Oe.DefaultKdfSaltLength)
        ), this.kdfParameters.set("P", ht.UInt32, Oe.DefaultKdfParallelism), this.kdfParameters.set(
          "I",
          ht.UInt64,
          new Ze(Oe.DefaultKdfIterations)
        ), this.kdfParameters.set(
          "M",
          ht.UInt64,
          new Ze(Oe.DefaultKdfMemory)
        ), this.kdfParameters.set("V", ht.UInt32, Oe.DefaultKdfVersion);
        break;
      case At.Aes:
        this.kdfParameters = new Zt(), this.kdfParameters.set("$UUID", ht.Bytes, lt(At.Aes)), this.kdfParameters.set(
          "S",
          ht.Bytes,
          He(Oe.DefaultKdfSaltLength)
        ), this.kdfParameters.set(
          "R",
          ht.UInt64,
          new Ze(Vt.KeyEncryptionRounds)
        );
        break;
      default:
        throw new H(j.InvalidArg, "bad KDF algo");
    }
  }
  write(e) {
    this.validate(), this.writeSignature(e), this.writeVersion(e);
    for (let t = 1; t < Ur.length; t++)
      this.writeField(e, t, Ur);
    this.writeField(e, 0, Ur), this.endPos = e.pos;
  }
  writeInnerHeader(e, t) {
    this.validateInner();
    for (let r = 1; r < Rr.length; r++)
      this.writeField(e, r, Rr, t);
    this.writeField(e, 0, Rr, t);
  }
  generateSalts() {
    if (this.masterSeed = He(32), this.versionMajor < 4)
      this.transformSeed = He(32), this.streamStartBytes = He(32), this.protectedStreamKey = He(32), this.encryptionIV = He(16);
    else {
      if (this.protectedStreamKey = He(64), !this.kdfParameters || !this.dataCipherUuid)
        throw new H(j.InvalidState, "no kdf params");
      this.kdfParameters.set("S", ht.Bytes, He(32));
      const e = this.dataCipherUuid.toString() === Ot.ChaCha20 ? 12 : 16;
      this.encryptionIV = He(e);
    }
  }
  setVersion(e) {
    if (e !== 3 && e !== 4)
      throw new H(j.InvalidArg, "bad file version");
    this.versionMajor = e, this.versionMinor = Zs[e], this.versionMajor === 4 ? (this.kdfParameters || this.createKdfParameters(), this.crsAlgorithm = Qt.ChaCha20, this.keyEncryptionRounds = void 0) : (this.kdfParameters = void 0, this.crsAlgorithm = Qt.Salsa20, this.keyEncryptionRounds = Vt.KeyEncryptionRounds);
  }
  setKdf(e) {
    this.createKdfParameters(e);
  }
  static read(e, t) {
    const r = new yr();
    for (r.readSignature(e), r.readVersion(e); r.readField(e, Ur, t); )
      ;
    return r.endPos = e.pos, r.validate(), r;
  }
  readInnerHeader(e, t) {
    for (; this.readField(e, Rr, t); )
      ;
    this.validateInner();
  }
  static create() {
    const e = new yr();
    return e.versionMajor = Oe.DefaultFileVersionMajor, e.versionMinor = Zs[Oe.DefaultFileVersionMajor], e.dataCipherUuid = new Ne(Ot.Aes), e.compression = Nt.GZip, e.crsAlgorithm = Qt.ChaCha20, e.createKdfParameters(), e;
  }
};
yr.MaxFileVersion = Oe.MaxSupportedVersion;
let Kt = yr;
class pu {
  constructor(e) {
    this.kdbx = e.kdbx, this.exportXml = !!e.exportXml;
  }
  setXmlDate(e, t) {
    const r = this.kdbx.versionMajor >= 4 && !this.exportXml;
    Zr(e, t, r);
  }
}
class Dr {
  constructor(e) {
    this.preserveXml = !1, this.kdbx = e, this.ctx = new pu({ kdbx: e });
  }
  load(e) {
    const t = new nt(e);
    return this.kdbx.credentials.ready.then(() => {
      if (this.kdbx.header = Kt.read(t, this.ctx), this.kdbx.header.versionMajor === 3)
        return this.loadV3(t);
      if (this.kdbx.header.versionMajor === 4)
        return this.loadV4(t);
      throw new H(
        j.InvalidVersion,
        `bad version: ${this.kdbx.versionMajor}`
      );
    });
  }
  loadV3(e) {
    return this.decryptXmlV3(e).then((t) => (this.kdbx.xml = tr(t), this.setProtectedValues().then(() => this.kdbx.loadFromXml(this.ctx).then(() => this.checkHeaderHashV3(e).then(() => (this.cleanXml(), this.kdbx))))));
  }
  loadV4(e) {
    return this.getHeaderHash(e).then((t) => {
      const r = e.readBytes(t.byteLength);
      if (!jt(r, t))
        throw new H(j.FileCorrupt, "header hash mismatch");
      return this.computeKeysV4().then((s) => this.getHeaderHmac(e, s.hmacKey).then((n) => {
        const u = e.readBytes(n.byteLength);
        if (!jt(u, n))
          throw new H(j.InvalidKey);
        return ja(e.readBytesToEnd(), s.hmacKey).then(
          (o) => (me(s.hmacKey), this.decryptData(o, s.cipherKey).then((p) => {
            me(s.cipherKey), this.kdbx.header.compression === Nt.GZip && (p = ae(gn(new Uint8Array(p)))), e = new nt(ae(p)), this.kdbx.header.readInnerHeader(e, this.ctx), p = e.readBytesToEnd();
            const y = Wt(p);
            return this.kdbx.xml = tr(y), this.setProtectedValues().then(() => this.kdbx.loadFromXml(this.ctx).then((f) => (this.cleanXml(), f)));
          }))
        );
      }));
    });
  }
  loadXml(e) {
    return this.kdbx.credentials.ready.then(() => (this.kdbx.header = Kt.create(), this.kdbx.xml = tr(e), io(this.kdbx.xml.documentElement), this.kdbx.loadFromXml(this.ctx).then(() => (this.cleanXml(), this.kdbx))));
  }
  save() {
    return this.kdbx.credentials.ready.then(() => {
      const e = new nt();
      if (this.kdbx.header.generateSalts(), this.kdbx.header.write(e), this.kdbx.versionMajor === 3)
        return this.saveV3(e);
      if (this.kdbx.versionMajor === 4)
        return this.saveV4(e);
      throw new H(
        j.InvalidVersion,
        `bad version: ${this.kdbx.versionMajor}`
      );
    });
  }
  saveV3(e) {
    return this.getHeaderHash(e).then((t) => (this.kdbx.meta.headerHash = t, this.kdbx.buildXml(this.ctx), this.getProtectSaltGenerator().then((r) => {
      if (!this.kdbx.xml)
        throw new H(j.InvalidState, "no xml");
      return wn(this.kdbx.xml.documentElement, r), this.encryptXmlV3().then((s) => (this.cleanXml(), e.writeBytes(s), e.getWrittenBytes()));
    })));
  }
  saveV4(e) {
    return this.kdbx.buildXml(this.ctx), this.getHeaderHash(e).then((t) => (e.writeBytes(t), this.computeKeysV4().then((r) => this.getHeaderHmac(e, r.hmacKey).then((s) => (e.writeBytes(s), this.getProtectSaltGenerator().then((n) => {
      if (!this.kdbx.xml)
        throw new H(j.InvalidState, "no xml");
      wn(this.kdbx.xml.documentElement, n);
      const u = Nr(this.kdbx.xml), o = new nt();
      this.kdbx.header.writeInnerHeader(o, this.ctx);
      const p = o.getWrittenBytes(), y = ae(Dt(u));
      let f = new ArrayBuffer(p.byteLength + y.byteLength);
      const v = new Uint8Array(f);
      return v.set(new Uint8Array(p)), v.set(new Uint8Array(y), p.byteLength), me(y), me(p), this.kdbx.header.compression === Nt.GZip && (f = ae(Ys(new Uint8Array(f)))), this.encryptData(ae(f), r.cipherKey).then(
        (m) => (me(r.cipherKey), Ha(m, r.hmacKey).then(
          (w) => (this.cleanXml(), me(r.hmacKey), e.writeBytes(w), e.getWrittenBytes())
        ))
      );
    }))))));
  }
  saveXml(e = !1) {
    return this.kdbx.credentials.ready.then(() => {
      if (this.kdbx.header.generateSalts(), this.ctx.exportXml = !0, this.kdbx.buildXml(this.ctx), !this.kdbx.xml)
        throw new H(j.InvalidState, "no xml");
      to(this.kdbx.xml.documentElement);
      const t = Nr(this.kdbx.xml, e);
      return ro(this.kdbx.xml.documentElement), this.cleanXml(), t;
    });
  }
  decryptXmlV3(e) {
    const t = e.readBytesToEnd();
    return this.getMasterKeyV3().then((r) => this.decryptData(t, r).then((s) => (me(r), s = this.trimStartBytesV3(s), La(s).then((n) => (this.kdbx.header.compression === Nt.GZip && (n = ae(gn(new Uint8Array(n)))), Wt(n))))));
  }
  encryptXmlV3() {
    if (!this.kdbx.xml)
      throw new H(j.InvalidState, "no xml");
    const e = Nr(this.kdbx.xml);
    let t = ae(Dt(e));
    return this.kdbx.header.compression === Nt.GZip && (t = ae(Ys(new Uint8Array(t)))), Na(ae(t)).then((r) => {
      if (!this.kdbx.header.streamStartBytes)
        throw new H(j.InvalidState, "no header start bytes");
      const s = new Uint8Array(this.kdbx.header.streamStartBytes), n = new Uint8Array(r.byteLength + s.length);
      return n.set(s), n.set(new Uint8Array(r), s.length), r = n, this.getMasterKeyV3().then((u) => this.encryptData(ae(r), u).then((o) => (me(u), o)));
    });
  }
  getMasterKeyV3() {
    return this.kdbx.credentials.getHash().then((e) => {
      if (!this.kdbx.header.transformSeed || !this.kdbx.header.keyEncryptionRounds || !this.kdbx.header.masterSeed)
        throw new H(j.FileCorrupt, "no header transform parameters");
      const t = this.kdbx.header.transformSeed, r = this.kdbx.header.keyEncryptionRounds, s = this.kdbx.header.masterSeed;
      return this.kdbx.credentials.getChallengeResponse(s).then((n) => An(
        new Uint8Array(e),
        t,
        r
      ).then((u) => (me(e), Ge(u).then((o) => {
        me(u);
        const p = n ? n.byteLength : 0, y = new Uint8Array(
          s.byteLength + o.byteLength + p
        );
        return y.set(new Uint8Array(s), 0), n && y.set(new Uint8Array(n), s.byteLength), y.set(new Uint8Array(o), s.byteLength + p), me(o), me(s), n && me(n), Ge(y.buffer).then((f) => (me(y.buffer), f));
      }))));
    });
  }
  trimStartBytesV3(e) {
    if (!this.kdbx.header.streamStartBytes)
      throw new H(j.FileCorrupt, "no stream start bytes");
    const t = this.kdbx.header.streamStartBytes;
    if (e.byteLength < t.byteLength)
      throw new H(j.FileCorrupt, "short start bytes");
    if (!jt(e.slice(0, this.kdbx.header.streamStartBytes.byteLength), t))
      throw new H(j.InvalidKey);
    return e.slice(t.byteLength);
  }
  setProtectedValues() {
    return this.getProtectSaltGenerator().then((e) => {
      if (!this.kdbx.xml)
        throw new H(j.InvalidState, "no xml");
      eo(this.kdbx.xml.documentElement, e);
    });
  }
  getProtectSaltGenerator() {
    if (!this.kdbx.header.protectedStreamKey || !this.kdbx.header.crsAlgorithm)
      throw new H(j.InvalidState, "bad header parameters");
    return qr.create(
      this.kdbx.header.protectedStreamKey,
      this.kdbx.header.crsAlgorithm
    );
  }
  getHeaderHash(e) {
    if (!this.kdbx.header.endPos)
      throw new H(j.InvalidState, "no end pos");
    const t = e.readBytesNoAdvance(0, this.kdbx.header.endPos);
    return Ge(t);
  }
  getHeaderHmac(e, t) {
    if (!this.kdbx.header.endPos)
      throw new H(j.InvalidState, "no end pos");
    const r = e.readBytesNoAdvance(0, this.kdbx.header.endPos);
    return Cn(t, new Ze(4294967295, 4294967295)).then(
      (s) => Sn(s, r)
    );
  }
  checkHeaderHashV3(e) {
    if (this.kdbx.meta.headerHash) {
      const t = this.kdbx.meta.headerHash;
      return this.getHeaderHash(e).then((r) => {
        if (!jt(t, r))
          throw new H(j.FileCorrupt, "header hash mismatch");
      });
    } else
      return Promise.resolve();
  }
  computeKeysV4() {
    const e = this.kdbx.header.masterSeed;
    if (!e || e.byteLength !== 32)
      return Promise.reject(new H(j.FileCorrupt, "bad master seed"));
    const t = this.kdbx.header.kdfParameters;
    if (!t)
      throw new H(j.FileCorrupt, "no kdf params");
    const r = t.get("S");
    if (!(r instanceof ArrayBuffer))
      throw new H(j.FileCorrupt, "no salt");
    return this.kdbx.credentials.getHash(r).then((s) => qa(s, t).then((n) => {
      if (me(s), !n || n.byteLength !== 32)
        return Promise.reject(new H(j.Unsupported, "bad derived key"));
      const u = new Uint8Array(65);
      return u.set(new Uint8Array(e), 0), u.set(new Uint8Array(n), e.byteLength), u[64] = 1, me(n), me(e), Promise.all([
        Ge(u.buffer.slice(0, 64)),
        zr(u.buffer)
      ]).then((o) => (me(u), { cipherKey: o[0], hmacKey: o[1] }));
    }));
  }
  decryptData(e, t) {
    const r = this.kdbx.header.dataCipherUuid;
    if (!r)
      throw new H(j.FileCorrupt, "no cipher id");
    switch (r.toString()) {
      case Ot.Aes:
        return this.transformDataV4Aes(e, t, !1);
      case Ot.ChaCha20:
        return this.transformDataV4ChaCha20(e, t);
      default:
        return Promise.reject(new H(j.Unsupported, "unsupported cipher"));
    }
  }
  encryptData(e, t) {
    const r = this.kdbx.header.dataCipherUuid;
    if (!r)
      throw new H(j.FileCorrupt, "no cipher id");
    switch (r.toString()) {
      case Ot.Aes:
        return this.transformDataV4Aes(e, t, !0);
      case Ot.ChaCha20:
        return this.transformDataV4ChaCha20(e, t);
      default:
        return Promise.reject(new H(j.Unsupported, "unsupported cipher"));
    }
  }
  transformDataV4Aes(e, t, r) {
    const s = In(), n = this.kdbx.header.encryptionIV;
    if (!n)
      throw new H(j.FileCorrupt, "no encryption IV");
    return s.importKey(t).then(() => r ? s.encrypt(e, n) : s.decrypt(e, n));
  }
  transformDataV4ChaCha20(e, t) {
    const r = this.kdbx.header.encryptionIV;
    if (!r)
      throw new H(j.FileCorrupt, "no encryption IV");
    return Ra(e, t, r);
  }
  cleanXml() {
    this.preserveXml || (this.kdbx.xml = void 0);
  }
}
class Or {
  constructor() {
    this.header = new Kt(), this.credentials = new Yt(null), this.meta = new rr(), this.binaries = new gt(), this.groups = [], this.deletedObjects = [];
  }
  get versionMajor() {
    return this.header.versionMajor;
  }
  get versionMinor() {
    return this.header.versionMinor;
  }
  /**
   * Creates a new database
   */
  static create(e, t) {
    if (!(e instanceof Yt))
      throw new H(j.InvalidArg, "credentials");
    const r = new Or();
    return r.credentials = e, r.header = Kt.create(), r.meta = rr.create(), r.meta._name = t, r.createDefaultGroup(), r.createRecycleBin(), r.meta._lastSelectedGroup = r.getDefaultGroup().uuid, r.meta._lastTopVisibleGroup = r.getDefaultGroup().uuid, r;
  }
  /**
   * Load a kdbx file
   * If there was an error loading file, throws an exception
   */
  static load(e, t, r) {
    if (!(e instanceof ArrayBuffer))
      return Promise.reject(new H(j.InvalidArg, "data"));
    if (!(t instanceof Yt))
      return Promise.reject(new H(j.InvalidArg, "credentials"));
    const s = new Or();
    s.credentials = t;
    const n = new Dr(s);
    return n.preserveXml = (r == null ? void 0 : r.preserveXml) || !1, n.load(e);
  }
  /**
   * Import database from an xml file
   * If there was an error loading file, throws an exception
   */
  static loadXml(e, t) {
    if (typeof e != "string")
      return Promise.reject(new H(j.InvalidArg, "data"));
    if (!(t instanceof Yt))
      return Promise.reject(new H(j.InvalidArg, "credentials"));
    const r = new Or();
    return r.credentials = t, new Dr(r).loadXml(e);
  }
  /**
   * Save the db to ArrayBuffer
   */
  save() {
    return new Dr(this).save();
  }
  /**
   * Save the db as XML string
   */
  saveXml(e = !1) {
    return new Dr(this).saveXml(e);
  }
  /**
   * Creates a default group, if it's not yet created
   */
  createDefaultGroup() {
    if (this.groups.length)
      return;
    const e = ze.create(this.meta.name || "");
    e.icon = Gt.FolderOpen, e.expanded = !0, this.groups.push(e);
  }
  /**
   * Creates a recycle bin group, if it's not yet created
   */
  createRecycleBin() {
    if (this.meta.recycleBinEnabled = !0, this.meta.recycleBinUuid && this.getGroup(this.meta.recycleBinUuid))
      return;
    const e = this.getDefaultGroup(), t = ze.create(Vt.RecycleBinName, e);
    t.icon = Gt.TrashBin, t.enableAutoType = !1, t.enableSearching = !1, this.meta.recycleBinUuid = t.uuid, e.groups.push(t);
  }
  /**
   * Adds a new group to an existing group
   */
  createGroup(e, t) {
    const r = ze.create(t, e);
    return e.groups.push(r), r;
  }
  /**
   * Adds a new entry to a group
   */
  createEntry(e) {
    const t = We.create(this.meta, e);
    return e.entries.push(t), t;
  }
  /**
   * Gets the default group
   */
  getDefaultGroup() {
    if (!this.groups[0])
      throw new H(j.InvalidState, "empty default group");
    return this.groups[0];
  }
  /**
   * Get a group by uuid, returns undefined if it's not found
   */
  getGroup(e, t) {
    const r = t ? t.groups : this.groups;
    for (const s of r) {
      if (s.uuid.equals(e))
        return s;
      const n = this.getGroup(e, s);
      if (n)
        return n;
    }
  }
  /**
   * Move an object from one group to another
   * @param object - object to be moved
   * @param toGroup - target parent group
   * @param atIndex - index in target group (by default, insert to the end of the group)
   */
  move(e, t, r) {
    var o, p;
    const s = e instanceof ze ? "groups" : "entries", n = (o = e.parentGroup) == null ? void 0 : o[s], u = n == null ? void 0 : n.indexOf(e);
    if (!(typeof u != "number" || u < 0)) {
      if (n.splice(u, 1), t) {
        const y = t[s];
        typeof r == "number" && r >= 0 ? y.splice(r, 0, e) : y.push(e);
      } else {
        const y = /* @__PURE__ */ new Date();
        if (e instanceof ze)
          for (const f of e.allGroupsAndEntries()) {
            const v = f.uuid;
            this.addDeletedObject(v, y);
          }
        else
          e.uuid && this.addDeletedObject(e.uuid, y);
      }
      e.previousParentGroup = (p = e.parentGroup) == null ? void 0 : p.uuid, e.parentGroup = t ?? void 0, e.times.locationChanged = /* @__PURE__ */ new Date();
    }
  }
  /**
   * Adds a so-called deleted object, this is used to keep track of objects during merging
   * @param uuid - object uuid
   * @param dt - deletion date
   */
  addDeletedObject(e, t) {
    const r = new Gr();
    r.uuid = e, r.deletionTime = t, this.deletedObjects.push(r);
  }
  /**
   * Delete an entry or a group
   * Depending on settings, removes either to trash, or completely
   */
  remove(e) {
    let t;
    this.meta.recycleBinEnabled && this.meta.recycleBinUuid && (this.createRecycleBin(), t = this.getGroup(this.meta.recycleBinUuid)), this.move(e, t);
  }
  /**
   * Creates a binary in the db and returns an object that can be put to entry.binaries
   */
  createBinary(e) {
    return this.binaries.add(e);
  }
  /**
   * Import an entry from another file
   * It's up to caller to decide what should happen to the original entry in the source file
   * Returns the new entry
   * @param entry - entry to be imported
   * @param group - target parent group
   * @param file - the source file containing the group
   */
  importEntry(e, t, r) {
    const s = new We(), n = Ne.random();
    s.copyFrom(e), s.uuid = n;
    for (const p of e.history) {
      const y = new We();
      y.copyFrom(p), y.uuid = n, s.history.push(y);
    }
    const u = /* @__PURE__ */ new Map(), o = /* @__PURE__ */ new Set();
    for (const p of s.history.concat(s)) {
      p.customIcon && o.add(p.customIcon.id);
      for (const y of p.binaries.values())
        gt.isKdbxBinaryWithHash(y) && u.set(y.hash, y);
    }
    for (const p of u.values())
      r.binaries.getValueByHash(p.hash) && !this.binaries.getValueByHash(p.hash) && this.binaries.addWithHash(p);
    for (const p of o) {
      const y = r.meta.customIcons.get(p);
      y && this.meta.customIcons.set(p, y);
    }
    return t.entries.push(s), s.parentGroup = t, s.times.update(), s;
  }
  /**
   * Perform database cleanup
   * @param settings.historyRules - remove extra history, it it doesn't match defined rules, e.g. records number
   * @param settings.customIcons - remove unused custom icons
   * @param settings.binaries - remove unused binaries
   */
  cleanup(e) {
    const t = /* @__PURE__ */ new Date(), r = e != null && e.historyRules && typeof this.meta.historyMaxItems == "number" && this.meta.historyMaxItems >= 0 ? this.meta.historyMaxItems : 1 / 0, s = /* @__PURE__ */ new Set(), n = /* @__PURE__ */ new Set(), u = (o) => {
      o.customIcon && s.add(o.customIcon.id);
      for (const p of o.binaries.values())
        gt.isKdbxBinaryWithHash(p) && n.add(p.hash);
    };
    for (const o of this.getDefaultGroup().allGroupsAndEntries())
      if (o instanceof We) {
        if (o.history.length > r && o.removeHistory(0, o.history.length - r), u(o), o.history)
          for (const p of o.history)
            u(p);
      } else
        o.customIcon && s.add(o.customIcon.id);
    if (e != null && e.customIcons) {
      for (const o of this.meta.customIcons.keys())
        if (!s.has(o)) {
          const p = new Ne(o);
          this.addDeletedObject(p, t), this.meta.customIcons.delete(o);
        }
    }
    if (e != null && e.binaries)
      for (const o of this.binaries.getAllWithHashes())
        n.has(o.hash) || this.binaries.deleteWithHash(o.hash);
  }
  /**
   * Merge the db with another db
   * Some parts of the remote DB are copied by reference, so it should NOT be modified after merge
   * Suggested use case:
   * - open the local db
   * - get a remote db somehow and open in
   * - merge the remote db into the local db: local.merge(remote)
   * - close the remote db
   * @param remote - database to merge in
   */
  merge(e) {
    const t = this.getDefaultGroup(), r = e.getDefaultGroup();
    if (!t || !r)
      throw new H(j.MergeError, "no default group");
    if (!t.uuid.equals(r.uuid))
      throw new H(j.MergeError, "default group is different");
    const s = this.getObjectMap();
    for (const u of e.deletedObjects)
      u.uuid && u.deletionTime && !s.deleted.has(u.uuid.id) && (this.deletedObjects.push(u), s.deleted.set(u.uuid.id, u.deletionTime));
    for (const u of e.binaries.getAllWithHashes())
      this.binaries.getValueByHash(u.hash) || this.binaries.addWithHash(u);
    const n = e.getObjectMap();
    s.remoteEntries = n.entries, s.remoteGroups = n.groups, this.meta.merge(e.meta, s), t.merge(s), this.cleanup({ historyRules: !0, customIcons: !0, binaries: !0 });
  }
  /**
   * Gets editing state tombstones (for successful merge)
   * The replica must save this state with the db, assign in on opening the db,
   * and call removeLocalEditState on successful upstream push.
   * This state is JSON serializable.
   */
  getLocalEditState() {
    const e = {
      entries: {}
    };
    for (const t of this.getDefaultGroup().allEntries())
      t._editState && t.uuid && e.entries && (e.entries[t.uuid.id] = t._editState);
    return this.meta._editState && (e.meta = this.meta._editState), e;
  }
  /**
   * Sets editing state tombstones returned previously by getLocalEditState
   * The replica must call this method on opening the db to the state returned previously on getLocalEditState.
   * @param editingState - result of getLocalEditState invoked before on saving the db
   */
  setLocalEditState(e) {
    var t;
    for (const r of this.getDefaultGroup().allEntries())
      (t = e.entries) != null && t[r.uuid.id] && (r._editState = e.entries[r.uuid.id]);
    e.meta && (this.meta._editState = e.meta);
  }
  /**
   * Removes editing state tombstones
   * Immediately after successful upstream push the replica must:
   * - call this method
   * - discard any previous state obtained by getLocalEditState call before
   */
  removeLocalEditState() {
    for (const e of this.getDefaultGroup().allEntries())
      e._editState = void 0;
    this.meta._editState = void 0;
  }
  /**
   * Upgrade the file to latest version
   */
  upgrade() {
    this.setVersion(Kt.MaxFileVersion);
  }
  /**
   * Set the file version to a specified number
   */
  setVersion(e) {
    this.meta.headerHash = void 0, this.meta.settingsChanged = /* @__PURE__ */ new Date(), this.header.setVersion(e);
  }
  /**
   * Set file key derivation function
   * @param kdf - KDF id, from KdfId
   */
  setKdf(e) {
    this.meta.headerHash = void 0, this.meta.settingsChanged = /* @__PURE__ */ new Date(), this.header.setKdf(e);
  }
  getObjectMap() {
    const e = {
      entries: /* @__PURE__ */ new Map(),
      groups: /* @__PURE__ */ new Map(),
      remoteEntries: /* @__PURE__ */ new Map(),
      remoteGroups: /* @__PURE__ */ new Map(),
      deleted: /* @__PURE__ */ new Map()
    };
    for (const t of this.getDefaultGroup().allGroupsAndEntries()) {
      if (e.entries.has(t.uuid.id))
        throw new H(j.MergeError, `duplicate: ${t.uuid}`);
      t instanceof We ? e.entries.set(t.uuid.id, t) : e.groups.set(t.uuid.id, t);
    }
    for (const t of this.deletedObjects)
      t.uuid && t.deletionTime && e.deleted.set(t.uuid.id, t.deletionTime);
    return e;
  }
  loadFromXml(e) {
    if (!this.xml)
      throw new H(j.InvalidState, "xml is not set");
    if (this.xml.documentElement.tagName !== U.DocNode)
      throw new H(j.FileCorrupt, "bad xml root");
    return this.parseMeta(e), this.binaries.computeHashes().then(() => (this.parseRoot(e), this));
  }
  parseMeta(e) {
    if (!this.xml)
      throw new H(j.InvalidState, "xml is not set");
    const t = yt(
      this.xml.documentElement,
      U.Meta,
      "no meta node"
    );
    this.meta = rr.read(t, e);
  }
  parseRoot(e) {
    if (!this.xml)
      throw new H(j.InvalidState, "xml is not set");
    this.groups = [], this.deletedObjects = [];
    const t = yt(
      this.xml.documentElement,
      U.Root,
      "no root node"
    );
    for (let r = 0, s = t.childNodes, n = s.length; r < n; r++) {
      const u = s[r];
      switch (u.tagName) {
        case U.Group:
          this.readGroup(u, e);
          break;
        case U.DeletedObjects:
          this.readDeletedObjects(u);
          break;
      }
    }
  }
  readDeletedObjects(e) {
    for (let t = 0, r = e.childNodes, s = r.length; t < s; t++) {
      const n = r[t];
      switch (n.tagName) {
        case U.DeletedObject:
          this.deletedObjects.push(Gr.read(n));
          break;
      }
    }
  }
  readGroup(e, t) {
    this.groups.push(ze.read(e, t));
  }
  buildXml(e) {
    const t = Ya(U.DocNode);
    this.meta.write(t.documentElement, e);
    const r = W(t.documentElement, U.Root);
    for (const n of this.groups)
      n.write(r, e);
    const s = W(r, U.DeletedObjects);
    for (const n of this.deletedObjects)
      n.write(s, e);
    this.xml = t;
  }
  versionIsAtLeast(e, t) {
    return this.versionMajor > e || this.versionMajor === e && this.versionMinor >= t;
  }
}
export {
  nt as BinaryStream,
  wu as ByteUtils,
  Qs as ChaCha20,
  gu as Consts,
  Yt as Credentials,
  _u as CryptoEngine,
  bu as HashedBlockTransform,
  xu as HmacBlockTransform,
  Ze as Int64,
  Or as Kdbx,
  gt as KdbxBinaries,
  pu as KdbxContext,
  Yt as KdbxCredentials,
  nr as KdbxCustomData,
  Gr as KdbxDeletedObject,
  We as KdbxEntry,
  H as KdbxError,
  Dr as KdbxFormat,
  ze as KdbxGroup,
  Kt as KdbxHeader,
  rr as KdbxMeta,
  St as KdbxTimes,
  Ne as KdbxUuid,
  vu as KeyEncryptorAes,
  Eu as KeyEncryptorKdf,
  qr as ProtectSaltGenerator,
  De as ProtectedValue,
  Oh as Salsa20,
  Zt as VarDictionary,
  Bu as XmlNames,
  Su as XmlUtils
};
