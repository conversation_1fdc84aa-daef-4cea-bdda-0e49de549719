/**
 * VaultKeeper 数据验证工具模块
 * 提供全面的数据验证功能，确保数据安全和完整性
 * 
 * 验证功能：
 * - 密码强度验证
 * - 邮箱格式验证
 * - URL 格式验证
 * - 输入安全验证
 * - 自定义验证规则
 */

/**
 * 验证结果接口
 */
export interface ValidationResult {
  isValid: boolean;       // 是否有效
  errors: string[];       // 错误信息列表
  warnings: string[];     // 警告信息列表
}

/**
 * 密码项验证接口
 */
export interface PasswordItemValidation {
  title: ValidationResult;
  username: ValidationResult;
  password: ValidationResult;
  website: ValidationResult;
  notes: ValidationResult;
  category: ValidationResult;
}

/**
 * 验证规则接口
 */
export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  custom?: (value: any) => boolean;
  message?: string;
}

/**
 * 数据验证工具类
 */
export class Validator {
  // 常用正则表达式
  private static readonly PATTERNS = {
    email: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
    url: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/,
    domain: /^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$/,
    ipv4: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
    phone: /^1[3-9]\d{9}$/,
    strongPassword: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/
  };

  /**
   * 验证密码强度
   * @param password 密码
   * @param minLength 最小长度 (默认 8)
   * @returns 验证结果
   */
  static validatePasswordStrength(password: string, minLength: number = 8): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 检查密码长度
    if (!password || password.length === 0) {
      errors.push('密码不能为空');
      return { isValid: false, errors, warnings };
    }

    if (password.length < minLength) {
      errors.push(`密码长度至少需要 ${minLength} 位`);
    }

    // 检查字符类型
    const hasLowercase = /[a-z]/.test(password);
    const hasUppercase = /[A-Z]/.test(password);
    const hasNumbers = /\d/.test(password);
    const hasSymbols = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password);

    if (!hasLowercase) warnings.push('建议包含小写字母');
    if (!hasUppercase) warnings.push('建议包含大写字母');
    if (!hasNumbers) warnings.push('建议包含数字');
    if (!hasSymbols) warnings.push('建议包含特殊符号');

    // 检查常见弱密码
    const commonPasswords = [
      '123456', 'password', '123456789', '12345678', '12345',
      '1234567', '1234567890', 'qwerty', 'abc123', 'password123'
    ];

    if (commonPasswords.includes(password.toLowerCase())) {
      errors.push('不能使用常见的弱密码');
    }

    // 检查重复字符
    const repeatedPattern = /(.)\1{2,}/;
    if (repeatedPattern.test(password)) {
      warnings.push('避免连续重复字符');
    }

    // 检查键盘序列
    const keyboardPatterns = [
      'qwerty', 'asdfgh', 'zxcvbn', '123456', '654321'
    ];
    
    for (const pattern of keyboardPatterns) {
      if (password.toLowerCase().includes(pattern)) {
        warnings.push('避免使用键盘序列');
        break;
      }
    }

    const isValid = errors.length === 0;
    return { isValid, errors, warnings };
  }

  /**
   * 验证邮箱格式
   * @param email 邮箱地址
   * @returns 验证结果
   */
  static validateEmail(email: string): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!email || email.trim().length === 0) {
      errors.push('邮箱地址不能为空');
      return { isValid: false, errors, warnings };
    }

    const trimmedEmail = email.trim();

    // 长度检查
    if (trimmedEmail.length > 254) {
      errors.push('邮箱地址过长');
    }

    // 格式检查
    if (!this.PATTERNS.email.test(trimmedEmail)) {
      errors.push('邮箱格式不正确');
    }

    // 检查本地部分长度
    const [localPart] = trimmedEmail.split('@');
    if (localPart && localPart.length > 64) {
      errors.push('邮箱用户名部分过长');
    }

    const isValid = errors.length === 0;
    return { isValid, errors, warnings };
  }

  /**
   * 验证 URL 格式
   * @param url URL 地址
   * @param requireHttps 是否要求 HTTPS (默认 false)
   * @returns 验证结果
   */
  static validateUrl(url: string, requireHttps: boolean = false): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!url || url.trim().length === 0) {
      errors.push('URL 不能为空');
      return { isValid: false, errors, warnings };
    }

    const trimmedUrl = url.trim();

    // 基本格式检查
    if (!this.PATTERNS.url.test(trimmedUrl)) {
      errors.push('URL 格式不正确');
    } else {
      // HTTPS 检查
      if (requireHttps && !trimmedUrl.startsWith('https://')) {
        errors.push('必须使用 HTTPS 协议');
      } else if (!trimmedUrl.startsWith('https://')) {
        warnings.push('建议使用 HTTPS 协议');
      }

      // 长度检查
      if (trimmedUrl.length > 2048) {
        warnings.push('URL 过长，可能影响兼容性');
      }
    }

    const isValid = errors.length === 0;
    return { isValid, errors, warnings };
  }

  /**
   * 验证用户名
   * @param username 用户名
   * @param minLength 最小长度 (默认 3)
   * @param maxLength 最大长度 (默认 50)
   * @returns 验证结果
   */
  static validateUsername(username: string, minLength: number = 3, maxLength: number = 50): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!username || username.trim().length === 0) {
      errors.push('用户名不能为空');
      return { isValid: false, errors, warnings };
    }

    const trimmedUsername = username.trim();

    // 长度检查
    if (trimmedUsername.length < minLength) {
      errors.push(`用户名长度至少需要 ${minLength} 位`);
    }

    if (trimmedUsername.length > maxLength) {
      errors.push(`用户名长度不能超过 ${maxLength} 位`);
    }

    // 字符检查
    const validPattern = /^[a-zA-Z0-9._@-]+$/;
    if (!validPattern.test(trimmedUsername)) {
      errors.push('用户名只能包含字母、数字、点号、下划线、@符号和连字符');
    }

    // 特殊规则检查
    if (trimmedUsername.startsWith('.') || trimmedUsername.endsWith('.')) {
      errors.push('用户名不能以点号开头或结尾');
    }

    if (trimmedUsername.includes('..')) {
      errors.push('用户名不能包含连续的点号');
    }

    const isValid = errors.length === 0;
    return { isValid, errors, warnings };
  }

  /**
   * 验证密码项数据
   * @param passwordItem 密码项数据
   * @returns 验证结果
   */
  static validatePasswordItem(passwordItem: any): PasswordItemValidation {
    return {
      title: this.validateField(passwordItem.title, {
        required: true,
        minLength: 1,
        maxLength: 100,
        message: '标题'
      }),
      username: this.validateField(passwordItem.username, {
        required: false,
        maxLength: 100,
        message: '用户名'
      }),
      password: this.validatePasswordStrength(passwordItem.password || ''),
      website: passwordItem.website ? 
        this.validateUrl(passwordItem.website) : 
        { isValid: true, errors: [], warnings: [] },
      notes: this.validateField(passwordItem.notes, {
        required: false,
        maxLength: 1000,
        message: '备注'
      }),
      category: this.validateField(passwordItem.category, {
        required: false,
        maxLength: 50,
        message: '分类'
      })
    };
  }

  /**
   * 验证输入安全性 (防止 XSS)
   * @param input 输入内容
   * @returns 验证结果
   */
  static validateInputSecurity(input: string): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!input) {
      return { isValid: true, errors, warnings };
    }

    // 检查潜在的脚本注入
    const dangerousPatterns = [
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      /javascript:/gi,
      /on\w+\s*=/gi,
      /<iframe\b/gi,
      /<object\b/gi,
      /<embed\b/gi
    ];

    for (const pattern of dangerousPatterns) {
      if (pattern.test(input)) {
        errors.push('输入内容包含潜在的安全风险');
        break;
      }
    }

    // 检查 SQL 注入模式
    const sqlPatterns = [
      /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC)\b)/gi,
      /(UNION\s+SELECT)/gi,
      /('|(\\')|(;)|(--)|(\s(OR|AND)\s))/gi
    ];

    for (const pattern of sqlPatterns) {
      if (pattern.test(input)) {
        warnings.push('输入内容可能包含 SQL 关键字');
        break;
      }
    }

    const isValid = errors.length === 0;
    return { isValid, errors, warnings };
  }

  /**
   * 自定义字段验证
   * @param value 字段值
   * @param rules 验证规则
   * @returns 验证结果
   */
  static validateField(value: any, rules: ValidationRule): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    const fieldName = rules.message || '字段';

    // 必填检查
    if (rules.required && (!value || (typeof value === 'string' && value.trim().length === 0))) {
      errors.push(`${fieldName}不能为空`);
      return { isValid: false, errors, warnings };
    }

    // 如果值为空且非必填，直接返回有效
    if (!value || (typeof value === 'string' && value.trim().length === 0)) {
      return { isValid: true, errors, warnings };
    }

    const stringValue = String(value).trim();

    // 长度检查
    if (rules.minLength && stringValue.length < rules.minLength) {
      errors.push(`${fieldName}长度至少需要 ${rules.minLength} 位`);
    }

    if (rules.maxLength && stringValue.length > rules.maxLength) {
      errors.push(`${fieldName}长度不能超过 ${rules.maxLength} 位`);
    }

    // 模式检查
    if (rules.pattern && !rules.pattern.test(stringValue)) {
      errors.push(`${fieldName}格式不正确`);
    }

    // 自定义验证
    if (rules.custom && !rules.custom(value)) {
      errors.push(`${fieldName}验证失败`);
    }

    const isValid = errors.length === 0;
    return { isValid, errors, warnings };
  }

  /**
   * 批量验证
   * @param data 要验证的数据对象
   * @param rules 验证规则对象
   * @returns 验证结果对象
   */
  static validateBatch(
    data: Record<string, any>, 
    rules: Record<string, ValidationRule>
  ): Record<string, ValidationResult> {
    const results: Record<string, ValidationResult> = {};

    for (const [field, rule] of Object.entries(rules)) {
      results[field] = this.validateField(data[field], rule);
    }

    return results;
  }

  /**
   * 检查验证结果是否全部有效
   * @param results 验证结果对象
   * @returns 是否全部有效
   */
  static isAllValid(results: Record<string, ValidationResult>): boolean {
    return Object.values(results).every(result => result.isValid);
  }

  /**
   * 获取所有错误信息
   * @param results 验证结果对象
   * @returns 错误信息数组
   */
  static getAllErrors(results: Record<string, ValidationResult>): string[] {
    const allErrors: string[] = [];
    
    for (const result of Object.values(results)) {
      allErrors.push(...result.errors);
    }
    
    return allErrors;
  }

  /**
   * 获取所有警告信息
   * @param results 验证结果对象
   * @returns 警告信息数组
   */
  static getAllWarnings(results: Record<string, ValidationResult>): string[] {
    const allWarnings: string[] = [];
    
    for (const result of Object.values(results)) {
      allWarnings.push(...result.warnings);
    }
    
    return allWarnings;
  }
}
