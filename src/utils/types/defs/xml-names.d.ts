export declare const Elem: {
    readonly DocNode: "KeePassFile";
    readonly Meta: "Meta";
    readonly <PERSON>: "Root";
    readonly Group: "Group";
    readonly Entry: "Entry";
    readonly Generator: "Generator";
    readonly HeaderHash: "HeaderHash";
    readonly SettingsChanged: "SettingsChanged";
    readonly DbName: "DatabaseName";
    readonly DbNameChanged: "DatabaseNameChanged";
    readonly DbDesc: "DatabaseDescription";
    readonly DbDescChanged: "DatabaseDescriptionChanged";
    readonly DbDefaultUser: "DefaultUserName";
    readonly DbDefaultUserChanged: "DefaultUserNameChanged";
    readonly DbMntncHistoryDays: "MaintenanceHistoryDays";
    readonly DbColor: "Color";
    readonly DbKeyChanged: "MasterKeyChanged";
    readonly DbKeyChangeRec: "MasterKeyChangeRec";
    readonly DbKeyChangeForce: "MasterKeyChangeForce";
    readonly RecycleBinEnabled: "RecycleBinEnabled";
    readonly RecycleBinUuid: "RecycleBinUUID";
    readonly RecycleBinChanged: "RecycleBinChanged";
    readonly EntryTemplatesGroup: "EntryTemplatesGroup";
    readonly EntryTemplatesGroupChanged: "EntryTemplatesGroupChanged";
    readonly HistoryMaxItems: "HistoryMaxItems";
    readonly HistoryMaxSize: "HistoryMaxSize";
    readonly LastSelectedGroup: "LastSelectedGroup";
    readonly LastTopVisibleGroup: "LastTopVisibleGroup";
    readonly MemoryProt: "MemoryProtection";
    readonly ProtTitle: "ProtectTitle";
    readonly ProtUserName: "ProtectUserName";
    readonly ProtPassword: "ProtectPassword";
    readonly ProtUrl: "ProtectURL";
    readonly ProtNotes: "ProtectNotes";
    readonly CustomIcons: "CustomIcons";
    readonly CustomIconItem: "Icon";
    readonly CustomIconItemID: "UUID";
    readonly CustomIconItemData: "Data";
    readonly CustomIconItemName: "Name";
    readonly AutoType: "AutoType";
    readonly History: "History";
    readonly Name: "Name";
    readonly Notes: "Notes";
    readonly Uuid: "UUID";
    readonly Icon: "IconID";
    readonly CustomIconID: "CustomIconUUID";
    readonly FgColor: "ForegroundColor";
    readonly BgColor: "BackgroundColor";
    readonly OverrideUrl: "OverrideURL";
    readonly Times: "Times";
    readonly Tags: "Tags";
    readonly QualityCheck: "QualityCheck";
    readonly PreviousParentGroup: "PreviousParentGroup";
    readonly CreationTime: "CreationTime";
    readonly LastModTime: "LastModificationTime";
    readonly LastAccessTime: "LastAccessTime";
    readonly ExpiryTime: "ExpiryTime";
    readonly Expires: "Expires";
    readonly UsageCount: "UsageCount";
    readonly LocationChanged: "LocationChanged";
    readonly GroupDefaultAutoTypeSeq: "DefaultAutoTypeSequence";
    readonly EnableAutoType: "EnableAutoType";
    readonly EnableSearching: "EnableSearching";
    readonly String: "String";
    readonly Binary: "Binary";
    readonly Key: "Key";
    readonly Value: "Value";
    readonly AutoTypeEnabled: "Enabled";
    readonly AutoTypeObfuscation: "DataTransferObfuscation";
    readonly AutoTypeDefaultSeq: "DefaultSequence";
    readonly AutoTypeItem: "Association";
    readonly Window: "Window";
    readonly KeystrokeSequence: "KeystrokeSequence";
    readonly Binaries: "Binaries";
    readonly IsExpanded: "IsExpanded";
    readonly LastTopVisibleEntry: "LastTopVisibleEntry";
    readonly DeletedObjects: "DeletedObjects";
    readonly DeletedObject: "DeletedObject";
    readonly DeletionTime: "DeletionTime";
    readonly CustomData: "CustomData";
    readonly StringDictExItem: "Item";
};
export declare const Attr: {
    readonly Id: "ID";
    readonly Ref: "Ref";
    readonly Protected: "Protected";
    readonly ProtectedInMemPlainXml: "ProtectInMemory";
    readonly Compressed: "Compressed";
};
export declare const Val: {
    readonly False: "False";
    readonly True: "True";
};
