export declare const Signatures: {
    readonly FileMagic: 2594363651;
    readonly Sig2Kdbx: 3041655655;
    readonly Sig2Kdb: 3041655653;
};
export declare const ErrorCodes: {
    readonly NotImplemented: "NotImplemented";
    readonly InvalidArg: "InvalidArg";
    readonly BadSignature: "BadSignature";
    readonly InvalidVersion: "InvalidVersion";
    readonly Unsupported: "Unsupported";
    readonly FileCorrupt: "FileCorrupt";
    readonly InvalidKey: "InvalidKey";
    readonly MergeError: "MergeError";
    readonly InvalidState: "InvalidState";
};
export declare const CompressionAlgorithm: {
    readonly None: 0;
    readonly GZip: 1;
};
export declare const CrsAlgorithm: {
    readonly Null: 0;
    readonly ArcFourVariant: 1;
    readonly Salsa20: 2;
    readonly ChaCha20: 3;
};
export declare const KdfId: {
    readonly Argon2: "72Nt34wpREuR96mkA+MKDA==";
    readonly Argon2d: "72Nt34wpREuR96mkA+MKDA==";
    readonly Argon2id: "nimLGVbbR3OyPfw+xvCh5g==";
    readonly Aes: "ydnzmmKKRGC/dA0IwYpP6g==";
};
export declare const CipherId: {
    readonly Aes: "McHy5r9xQ1C+WAUhavxa/w==";
    readonly ChaCha20: "1gOKK4tvTLWlJDOaMdu1mg==";
};
export declare const AutoTypeObfuscationOptions: {
    readonly None: 0;
    readonly UseClipboard: 1;
};
export declare const Defaults: {
    readonly KeyEncryptionRounds: 300000;
    readonly MntncHistoryDays: 365;
    readonly HistoryMaxItems: 10;
    readonly HistoryMaxSize: number;
    readonly RecycleBinName: "Recycle Bin";
};
export declare const Icons: {
    readonly Key: 0;
    readonly World: 1;
    readonly Warning: 2;
    readonly NetworkServer: 3;
    readonly MarkedDirectory: 4;
    readonly UserCommunication: 5;
    readonly Parts: 6;
    readonly Notepad: 7;
    readonly WorldSocket: 8;
    readonly Identity: 9;
    readonly PaperReady: 10;
    readonly Digicam: 11;
    readonly IRCommunication: 12;
    readonly MultiKeys: 13;
    readonly Energy: 14;
    readonly Scanner: 15;
    readonly WorldStar: 16;
    readonly CDRom: 17;
    readonly Monitor: 18;
    readonly EMail: 19;
    readonly Configuration: 20;
    readonly ClipboardReady: 21;
    readonly PaperNew: 22;
    readonly Screen: 23;
    readonly EnergyCareful: 24;
    readonly EMailBox: 25;
    readonly Disk: 26;
    readonly Drive: 27;
    readonly PaperQ: 28;
    readonly TerminalEncrypted: 29;
    readonly Console: 30;
    readonly Printer: 31;
    readonly ProgramIcons: 32;
    readonly Run: 33;
    readonly Settings: 34;
    readonly WorldComputer: 35;
    readonly Archive: 36;
    readonly Homebanking: 37;
    readonly DriveWindows: 38;
    readonly Clock: 39;
    readonly EMailSearch: 40;
    readonly PaperFlag: 41;
    readonly Memory: 42;
    readonly TrashBin: 43;
    readonly Note: 44;
    readonly Expired: 45;
    readonly Info: 46;
    readonly Package: 47;
    readonly Folder: 48;
    readonly FolderOpen: 49;
    readonly FolderPackage: 50;
    readonly LockOpen: 51;
    readonly PaperLocked: 52;
    readonly Checked: 53;
    readonly Pen: 54;
    readonly Thumbnail: 55;
    readonly Book: 56;
    readonly List: 57;
    readonly UserKey: 58;
    readonly Tool: 59;
    readonly Home: 60;
    readonly Star: 61;
    readonly Tux: 62;
    readonly Feather: 63;
    readonly Apple: 64;
    readonly Wiki: 65;
    readonly Money: 66;
    readonly Certificate: 67;
    readonly BlackBerry: 68;
};
