import { KdbxUuid } from './kdbx-uuid';
import { VarDictionary } from '../utils/var-dictionary';
import { BinaryStream } from '../utils/binary-stream';
import { KdbxContext } from './kdbx-context';
export declare class KdbxHeader {
    static readonly MaxFileVersion: 4;
    versionMajor: number;
    versionMinor: number;
    dataCipherUuid: KdbxUuid | undefined;
    compression: number | undefined;
    masterSeed: ArrayBuffer | undefined;
    transformSeed: ArrayBuffer | undefined;
    keyEncryptionRounds: number | undefined;
    encryptionIV: ArrayBuffer | undefined;
    protectedStreamKey: ArrayBuffer | undefined;
    streamStartBytes: ArrayBuffer | undefined;
    crsAlgorithm: number | undefined;
    endPos: number | undefined;
    kdfParameters: VarDictionary | undefined;
    publicCustomData: VarDictionary | undefined;
    private readSignature;
    private writeSignature;
    private readVersion;
    private writeVersion;
    private readCipherID;
    private writeCipherID;
    private readCompressionFlags;
    private writeCompressionFlags;
    private readMasterSeed;
    private writeMasterSeed;
    private readTransformSeed;
    private writeTransformSeed;
    private readTransformRounds;
    private writeTransformRounds;
    private readEncryptionIV;
    private writeEncryptionIV;
    private readProtectedStreamKey;
    private writeProtectedStreamKey;
    private readStreamStartBytes;
    private writeStreamStartBytes;
    private readInnerRandomStreamID;
    private writeInnerRandomStreamID;
    private readInnerRandomStreamKey;
    private writeInnerRandomStreamKey;
    private readKdfParameters;
    private writeKdfParameters;
    private readPublicCustomData;
    private hasPublicCustomData;
    private writePublicCustomData;
    private readBinary;
    private writeBinary;
    private writeEndOfHeader;
    private readField;
    private writeField;
    private readFieldSize;
    private writeFieldSize;
    private writeFieldBytes;
    private validate;
    private validateInner;
    private createKdfParameters;
    write(stm: BinaryStream): void;
    writeInnerHeader(stm: BinaryStream, ctx: KdbxContext): void;
    generateSalts(): void;
    setVersion(version: number): void;
    setKdf(kdf: string): void;
    static read(stm: BinaryStream, ctx: KdbxContext): KdbxHeader;
    readInnerHeader(stm: BinaryStream, ctx: KdbxContext): void;
    static create(): KdbxHeader;
}
