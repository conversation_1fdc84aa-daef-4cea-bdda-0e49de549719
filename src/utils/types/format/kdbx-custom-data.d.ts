import { KdbxContext } from './kdbx-context';
export type KdbxCustomDataItem = {
    value: string | undefined;
    lastModified?: Date | undefined;
};
export type KdbxCustomDataMap = Map<string, KdbxCustomDataItem>;
export declare class KdbxCustomData {
    static read(node: Node): KdbxCustomDataMap;
    static write(parentNode: Node, ctx: KdbxContext, customData: KdbxCustomDataMap | undefined): void;
    private static readItem;
}
