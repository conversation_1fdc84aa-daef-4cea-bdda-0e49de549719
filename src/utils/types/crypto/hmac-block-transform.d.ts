import { Int64 } from '../utils/int64';
export declare function getH<PERSON>c<PERSON><PERSON>(key: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, blockIndex: Int64): Promise<ArrayBuffer>;
export declare function decrypt(data: <PERSON><PERSON><PERSON><PERSON>uffer, key: ArrayBuffer): Promise<ArrayBuffer>;
export declare function encrypt(data: <PERSON><PERSON><PERSON><PERSON>uff<PERSON>, key: <PERSON><PERSON><PERSON><PERSON>uffer): Promise<ArrayBuffer>;
