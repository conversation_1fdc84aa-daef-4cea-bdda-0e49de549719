/**
 * keeword 安全存储工具模块
 * 提供加密的本地存储功能，确保敏感数据安全
 * 
 * 安全特性：
 * - 自动加密存储
 * - 数据完整性验证
 * - 过期时间管理
 * - 存储空间管理
 */

import { CryptoUtils } from './crypto';

/**
 * 存储项接口
 */
interface StorageItem<T = any> {
  data: T;                    // 实际数据
  timestamp: number;          // 存储时间戳
  expireTime?: number;        // 过期时间戳
  encrypted: boolean;         // 是否加密
  checksum?: string;          // 数据校验和
}

/**
 * 存储选项接口
 */
interface StorageOptions {
  encrypt?: boolean;          // 是否加密 (默认 true)
  expireIn?: number;          // 过期时间 (毫秒)
  compress?: boolean;         // 是否压缩 (暂未实现)
}

/**
 * 存储信息接口
 */
interface StorageInfo {
  totalSize: number;
  usedSize: number;
  itemCount: number;
  items: Record<string, number>;
  lastCleanup: number;
}

/**
 * 安全存储工具类
 */
export class SecureStorage {
  private static readonly STORAGE_PREFIX = 'kee_'; // VaultKeeper 前缀
  private static readonly MASTER_KEY_STORAGE = 'kee_master_key';
  private static readonly STORAGE_INFO_KEY = 'kee_storage_info';

  private static masterKey: string | null = null;

  /**
   * 初始化存储系统
   * @param masterPassword 主密码（可选，如果不提供则使用设备标识符）
   */
  static async initialize(masterPassword?: string): Promise<void> {
    try {
      // 生成或获取主密钥
      SecureStorage.masterKey = await SecureStorage.deriveMasterKey(masterPassword);

      // 初始化存储信息
      await SecureStorage.initStorageInfo();

      console.log('🔐 安全存储系统初始化成功');
    } catch (error) {
      console.error('❌ 存储系统初始化失败:', error);
      throw new Error('存储系统初始化失败');
    }
  }

  /**
   * 自动初始化存储系统（无需主密码）
   */
  static async autoInitialize(): Promise<void> {
    try {
      // 使用设备信息生成默认密钥
      const systemInfo = wx.getSystemInfoSync();
      const defaultKey = `kee_${systemInfo.platform}_${systemInfo.model}_${Date.now()}`;

      await SecureStorage.initialize(defaultKey);
      console.log('🔐 存储系统自动初始化完成');
    } catch (error) {
      console.error('❌ 存储系统自动初始化失败:', error);
      throw new Error('存储系统自动初始化失败');
    }
  }

  /**
   * 存储数据
   * @param key 存储键
   * @param value 存储值
   * @param options 存储选项
   */
  static async setItem<T>(
    key: string,
    value: T,
    options: StorageOptions = {}
  ): Promise<void> {
    try {
      const {
        encrypt = true,
        expireIn,
        compress = false
      } = options;

      // 检查主密钥
      if (encrypt && !SecureStorage.masterKey) {
        throw new Error('存储系统未初始化');
      }

      // 创建存储项
      const storageItem: StorageItem<T> = {
        data: value,
        timestamp: Date.now(),
        encrypted: encrypt,
        expireTime: expireIn ? Date.now() + expireIn : undefined
      };

      // 序列化数据
      let serializedData = JSON.stringify(storageItem);

      // 加密数据
      if (encrypt && SecureStorage.masterKey) {
        serializedData = CryptoUtils.encrypt(serializedData, SecureStorage.masterKey);
        // 添加校验和
        storageItem.checksum = CryptoUtils.hash(serializedData);
      }

      // 存储到小程序本地存储
      const storageKey = SecureStorage.getStorageKey(key);
      wx.setStorageSync(storageKey, serializedData);

      // 更新存储信息
      await SecureStorage.updateStorageInfo(key, serializedData.length);

      console.log(`💾 数据存储成功: ${key}`);
    } catch (error) {
      console.error(`❌ 数据存储失败: ${key}`, error);
      throw new Error(`数据存储失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 获取数据
   * @param key 存储键
   * @returns 存储的数据，如果不存在或已过期返回 null
   */
  static async getItem<T>(key: string): Promise<T | null> {
    try {
      const storageKey = SecureStorage.getStorageKey(key);
      const serializedData = wx.getStorageSync(storageKey);

      if (!serializedData) {
        return null;
      }

      let storageItem: StorageItem<T>;

      // 尝试解析数据
      try {
        // 如果数据是加密的，先解密
        if (SecureStorage.masterKey && typeof serializedData === 'string') {
          const decryptedData = CryptoUtils.decrypt(serializedData, SecureStorage.masterKey);
          storageItem = JSON.parse(decryptedData);
        } else {
          storageItem = JSON.parse(serializedData);
        }
      } catch (parseError) {
        console.warn(`⚠️ 数据解析失败: ${key}`, parseError);
        // 删除损坏的数据
        await SecureStorage.removeItem(key);
        return null;
      }

      // 检查数据完整性
      if (storageItem.encrypted && storageItem.checksum && SecureStorage.masterKey) {
        const currentChecksum = CryptoUtils.hash(serializedData);
        if (currentChecksum !== storageItem.checksum) {
          console.warn(`⚠️ 数据完整性验证失败: ${key}`);
          await SecureStorage.removeItem(key);
          return null;
        }
      }

      // 检查过期时间
      if (storageItem.expireTime && Date.now() > storageItem.expireTime) {
        console.log(`⏰ 数据已过期: ${key}`);
        await SecureStorage.removeItem(key);
        return null;
      }

      console.log(`📖 数据读取成功: ${key}`);
      return storageItem.data;
    } catch (error) {
      console.error(`❌ 数据读取失败: ${key}`, error);
      return null;
    }
  }

  /**
   * 删除数据
   * @param key 存储键
   */
  static async removeItem(key: string): Promise<void> {
    try {
      const storageKey = SecureStorage.getStorageKey(key);
      wx.removeStorageSync(storageKey);

      // 更新存储信息
      await SecureStorage.updateStorageInfo(key, 0, true);

      console.log(`🗑️ 数据删除成功: ${key}`);
    } catch (error) {
      console.error(`❌ 数据删除失败: ${key}`, error);
      throw new Error(`数据删除失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 清空所有 VaultKeeper 数据
   */
  static async clear(): Promise<void> {
    try {
      const storageInfo = wx.getStorageInfoSync();
      const vaultKeeperKeys = storageInfo.keys.filter(key =>
        key.startsWith(SecureStorage.STORAGE_PREFIX)
      );

      for (const key of vaultKeeperKeys) {
        wx.removeStorageSync(key);
      }

      // 重置存储信息
      await SecureStorage.initStorageInfo();

      console.log('🧹 存储数据清空成功');
    } catch (error) {
      console.error('❌ 存储数据清空失败:', error);
      throw new Error('存储数据清空失败');
    }
  }

  /**
   * 获取存储信息
   */
  static async getStorageInfo(): Promise<StorageInfo> {
    try {
      const info: StorageInfo = await SecureStorage.getItem('storage_info') || {
        totalSize: 0,
        usedSize: 0,
        itemCount: 0,
        items: {},
        lastCleanup: Date.now()
      };

      return info;
    } catch (error) {
      console.error('❌ 获取存储信息失败:', error);
      return {
        totalSize: 0,
        usedSize: 0,
        itemCount: 0,
        items: {},
        lastCleanup: Date.now()
      };
    }
  }

  /**
   * 检查存储空间
   */
  static async checkStorageSpace(): Promise<{
    available: number;
    used: number;
    total: number;
    percentage: number;
  }> {
    try {
      const storageInfo = wx.getStorageInfoSync();
      const total = 10 * 1024 * 1024; // 小程序存储限制约10MB
      const used = storageInfo.currentSize * 1024; // 转换为字节
      const available = total - used;
      const percentage = (used / total) * 100;

      return {
        available,
        used,
        total,
        percentage
      };
    } catch (error) {
      console.error('❌ 检查存储空间失败:', error);
      return {
        available: 0,
        used: 0,
        total: 0,
        percentage: 0
      };
    }
  }

  /**
   * 清理过期数据
   */
  static async cleanupExpiredData(): Promise<number> {
    try {
      let cleanedCount = 0;
      const storageInfo = wx.getStorageInfoSync();
      const vaultKeeperKeys = storageInfo.keys.filter(key =>
        key.startsWith(SecureStorage.STORAGE_PREFIX) &&
        key !== SecureStorage.MASTER_KEY_STORAGE &&
        key !== SecureStorage.getStorageKey('storage_info')
      );

      for (const storageKey of vaultKeeperKeys) {
        const key = storageKey.replace(SecureStorage.STORAGE_PREFIX, '');
        const data = await SecureStorage.getItem(key);

        if (data === null) {
          // getItem 会自动删除过期数据
          cleanedCount++;
        }
      }

      console.log(`🧹 清理过期数据完成，清理了 ${cleanedCount} 项`);
      return cleanedCount;
    } catch (error) {
      console.error('❌ 清理过期数据失败:', error);
      return 0;
    }
  }

  // ==================== 私有方法 ====================

  /**
   * 生成存储键
   */
  private static getStorageKey(key: string): string {
    return `${SecureStorage.STORAGE_PREFIX}${key}`;
  }

  /**
   * 派生主密钥
   */
  private static async deriveMasterKey(masterPassword?: string): Promise<string> {
    // 如果没有提供主密码，使用默认密码
    if (!masterPassword) {
      masterPassword = '_default_key_' + Date.now()
    }
    // 检查是否已有存储的主密钥
    const storedKey = wx.getStorageSync(SecureStorage.MASTER_KEY_STORAGE);

    if (storedKey) {
      // 验证主密码
      try {
        const testData = 'test';
        const encrypted = CryptoUtils.encrypt(testData, storedKey);
        const decrypted = CryptoUtils.decrypt(encrypted, storedKey);

        if (decrypted === testData) {
          return storedKey;
        }
      } catch (error) {
        console.warn('⚠️ 主密钥验证失败');
      }
    }

    // 生成新的主密钥
    const salt = CryptoUtils.generateSalt(16);
    const masterKey = CryptoUtils.hash(masterPassword + salt);

    // 存储主密钥（这里应该使用更安全的方式）
    wx.setStorageSync(SecureStorage.MASTER_KEY_STORAGE, masterKey);

    return masterKey;
  }

  /**
   * 初始化存储信息（直接存储，避免递归调用）
   */
  private static async initStorageInfo(): Promise<void> {
    const defaultInfo = {
      totalSize: 0,
      usedSize: 0,
      itemCount: 0,
      items: {},
      lastCleanup: Date.now()
    };

    const existingInfo = await SecureStorage.getItem('storage_info');
    if (!existingInfo) {
      // 直接存储，避免递归调用 setItem
      const storageKey = SecureStorage.getStorageKey('storage_info');
      const serializedData = JSON.stringify({
        data: defaultInfo,
        timestamp: Date.now(),
        encrypted: false
      });
      wx.setStorageSync(storageKey, serializedData);
    }
  }

  /**
   * 更新存储信息（直接存储，避免递归调用）
   */
  private static async updateStorageInfo(
    key: string,
    size: number,
    isDelete: boolean = false
  ): Promise<void> {
    try {
      // 跳过 storage_info 自身的更新，避免无限递归
      if (key === 'storage_info') {
        return;
      }

      const info: StorageInfo = await SecureStorage.getItem('storage_info') || {
        totalSize: 0,
        usedSize: 0,
        itemCount: 0,
        items: {},
        lastCleanup: Date.now()
      };

      if (isDelete) {
        if (info.items[key]) {
          info.usedSize -= info.items[key];
          delete info.items[key];
          info.itemCount--;
        }
      } else {
        const oldSize = info.items[key] || 0;
        info.usedSize = info.usedSize - oldSize + size;
        info.items[key] = size;

        if (oldSize === 0) {
          info.itemCount++;
        }
      }

      info.totalSize = Math.max(info.totalSize, info.usedSize);

      // 直接存储，避免递归调用 setItem
      const storageKey = SecureStorage.getStorageKey('storage_info');
      const serializedData = JSON.stringify({
        data: info,
        timestamp: Date.now(),
        encrypted: false
      });
      wx.setStorageSync(storageKey, serializedData);
    } catch (error) {
      console.error('❌ 更新存储信息失败:', error);
    }
  }

  /**
   * 清理兼容性数据 - 删除可能导致解密错误的旧数据
   */
  static clearIncompatibleData(): void {
    try {
      console.log('🔧 开始清理兼容性数据...')
      
      // 获取所有存储的 key
      const info = wx.getStorageInfoSync()
      const keysToRemove: string[] = []
      
      // 检查每个 key，尝试解密，如果失败则标记删除
      info.keys.forEach(key => {
        if (key.startsWith(SecureStorage.STORAGE_PREFIX)) {
          try {
            const data = wx.getStorageSync(key)
            if (typeof data === 'string') {
              JSON.parse(data) // 尝试解析 JSON
            }
          } catch (error) {
            console.log('🗑️ 标记删除兼容性问题数据:', key)
            keysToRemove.push(key)
          }
        }
      })
      
      // 删除有问题的数据
      keysToRemove.forEach(key => {
        try {
          wx.removeStorageSync(key)
          console.log('✅ 已删除兼容性问题数据:', key)
        } catch (error) {
          console.warn('⚠️ 删除数据失败:', key, error)
        }
      })
      
      console.log(`🔧 兼容性数据清理完成，共清理 ${keysToRemove.length} 项数据`)
    } catch (error) {
      console.error('❌ 清理兼容性数据失败:', error)
    }
  }
}
