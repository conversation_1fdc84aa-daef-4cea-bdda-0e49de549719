// miniprogram jwt-decode
const b64 = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="
const b64re = /^(?:[A-Za-z\d+\/]{4})*?(?:[A-Za-z\d+\/]{2}(?:==)?|[A-Za-z\d+\/]{3}=?)?$/;

// UTF-8 编码辅助函数
function utf8Encode(string: string): string {
    const utf8: number[] = [];
    for (let i = 0; i < string.length; i++) {
        let charCode = string.charCodeAt(i);
        if (charCode < 0x80) {
            utf8.push(charCode);
        } else if (charCode < 0x800) {
            utf8.push(0xc0 | (charCode >> 6), 0x80 | (charCode & 0x3f));
        } else if (charCode < 0xd800 || charCode >= 0xe000) {
            utf8.push(0xe0 | (charCode >> 12), 0x80 | ((charCode >> 6) & 0x3f), 0x80 | (charCode & 0x3f));
        } else {
            // 代理对
            i++;
            charCode = 0x10000 + (((charCode & 0x3ff) << 10) | (string.charCodeAt(i) & 0x3ff));
            utf8.push(0xf0 | (charCode >> 18), 0x80 | ((charCode >> 12) & 0x3f), 0x80 | ((charCode >> 6) & 0x3f), 0x80 | (charCode & 0x3f));
        }
    }
    return String.fromCharCode.apply(null, utf8);
}

// UTF-8 解码辅助函数
function utf8Decode(utf8String: string): string {
    const result: string[] = [];
    let i = 0;

    while (i < utf8String.length) {
        const byte1 = utf8String.charCodeAt(i++);

        if (byte1 < 0x80) {
            // 单字节字符
            result.push(String.fromCharCode(byte1));
        } else if ((byte1 & 0xe0) === 0xc0) {
            // 双字节字符
            const byte2 = utf8String.charCodeAt(i++);
            result.push(String.fromCharCode(((byte1 & 0x1f) << 6) | (byte2 & 0x3f)));
        } else if ((byte1 & 0xf0) === 0xe0) {
            // 三字节字符
            const byte2 = utf8String.charCodeAt(i++);
            const byte3 = utf8String.charCodeAt(i++);
            result.push(String.fromCharCode(((byte1 & 0x0f) << 12) | ((byte2 & 0x3f) << 6) | (byte3 & 0x3f)));
        } else if ((byte1 & 0xf8) === 0xf0) {
            // 四字节字符（代理对）
            const byte2 = utf8String.charCodeAt(i++);
            const byte3 = utf8String.charCodeAt(i++);
            const byte4 = utf8String.charCodeAt(i++);
            const codePoint = ((byte1 & 0x07) << 18) | ((byte2 & 0x3f) << 12) | ((byte3 & 0x3f) << 6) | (byte4 & 0x3f);
            const surrogate1 = 0xd800 + ((codePoint - 0x10000) >> 10);
            const surrogate2 = 0xdc00 + ((codePoint - 0x10000) & 0x3ff);
            result.push(String.fromCharCode(surrogate1, surrogate2));
        }
    }

    return result.join('');
}

// btoa - 支持 Unicode 字符
export const weBtoa = function (string: string) {
    // 先将 Unicode 字符串转换为 UTF-8 字节序列
    const utf8String = utf8Encode(string);

    var bitmap, a, b, c,
        result = "",
        i = 0,
        rest = utf8String.length % 3;

    for (; i < utf8String.length;) {
        a = utf8String.charCodeAt(i++) || 0;
        b = utf8String.charCodeAt(i++) || 0;
        c = utf8String.charCodeAt(i++) || 0;

        bitmap = (a << 16) | (b << 8) | c;
        result += b64.charAt(bitmap >> 18 & 63) + b64.charAt(bitmap >> 12 & 63) +
            b64.charAt(bitmap >> 6 & 63) + b64.charAt(bitmap & 63);
    }

    return rest ? result.slice(0, rest - 3) + "===".substring(rest) : result;
};
// atob - 支持 Unicode 字符
export const weAtob = function (string: string) {
    string = String(string).replace(/[\t\n\f\r ]+/g, "");
    if (!b64re.test(string))
        throw new TypeError("Failed to execute 'atob' on 'Window': The string to be decoded is not correctly encoded.");
    string += "==".slice(2 - (string.length & 3));
    var bitmap, result = "",
        r1, r2, i = 0;
    for (; i < string.length;) {
        bitmap = b64.indexOf(string.charAt(i++)) << 18 | b64.indexOf(string.charAt(i++)) << 12 |
            (r1 = b64.indexOf(string.charAt(i++))) << 6 | (r2 = b64.indexOf(string.charAt(i++)));

        result += r1 === 64 ? String.fromCharCode(bitmap >> 16 & 255) :
            r2 === 64 ? String.fromCharCode(bitmap >> 16 & 255, bitmap >> 8 & 255) :
                String.fromCharCode(bitmap >> 16 & 255, bitmap >> 8 & 255, bitmap & 255);
    }

    // 将 UTF-8 字节序列转换回 Unicode 字符串
    try {
        return utf8Decode(result);
    } catch (error) {
        // 如果解码失败，返回原始结果
        return result;
    }
};

// @quote https://github.com/auth0/jwt-decode
function b64DecodeUnicode(str: string) {
    return decodeURIComponent(
        weAtob(str).replace(/(.)/g, function (p: string) {
            var code = p.charCodeAt(0).toString(16).toUpperCase();
            if (code.length < 2) {
                code = "0" + code;
            }
            return "%" + code;
        })
    );
}

function base64_url_decode(str: string) {
    var output = str.replace(/-/g, "+").replace(/_/g, "/");
    switch (output.length % 4) {
        case 0:
            break;
        case 2:
            output += "==";
            break;
        case 3:
            output += "=";
            break;
        default:
            throw "Illegal base64url string!";
    }

    try {
        return b64DecodeUnicode(output);
    } catch (err) {
        return weAtob(output);
    }
}

export default function weappJwtDecode(token: string, options?: any) {
    if (typeof token !== "string") {
        throw ("Invalid token specified");
    }

    options = options || {};
    var pos = options.header === true ? 0 : 1;
    try {
        return JSON.parse(base64_url_decode(token.split(".")[pos]));
    } catch (e) {
        throw ("Invalid token specified: " + (e instanceof Error ? e.message : String(e)));
    }
}