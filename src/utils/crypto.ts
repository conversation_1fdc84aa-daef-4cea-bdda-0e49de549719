/**
 * VaultKeeper 加密工具模块
 * 提供密码加密、解密、生成和强度计算功能
 * 
 * 安全特性：
 * - AES-256 加密算法
 * - 安全的密码生成
 * - 密码强度评估
 * - 哈希函数支持
 */

import { weBtoa, weAtob } from "./btoa";

// 注意：在实际项目中需要安装 crypto-js 库
// 这里提供接口定义，实际实现需要根据小程序环境调整

/**
 * 密码生成选项接口
 */
export interface PasswordOptions {
  length: number;           // 密码长度
  includeUppercase: boolean; // 包含大写字母
  includeLowercase: boolean; // 包含小写字母
  includeNumbers: boolean;   // 包含数字
  includeSymbols: boolean;   // 包含特殊符号
  excludeSimilar: boolean;   // 排除相似字符 (0, O, l, 1, etc.)
  excludeAmbiguous: boolean; // 排除歧义字符
  customCharset?: string;    // 自定义字符集
}

/**
 * 密码强度评估结果
 */
export interface PasswordStrength {
  score: number;        // 强度评分 (0-100)
  level: 'weak' | 'medium' | 'strong' | 'very-strong'; // 强度等级
  feedback: string[];   // 改进建议
  entropy: number;      // 熵值
  crackTime: string;    // 预估破解时间
}

/**
 * 加密工具类
 */
export class CryptoUtils {
  // 字符集定义
  private static readonly CHARSET = {
    uppercase: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ',
    lowercase: 'abcdefghijklmnopqrstuvwxyz',
    numbers: '0123456789',
    symbols: '!@#$%^&*()_+-=[]{}|;:,.<>?',
    similar: '0O1lI|',
    ambiguous: '{}[]()/\\\'"`~,;.<>'
  };

  /**
   * AES 加密
   * @param data 要加密的数据
   * @param key 加密密钥
   * @returns 加密后的字符串
   */
  static encrypt(data: string, key: string): string {
    try {
      // 这里提供模拟实现
      const encrypted = CryptoUtils.simpleEncrypt(data, key);
      console.log('🔐 数据加密成功');
      return encrypted;
    } catch (error) {
      console.error('❌ 加密失败:', error);
      throw new Error('数据加密失败');
    }
  }

  /**
   * AES 解密
   * @param encryptedData 加密的数据
   * @param key 解密密钥
   * @returns 解密后的字符串
   */
  static decrypt(encryptedData: string, key: string): string {
    try {
      // 实际实现需要使用 crypto-js 或小程序支持的加密库
      const decrypted = CryptoUtils.simpleDecrypt(encryptedData, key);
      console.log('🔓 数据解密成功');
      return decrypted;
    } catch (error) {
      console.error('❌ 解密失败:', error);
      throw new Error('数据解密失败');
    }
  }

  /**
   * 生成安全密码
   * @param options 密码生成选项
   * @returns 生成的密码
   */
  static generatePassword(options: PasswordOptions): string;
  /**
   * 生成安全密码（简化版本）
   * @param length 密码长度
   * @param charset 字符集
   * @returns 生成的密码
   */
  static generatePassword(length: number, charset: string): string;
  static generatePassword(optionsOrLength: PasswordOptions | number, charsetParam?: string): string {
    try {
      // 判断参数类型
      if (typeof optionsOrLength === 'number') {
        // 简化版本：直接使用长度和字符集
        const length = optionsOrLength;
        const charset = charsetParam || '';

        if (charset.length === 0) {
          throw new Error('字符集不能为空');
        }

        let password = '';
        for (let i = 0; i < length; i++) {
          const randomIndex = Math.floor(Math.random() * charset.length);
          password += charset[randomIndex];
        }

        console.log('🎲 密码生成成功，长度:', password.length);
        return password;
      }

      // 完整版本：使用选项对象
      const options = optionsOrLength;
      let charset = '';

      // 构建字符集
      if (options.includeUppercase) charset += CryptoUtils.CHARSET.uppercase;
      if (options.includeLowercase) charset += CryptoUtils.CHARSET.lowercase;
      if (options.includeNumbers) charset += CryptoUtils.CHARSET.numbers;
      if (options.includeSymbols) charset += CryptoUtils.CHARSET.symbols;

      // 使用自定义字符集
      if (options.customCharset) {
        charset = options.customCharset;
      }

      // 排除相似字符
      if (options.excludeSimilar) {
        charset = charset.split('').filter(char =>
          !CryptoUtils.CHARSET.similar.includes(char)
        ).join('');
      }

      // 排除歧义字符
      if (options.excludeAmbiguous) {
        charset = charset.split('').filter(char =>
          !CryptoUtils.CHARSET.ambiguous.includes(char)
        ).join('');
      }

      if (charset.length === 0) {
        throw new Error('字符集不能为空');
      }

      // 生成密码
      let password = '';
      for (let i = 0; i < options.length; i++) {
        const randomIndex = Math.floor(Math.random() * charset.length);
        password += charset[randomIndex];
      }

      // 确保密码包含所需的字符类型
      password = CryptoUtils.ensurePasswordComplexity(password, options);

      console.log('🎲 密码生成成功，长度:', password.length);
      return password;
    } catch (error) {
      console.error('❌ 密码生成失败:', error);
      throw new Error('密码生成失败');
    }
  }

  /**
   * 计算密码强度
   * @param password 要评估的密码
   * @returns 密码强度评估结果
   */
  static calculateStrength(password: string): PasswordStrength {
    try {
      let score = 0;
      const feedback: string[] = [];

      // 长度评分
      if (password.length >= 12) {
        score += 25;
      } else if (password.length >= 8) {
        score += 15;
      } else {
        feedback.push('密码长度至少应为8位');
      }

      // 字符类型评分
      const hasUppercase = /[A-Z]/.test(password);
      const hasLowercase = /[a-z]/.test(password);
      const hasNumbers = /[0-9]/.test(password);
      const hasSymbols = /[^A-Za-z0-9]/.test(password);

      const charTypes = [hasUppercase, hasLowercase, hasNumbers, hasSymbols]
        .filter(Boolean).length;

      score += charTypes * 15;

      if (!hasUppercase) feedback.push('建议包含大写字母');
      if (!hasLowercase) feedback.push('建议包含小写字母');
      if (!hasNumbers) feedback.push('建议包含数字');
      if (!hasSymbols) feedback.push('建议包含特殊符号');

      // 重复字符检查
      const repeatedChars = CryptoUtils.checkRepeatedCharacters(password);
      if (repeatedChars > 0) {
        score -= repeatedChars * 5;
        feedback.push('避免重复字符');
      }

      // 常见模式检查
      if (CryptoUtils.hasCommonPatterns(password)) {
        score -= 20;
        feedback.push('避免使用常见模式');
      }

      // 计算熵值
      const entropy = CryptoUtils.calculateEntropy(password);

      // 确定强度等级
      let level: PasswordStrength['level'];
      if (score >= 80) level = 'very-strong';
      else if (score >= 60) level = 'strong';
      else if (score >= 40) level = 'medium';
      else level = 'weak';

      // 估算破解时间
      const crackTime = CryptoUtils.estimateCrackTime(entropy);

      console.log('📊 密码强度评估完成，评分:', score);

      return {
        score: Math.max(0, Math.min(100, score)),
        level,
        feedback,
        entropy,
        crackTime
      };
    } catch (error) {
      console.error('❌ 密码强度计算失败:', error);
      throw new Error('密码强度计算失败');
    }
  }

  /**
   * 生成哈希值
   * @param data 要哈希的数据
   * @param algorithm 哈希算法 (默认 SHA-256)
   * @returns 哈希值
   */
  static hash(data: string, algorithm: string = 'SHA-256'): string {
    try {
      // 实际实现需要使用小程序支持的哈希库
      // 这里提供模拟实现
      const hash = CryptoUtils.simpleHash(data);
      console.log('🔗 哈希计算成功');
      return hash;
    } catch (error) {
      console.error('❌ 哈希计算失败:', error);
      throw new Error('哈希计算失败');
    }
  }

  /**
   * 生成随机盐值
   * @param length 盐值长度
   * @returns 随机盐值
   */
  static generateSalt(length: number = 16): string {
    const charset = CryptoUtils.CHARSET.uppercase + CryptoUtils.CHARSET.lowercase + CryptoUtils.CHARSET.numbers;
    let salt = '';

    for (let i = 0; i < length; i++) {
      const randomIndex = Math.floor(Math.random() * charset.length);
      salt += charset[randomIndex];
    }

    return salt;
  }

  // ==================== 私有辅助方法 ====================

  /**
   * 简单加密实现 (仅用于演示，实际项目需要使用专业加密库)
   */
  private static simpleEncrypt(data: string, key: string): string {
    // 这里只是一个简单的示例，实际应该使用 AES 等强加密算法
    const keyHash = CryptoUtils.simpleHash(key);
    let encrypted = '';

    for (let i = 0; i < data.length; i++) {
      const charCode = data.charCodeAt(i);
      const keyCode = keyHash.charCodeAt(i % keyHash.length);
      encrypted += String.fromCharCode(charCode ^ keyCode);
    }

    return weBtoa(encrypted); // Base64 编码
  }

  /**
   * 简单解密实现
   */
  private static simpleDecrypt(encryptedData: string, key: string): string {
    try {
      const encrypted = weAtob(encryptedData); // 使用支持 Unicode 的 Base64 解码
      const keyHash = CryptoUtils.simpleHash(key);
      let decrypted = '';

      for (let i = 0; i < encrypted.length; i++) {
        const charCode = encrypted.charCodeAt(i);
        const keyCode = keyHash.charCodeAt(i % keyHash.length);
        decrypted += String.fromCharCode(charCode ^ keyCode);
      }

      return decrypted;
    } catch (error) {
      throw new Error('解密数据格式错误');
    }
  }

  /**
   * 简单哈希实现
   */
  private static simpleHash(data: string): string {
    let hash = 0;
    for (let i = 0; i < data.length; i++) {
      const char = data.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash).toString(16);
  }

  /**
   * 确保密码复杂性
   */
  private static ensurePasswordComplexity(password: string, options: PasswordOptions): string {
    // 这里可以添加逻辑确保生成的密码包含所需的字符类型
    return password;
  }

  /**
   * 检查重复字符
   */
  private static checkRepeatedCharacters(password: string): number {
    let repeatedCount = 0;
    for (let i = 0; i < password.length - 1; i++) {
      if (password[i] === password[i + 1]) {
        repeatedCount++;
      }
    }
    return repeatedCount;
  }

  /**
   * 检查常见模式
   */
  private static hasCommonPatterns(password: string): boolean {
    const commonPatterns = [
      /123456/,
      /abcdef/,
      /qwerty/,
      /password/i,
      /admin/i
    ];

    return commonPatterns.some(pattern => pattern.test(password));
  }

  /**
   * 计算密码熵值
   */
  private static calculateEntropy(password: string): number {
    const uniqueChars = new Set(password).size;
    return password.length * Math.log2(uniqueChars);
  }

  /**
   * 估算破解时间
   */
  private static estimateCrackTime(entropy: number): string {
    const attemptsPerSecond = 1000000; // 假设每秒100万次尝试
    const secondsToCrack = Math.pow(2, entropy - 1) / attemptsPerSecond;

    if (secondsToCrack < 60) return '不到1分钟';
    if (secondsToCrack < 3600) return `${Math.round(secondsToCrack / 60)}分钟`;
    if (secondsToCrack < 86400) return `${Math.round(secondsToCrack / 3600)}小时`;
    if (secondsToCrack < 31536000) return `${Math.round(secondsToCrack / 86400)}天`;
    return `${Math.round(secondsToCrack / 31536000)}年`;
  }

  /**
   * 生成UUID
   * 用于在不支持randomUUID的环境中生成唯一标识符
   */
  static generateUUID(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

}
