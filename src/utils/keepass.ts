/**
 * KeePass 数据库导入工具
 * 使用 kdbxweb 库解析 .kdbx 文件
 */
// import * as kdbxweb from 'kdbxweb'
import * as kdbxweb from './kdbx-mp'
import { PasswordItem } from '../store/password'
import { CryptoUtils } from './crypto'

// KeePass 导入结果接口
export interface KeePassImportResult {
  success: boolean
  message: string
  passwords: PasswordItem[]
  totalCount: number
  importedCount: number
  skippedCount: number
  errors: string[]
}

// KeePass 导入选项
export interface KeePassImportOptions {
  skipDuplicates?: boolean // 跳过重复项
  mergeCategories?: boolean // 合并分类
  preserveStructure?: boolean // 保持文件夹结构
}

export class KeePassImporter {
  /**
   * 导入 KeePass 数据库文件
   * @param fileBuffer 文件二进制数据
   * @param password 数据库密码
   * @param keyFile 密钥文件（可选）
   * @param options 导入选项
   */
  static async importDatabase(
    fileBuffer: ArrayBuffer,
    password: string,
    keyFile?: ArrayBuffer,
    options: KeePassImportOptions = {}
  ): Promise<KeePassImportResult> {
    const result: KeePassImportResult = {
      success: false,
      message: '',
      passwords: [],
      totalCount: 0,
      importedCount: 0,
      skippedCount: 0,
      errors: []
    }

    try {
      console.log('🔐 开始导入 KeePass 数据库...')

      // 创建凭据
      const credentials = new kdbxweb.Credentials(
        kdbxweb.ProtectedValue.fromString(password),
        keyFile ? new Uint8Array(keyFile) : undefined,
      )

      // 解析数据库
      const db = await kdbxweb.Kdbx.load(fileBuffer, credentials)

      console.log('✅ KeePass 数据库解析成功')
      console.log('📊 数据库信息:', {
        name: db.meta.name,
        description: db.meta.desc,
        generator: db.meta.generator,
        version: db.header.versionMajor + '.' + db.header.versionMinor
      })

      // 遍历所有条目
      const allEntries = this.getAllEntries(db.getDefaultGroup())
      result.totalCount = allEntries.length

      console.log(`📝 找到 ${allEntries.length} 个密码条目`)

      // 转换条目为密码项
      for (const entry of allEntries) {
        try {
          const passwordItem = this.convertEntryToPasswordItem(entry, options)
          if (passwordItem) {
            result.passwords.push(passwordItem)
            result.importedCount++
          } else {
            result.skippedCount++
          }
        } catch (error: any) {
          console.error('❌ 转换条目失败:', error)
          result.errors.push(`条目转换失败: ${error?.message || '未知错误'}`)
          result.skippedCount++
        }
      }

      result.success = true
      result.message = `成功导入 ${result.importedCount} 个密码，跳过 ${result.skippedCount} 个`

      console.log('🎉 KeePass 导入完成:', result)

    } catch (error: any) {
      console.error('❌ KeePass 导入失败:', error)

      if (error?.code === 'InvalidKey') {
        result.message = '密码或密钥文件错误'
      } else if (error?.code === 'FileCorrupt') {
        result.message = '数据库文件损坏或格式不正确'
      } else if (error?.code === 'Unsupported') {
        result.message = '不支持的数据库版本'
      } else {
        result.message = `导入失败: ${error?.message || '未知错误'}`
      }

      result.errors.push(result.message)
    }

    return result
  }

  /**
   * 递归获取所有条目
   */
  private static getAllEntries(group: any): any[] {
    let entries: any[] = []

    // 添加当前组的条目
    if (group.entries) {
      entries = entries.concat(Array.from(group.entries))
    }

    // 递归处理子组
    if (group.groups) {
      for (const subGroup of group.groups) {
        entries = entries.concat(this.getAllEntries(subGroup))
      }
    }

    return entries
  }

  /**
   * 将 KeePass 条目转换为密码项
   */
  private static convertEntryToPasswordItem(
    entry: any,
    options: KeePassImportOptions
  ): PasswordItem | null {
    try {
      // 跳过回收站中的条目
      if (this.isInRecycleBin(entry)) {
        return null
      }

      // 获取基本字段
      const title = this.getFieldValue(entry, 'Title') || '未命名'
      const username = this.getFieldValue(entry, 'UserName') || ''
      const password = this.getFieldValue(entry, 'Password') || ''
      const url = this.getFieldValue(entry, 'URL') || ''
      const notes = this.getFieldValue(entry, 'Notes') || ''

      // 跳过空密码条目
      if (!password && !username && !url) {
        return null
      }

      // 生成唯一ID
      const id = CryptoUtils.generateUUID()

      // 确定分类
      const category = this.determineCategory(entry, title, url, options)

      // 生成标签
      const tags = this.generateTags(entry, title, url)

      // 计算密码强度
      const strength = password ? CryptoUtils.calculateStrength(password) : null

      // 确定图标和图标类型
      const iconInfo = this.determineIcon(title, url, category)

      // 处理过期时间
      const expiresAt = entry.times?.expiryTime?.toISOString()

      // 处理自定义字段
      const customFields: any[] = []
      if (entry.fields) {
        for (const [fieldName, fieldValue] of entry.fields) {
          // 跳过标准字段
          if (!['Title', 'UserName', 'Password', 'URL', 'Notes'].includes(fieldName)) {
            customFields.push({
              name: fieldName,
              value: fieldValue?.getText() || '',
              protected: fieldValue?.isProtected() || false
            })
          }
        }
      }

      // 处理附件
      const attachments: any[] = []
      if (entry.binaries) {
        for (const [attachmentName, binaryData] of entry.binaries) {
          if (binaryData) {
            attachments.push({
              name: attachmentName,
              data: binaryData.toString('base64'),
              size: binaryData.length,
              mimeType: KeePassImporter.getMimeType(attachmentName)
            })
          }
        }
      }

      // 处理 Auto-Type 序列
      const autoType = entry.autoType?.defaultSequence || undefined

      const passwordItem: PasswordItem = {
        id,
        title,
        username,
        password,
        website: url,
        notes,
        category,
        tags,
        isFavorite: false,
        icon: iconInfo.icon,
        iconType: iconInfo.iconType,
        iconColor: iconInfo.iconColor,
        createdAt: entry.times?.creationTime?.toISOString() || new Date().toISOString(),
        updatedAt: entry.times?.lastModTime?.toISOString() || new Date().toISOString(),
        lastUsedAt: entry.times?.lastAccessTime?.toISOString(),
        expiresAt,
        customFields: customFields.length > 0 ? customFields : undefined,
        attachments: attachments.length > 0 ? attachments : undefined,
        autoType,
        strength: strength || undefined
      }

      return passwordItem

    } catch (error: any) {
      console.error('❌ 转换条目失败:', error)
      return null
    }
  }

  /**
   * 根据文件扩展名获取 MIME 类型
   */
  private static getMimeType(filename: string): string {
    const ext = filename.toLowerCase().split('.').pop()
    const mimeTypes: Record<string, string> = {
      'txt': 'text/plain',
      'pdf': 'application/pdf',
      'doc': 'application/msword',
      'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'xls': 'application/vnd.ms-excel',
      'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'jpg': 'image/jpeg',
      'jpeg': 'image/jpeg',
      'png': 'image/png',
      'gif': 'image/gif',
      'zip': 'application/zip',
      'rar': 'application/x-rar-compressed',
      'json': 'application/json',
      'xml': 'application/xml'
    }
    return mimeTypes[ext || ''] || 'application/octet-stream'
  }

  /**
   * 获取字段值
   */
  private static getFieldValue(entry: any, fieldName: string): string {
    try {
      const field = entry.fields.get(fieldName)
      if (field && field.getText) {
        return field.getText() || ''
      }
      return field ? String(field) : ''
    } catch (error) {
      return ''
    }
  }

  /**
   * 检查条目是否在回收站中
   */
  private static isInRecycleBin(entry: any): boolean {
    try {
      let parent = entry.parentGroup
      while (parent) {
        if (parent.name === 'Recycle Bin' || parent.name === '回收站') {
          return true
        }
        parent = parent.parentGroup
      }
      return false
    } catch (error) {
      return false
    }
  }

  /**
   * 确定密码分类
   */
  private static determineCategory(
    entry: any,
    title: string,
    url: string,
    options: KeePassImportOptions
  ): string {
    // 如果保持结构，使用组名作为分类
    if (options.preserveStructure && entry.parentGroup) {
      const groupName = entry.parentGroup.name
      if (groupName && groupName !== 'Root' && groupName !== '根组') {
        return this.mapGroupNameToCategory(groupName)
      }
    }

    // 根据URL和标题推断分类
    const titleLower = title.toLowerCase()
    const urlLower = url.toLowerCase()

    if (urlLower.includes('bank') || titleLower.includes('银行') || titleLower.includes('bank')) {
      return 'finance'
    }
    if (urlLower.includes('shop') || urlLower.includes('buy') || titleLower.includes('购物') || titleLower.includes('淘宝')) {
      return 'shopping'
    }
    if (urlLower.includes('social') || titleLower.includes('微信') || titleLower.includes('qq')) {
      return 'social'
    }
    if (urlLower.includes('work') || urlLower.includes('office') || titleLower.includes('工作')) {
      return 'work'
    }
    if (urlLower.includes('game') || titleLower.includes('游戏') || titleLower.includes('game')) {
      return 'entertainment'
    }

    return 'other'
  }

  /**
   * 映射组名到分类
   */
  private static mapGroupNameToCategory(groupName: string): string {
    const groupLower = groupName.toLowerCase()

    const categoryMap: Record<string, string> = {
      'finance': 'finance',
      'financial': 'finance',
      'bank': 'finance',
      '金融': 'finance',
      '银行': 'finance',
      'shopping': 'shopping',
      'shop': 'shopping',
      '购物': 'shopping',
      'social': 'social',
      '社交': 'social',
      'work': 'work',
      'office': 'work',
      '工作': 'work',
      'entertainment': 'entertainment',
      'game': 'entertainment',
      '娱乐': 'entertainment',
      '游戏': 'entertainment'
    }

    for (const [key, category] of Object.entries(categoryMap)) {
      if (groupLower.includes(key)) {
        return category
      }
    }

    return 'other'
  }

  /**
   * 生成标签
   */
  private static generateTags(entry: any, title: string, url: string): string[] {
    const tags: string[] = []

    // 从组名生成标签
    if (entry.parentGroup && entry.parentGroup.name) {
      const groupName = entry.parentGroup.name
      if (groupName !== 'Root' && groupName !== '根组') {
        tags.push(groupName)
      }
    }

    // 从URL生成标签
    if (url) {
      try {
        const urlObj = new URL(url)
        const domain = urlObj.hostname.replace('www.', '')
        if (domain) {
          tags.push(domain)
        }
      } catch (error) {
        // URL解析失败，忽略
      }
    }

    // 从标题生成标签
    if (title.includes('测试') || title.includes('test')) {
      tags.push('测试')
    }

    return [...new Set(tags)] // 去重
  }

  /**
   * 确定图标、图标类型和颜色
   */
  private static determineIcon(title: string, url: string, category: string): { icon: string; iconType: 'fas' | 'far' | 'fab'; iconColor: string } {
    const titleLower = title.toLowerCase()
    const urlLower = url.toLowerCase()

    // 特定服务的图标（品牌图标使用 fab）
    if (titleLower.includes('google') || urlLower.includes('google')) {
      return { icon: 'google', iconType: 'fab', iconColor: '#4285f4' }
    }
    if (titleLower.includes('github') || urlLower.includes('github')) {
      return { icon: 'github', iconType: 'fab', iconColor: '#333333' }
    }
    if (titleLower.includes('微信') || titleLower.includes('wechat')) {
      return { icon: 'weixin', iconType: 'fab', iconColor: '#07c160' }
    }
    if (titleLower.includes('qq') || urlLower.includes('qq.com')) {
      return { icon: 'qq', iconType: 'fab', iconColor: '#12b7f5' }
    }
    if (titleLower.includes('微博') || urlLower.includes('weibo')) {
      return { icon: 'weibo', iconType: 'fab', iconColor: '#e6162d' }
    }
    if (titleLower.includes('淘宝') || urlLower.includes('taobao')) {
      return { icon: 'shopping-cart', iconType: 'fas', iconColor: '#ff6900' }
    }
    if (titleLower.includes('支付宝') || urlLower.includes('alipay')) {
      return { icon: 'alipay', iconType: 'fab', iconColor: '#1677ff' }
    }
    if (titleLower.includes('apple') || urlLower.includes('apple.com')) {
      return { icon: 'apple', iconType: 'fab', iconColor: '#000000' }
    }
    if (titleLower.includes('microsoft') || urlLower.includes('microsoft')) {
      return { icon: 'microsoft', iconType: 'fab', iconColor: '#00a1f1' }
    }

    // 根据分类确定图标（通用图标使用 fas）
    const categoryIcons: Record<string, { icon: string; iconType: 'fas' | 'far' | 'fab'; iconColor: string }> = {
      'finance': { icon: 'university', iconType: 'fas', iconColor: '#10b981' },
      'shopping': { icon: 'shopping-cart', iconType: 'fas', iconColor: '#f59e0b' },
      'social': { icon: 'users', iconType: 'fas', iconColor: '#8b5cf6' },
      'work': { icon: 'briefcase', iconType: 'fas', iconColor: '#3b82f6' },
      'entertainment': { icon: 'gamepad', iconType: 'fas', iconColor: '#ef4444' },
      'other': { icon: 'globe', iconType: 'fas', iconColor: '#6b7280' }
    }

    return categoryIcons[category] || { icon: 'globe', iconType: 'fas', iconColor: '#6b7280' }
  }
}
