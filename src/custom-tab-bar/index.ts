/**
 * VaultKeeper 自定义 TabBar 组件
 * 使用 Unicode 字符图标，确保在小程序中正常显示
 */

Component({
  data: {
    selected: 0,
    color: "#888888",
    selectedColor: "#6d4aff",
    list: [
      {
        pagePath: "/pages/home/<USER>",
        iconClass: "fas fa-home",
        text: "密码库",
        iconUnicode: "\uf015" // home icon
      },
      {
        pagePath: "/pages/password-generator/password-generator",
        iconClass: "fas fa-key",
        text: "生成器",
        iconUnicode: "\uf084" // key icon
      },
      {
        pagePath: "/pages/security-analysis/security-analysis",
        iconClass: "fas fa-shield-alt",
        text: "安全",
        iconUnicode: "\uf3ed" // shield-alt icon
      },
      {
        pagePath: "/pages/settings/settings",
        iconClass: "fas fa-cog",
        text: "设置",
        iconUnicode: "\uf013" // cog icon
      }
    ]
  },

  attached() {
    console.log('🎯 自定义 TabBar 组件初始化')

    // 设置全局 tabBar 引用
    getApp().globalData.tabBar = this
  },

  methods: {
    /**
     * 切换 Tab
     */
    switchTab(e: any) {
      const data = e.currentTarget.dataset
      const url = data.path
      const index = data.index

      console.log('🎯 TabBar 切换:', { url, index })

      wx.switchTab({
        url,
        success: () => {
          this.setData({
            selected: index
          })
        },
        fail: (err) => {
          console.error('❌ TabBar 切换失败:', err)
        }
      })
    },

    /**
     * 设置当前选中的 Tab
     */
    setSelected(index: number) {
      this.setData({
        selected: index
      })
    },

    /**
     * 获取当前选中的 Tab
     */
    getSelected() {
      return this.data.selected
    }
  }
})