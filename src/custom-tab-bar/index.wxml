<!-- VaultKeeper 自定义 TabBar - 使用 Unicode 字符确保兼容性 -->
<view style="position: fixed; bottom: 0; left: 0; right: 0; z-index: 1000;">
  <view class="custom-tabbar" style="height: 168rpx; display: flex; align-items: center; justify-content: space-around; padding-bottom: 40rpx;">
    <view wx:for="{{list}}" wx:key="index" style="display: flex; flex-direction: column; align-items: center; justify-content: center; padding: 8rpx 12rpx; min-width: 120rpx; transition: all 0.2s ease; {{selected === index ? 'transform: scale(1.05);' : ''}}" data-path="{{item.pagePath}}" data-index="{{index}}" bindtap="switchTab">
      <text class="{{item.iconClass}}" style="font-size: 32rpx; margin-bottom: 4rpx; color: {{selected === index ? selectedColor : color}}; line-height: 1;"></text>
      <text style="font-size: 20rpx; font-weight: 500; color: {{selected === index ? selectedColor : color}}; line-height: 1;">
        {{item.text}}
      </text>
    </view>
  </view>
</view>