/**
 * VaultKeeper 全局样式
 * 基于 weapp-tailwindcss 和原型设计的玻璃拟态风格
 */

/* 引入 FontAwesome 图标样式 - @use 必须在最前面 */
@use 'styles/icons.scss';

/* 引入 weapp-tailwindcss - 使用 @use 避免弃用警告 */
@use 'weapp-tailwindcss';


@use 'styles/variables.scss';

/* ==================== 全局基础样式 ==================== */
page {
  background: linear-gradient(135deg, #1a1c25 0%, #0f1118 100%);
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial,
    sans-serif;
  color: #e0e0e0;
  font-size: 28rpx;
  line-height: 1.5;
  min-height: 100vh;
}

/* 重置默认样式 */
view,
text,
button,
input,
textarea,
image,
scroll-view {
  box-sizing: border-box;
}

/* ==================== 玻璃拟态效果（无法用 Tailwind 完全替代） ==================== */
.glass-card {
  background: rgba(23, 25, 35, 0.85);
  border: 1rpx solid rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(12px);
  position: relative;
  overflow: hidden;
}

.glass-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0.05) 50%,
      rgba(255, 255, 255, 0.02) 100%);
  pointer-events: none;
  z-index: 1;
}

.glass-card>view,
.glass-card>text,
.glass-card>button,
.glass-card>image {
  position: relative;
  z-index: 2;
}

/* ==================== 特殊动画效果（Tailwind 无法完全替代） ==================== */
.glass-card--clickable {
  transition: all 0.2s ease;
}

.glass-card--clickable:active {
  transform: scale(0.98);
}

/* 密码强度指示器特殊样式 */
.strength-meter-fill {
  height: 100%;
  border-radius: 2rpx;
  transition: width 0.3s ease;
}

.strength-weak {
  background: #ff4757;
  width: 30%;
}

.strength-medium {
  background: #ffa502;
  width: 60%;
}

.strength-strong {
  background: #2ed573;
  width: 100%;
}

/* ==================== 特殊表单样式（Tailwind 无法完全替代） ==================== */
.form-input-dark {
  background: rgba(40, 44, 58, 0.7);
  border: 1rpx solid rgba(255, 255, 255, 0.08);
}

.form-input-dark:focus {
  border-color: #6d4aff;
  outline: none;
}

.form-input-dark::placeholder {
  color: #888888;
}

/* ==================== 自定义 TabBar 样式 ==================== */
.custom-tabbar {
  background: rgba(26, 28, 37, 0.98);
  border-top: 1rpx solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.3);
}

/* ==================== 安全相关特殊样式 ==================== */
.password-hidden {
  font-family: 'Courier New', monospace;
  letter-spacing: 4rpx;
}