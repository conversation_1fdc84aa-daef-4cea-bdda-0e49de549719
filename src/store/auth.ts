/**
 * VaultKeeper 认证状态管理
 * 管理用户登录状态、会话管理和安全设置
 * 
 * 功能：
 * - 用户认证状态
 * - 主密码管理
 * - 生物识别设置
 * - 会话超时控制
 * - 自动锁定机制
 */

import { ref, computed } from '@vue-mini/core'
import { defineStore } from '@vue-mini/pinia'
import { SecureStorage } from '../utils/storage'
import { CryptoUtils } from '../utils/crypto'
import { Validator } from '../utils/validator'

/**
 * 用户信息接口
 */
export interface UserInfo {
  id?: string;
  username?: string;
  email?: string;
  avatar?: string;
  createdAt?: string;
  lastLoginAt?: string;
}

/**
 * 认证状态接口
 */
export interface AuthState {
  isLoggedIn: boolean;
  isInitialized: boolean;
  user: UserInfo | null;
  sessionStartTime: number | null;
  lastActiveTime: number | null;
}

/**
 * 生物识别设置接口
 */
export interface BiometricSettings {
  enabled: boolean;
  supportedMethods: string[];
  lastUsedMethod?: string;
}

/**
 * 认证状态管理 Store
 */
export const useAuthStore = defineStore('auth', () => {
  // ==================== 状态定义 ====================
  
  // 登录状态
  const isLoggedIn = ref<boolean>(false)
  const isInitialized = ref<boolean>(false)
  const isLoading = ref<boolean>(false)
  
  // 用户信息
  const user = ref<UserInfo | null>(null)
  
  // 会话管理
  const sessionStartTime = ref<number | null>(null)
  const lastActiveTime = ref<number | null>(null)
  const sessionTimeout = ref<number>(15) // 默认15分钟
  
  // 生物识别设置
  const biometricSettings = ref<BiometricSettings>({
    enabled: false,
    supportedMethods: [],
    lastUsedMethod: undefined
  })
  
  // 安全设置
  const autoLockEnabled = ref<boolean>(true)
  const autoLockTimeout = ref<number>(5) // 默认5分钟
  const maxLoginAttempts = ref<number>(5)
  const loginAttempts = ref<number>(0)
  const lockoutEndTime = ref<number | null>(null)

  // ==================== 计算属性 ====================
  
  /**
   * 是否处于锁定状态
   */
  const isLockedOut = computed(() => {
    if (!lockoutEndTime.value) return false
    return Date.now() < lockoutEndTime.value
  })
  
  /**
   * 会话是否有效
   */
  const isSessionValid = computed(() => {
    if (!isLoggedIn.value || !sessionStartTime.value) return false
    
    const now = Date.now()
    const sessionDuration = now - sessionStartTime.value
    const maxDuration = sessionTimeout.value * 60 * 1000 // 转换为毫秒
    
    return sessionDuration < maxDuration
  })
  
  /**
   * 距离会话过期的剩余时间（分钟）
   */
  const sessionTimeRemaining = computed(() => {
    if (!isLoggedIn.value || !sessionStartTime.value) return 0
    
    const now = Date.now()
    const sessionDuration = now - sessionStartTime.value
    const maxDuration = sessionTimeout.value * 60 * 1000
    const remaining = maxDuration - sessionDuration
    
    return Math.max(0, Math.floor(remaining / (60 * 1000)))
  })
  
  /**
   * 是否需要自动锁定
   */
  const shouldAutoLock = computed(() => {
    if (!autoLockEnabled.value || !lastActiveTime.value) return false
    
    const now = Date.now()
    const inactiveDuration = now - lastActiveTime.value
    const lockTimeout = autoLockTimeout.value * 60 * 1000 // 转换为毫秒
    
    return inactiveDuration > lockTimeout
  })

  // ==================== 操作方法 ====================
  
  /**
   * 初始化认证状态
   */
  const initialize = async (): Promise<void> => {
    try {
      isLoading.value = true
      console.log('🔐 初始化认证状态...')
      
      // 检查是否有保存的会话
      const savedSession = await SecureStorage.getItem('auth_session')
      if (savedSession) {
        sessionStartTime.value = savedSession.startTime
        lastActiveTime.value = savedSession.lastActiveTime
        user.value = savedSession.user
        
        // 检查会话是否仍然有效
        if (isSessionValid.value) {
          isLoggedIn.value = true
          console.log('✅ 恢复有效会话')
        } else {
          console.log('⏰ 会话已过期')
          await logout()
        }
      }
      
      // 加载设置
      await loadSettings()
      
      // 检查生物识别支持
      await checkBiometricSupport()
      
      isInitialized.value = true
      console.log('✅ 认证状态初始化完成')
    } catch (error) {
      console.error('❌ 认证状态初始化失败:', error)
      throw new Error('认证状态初始化失败')
    } finally {
      isLoading.value = false
    }
  }
  
  /**
   * 用户登录
   * @param masterPassword 主密码
   * @returns 登录是否成功
   */
  const login = async (masterPassword: string): Promise<boolean> => {
    try {
      isLoading.value = true
      
      // 检查是否被锁定
      if (isLockedOut.value) {
        const remainingTime = Math.ceil((lockoutEndTime.value! - Date.now()) / (60 * 1000))
        throw new Error(`账户已锁定，请 ${remainingTime} 分钟后重试`)
      }
      
      // 验证主密码格式
      const passwordValidation = Validator.validatePasswordStrength(masterPassword)
      if (!passwordValidation.isValid) {
        throw new Error('主密码格式不正确')
      }
      
      console.log('🔐 验证主密码...')
      
      // 初始化安全存储
      await SecureStorage.initialize(masterPassword)
      
      // 验证主密码（尝试解密一个测试数据）
      const testKey = 'auth_test'
      const testData = 'test_data'
      
      try {
        await SecureStorage.setItem(testKey, testData)
        const decryptedData = await SecureStorage.getItem(testKey)
        
        if (decryptedData !== testData) {
          throw new Error('密码验证失败')
        }
        
        // 清理测试数据
        await SecureStorage.removeItem(testKey)
      } catch (error) {
        loginAttempts.value++
        
        if (loginAttempts.value >= maxLoginAttempts.value) {
          // 锁定账户30分钟
          lockoutEndTime.value = Date.now() + (30 * 60 * 1000)
          await SecureStorage.setItem('lockout_end_time', lockoutEndTime.value, { encrypt: false })
        }
        
        throw new Error('主密码错误')
      }
      
      // 登录成功
      const now = Date.now()
      isLoggedIn.value = true
      sessionStartTime.value = now
      lastActiveTime.value = now
      loginAttempts.value = 0
      lockoutEndTime.value = null
      
      // 创建用户信息
      user.value = {
        id: 'local_user',
        username: 'VaultKeeper User',
        createdAt: new Date().toISOString(),
        lastLoginAt: new Date().toISOString()
      }
      
      // 保存会话信息
      await saveSession()
      
      // 清理锁定状态
      await SecureStorage.removeItem('lockout_end_time')
      
      console.log('✅ 登录成功')
      return true
    } catch (error) {
      console.error('❌ 登录失败:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }
  
  /**
   * 生物识别登录
   * @returns 登录是否成功
   */
  const biometricLogin = async (): Promise<boolean> => {
    try {
      if (!biometricSettings.value.enabled) {
        throw new Error('生物识别未启用')
      }

      // 检查运行环境
      const accountInfo = wx.getAccountInfoSync()
      const isDevTool = accountInfo.miniProgram.envVersion === 'develop'

      if (isDevTool) {
        console.log('🛠️ 开发者工具环境，模拟生物识别成功')
        // 在开发环境中模拟成功
        const now = Date.now()
        isLoggedIn.value = true
        sessionStartTime.value = now
        lastActiveTime.value = now
        await saveSession()
        return true
      }

      isLoading.value = true
      console.log('👆 开始生物识别验证...')

      // 调用小程序生物识别 API
      const authResult = await new Promise<boolean>((resolve, reject) => {
        wx.startSoterAuthentication({
          requestAuthModes: biometricSettings.value.supportedMethods,
          challenge: 'vaultkeeper_auth_' + Date.now(),
          authContent: '请验证身份以解锁 VaultKeeper',
          success: (res) => {
            console.log('✅ 生物识别验证成功:', res)
            resolve(true)
          },
          fail: (error) => {
            console.error('❌ 生物识别验证失败:', error)
            reject(new Error('生物识别验证失败'))
          }
        })
      })

      if (authResult) {
        // 生物识别成功，恢复会话
        const now = Date.now()
        isLoggedIn.value = true
        sessionStartTime.value = now
        lastActiveTime.value = now

        await saveSession()

        console.log('✅ 生物识别登录成功')
        return true
      }

      return false
    } catch (error) {
      console.error('❌ 生物识别登录失败:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }
  
  /**
   * 用户登出
   */
  const logout = async (): Promise<void> => {
    try {
      console.log('🚪 用户登出...')
      
      // 清理状态
      isLoggedIn.value = false
      sessionStartTime.value = null
      lastActiveTime.value = null
      user.value = null
      
      // 清理会话数据
      await SecureStorage.removeItem('auth_session')
      
      console.log('✅ 登出成功')
    } catch (error) {
      console.error('❌ 登出失败:', error)
    }
  }
  
  /**
   * 更新活跃时间
   */
  const updateActiveTime = async (): Promise<void> => {
    if (isLoggedIn.value) {
      lastActiveTime.value = Date.now()
      await saveSession()
    }
  }
  
  /**
   * 检查并处理会话超时
   */
  const checkSessionTimeout = async (): Promise<void> => {
    if (isLoggedIn.value && !isSessionValid.value) {
      console.log('⏰ 会话超时，自动登出')
      await logout()
      
      // 显示超时提示
      wx.showModal({
        title: '会话超时',
        content: '为了您的安全，请重新登录',
        showCancel: false,
        success: () => {
          wx.reLaunch({ url: '/pages/login/login' })
        }
      })
    }
  }
  
  /**
   * 检查并处理自动锁定
   */
  const checkAutoLock = async (): Promise<void> => {
    if (shouldAutoLock.value) {
      console.log('🔒 触发自动锁定')
      await logout()
      
      wx.showModal({
        title: '自动锁定',
        content: '应用已自动锁定，请重新验证身份',
        showCancel: false,
        success: () => {
          wx.reLaunch({ url: '/pages/login/login' })
        }
      })
    }
  }
  
  /**
   * 启用生物识别
   */
  const enableBiometric = async (): Promise<void> => {
    try {
      if (biometricSettings.value.supportedMethods.length === 0) {
        throw new Error('设备不支持生物识别')
      }
      
      // 先进行一次生物识别验证
      await biometricLogin()
      
      biometricSettings.value.enabled = true
      await saveSettings()
      
      console.log('✅ 生物识别已启用')
    } catch (error) {
      console.error('❌ 启用生物识别失败:', error)
      throw error
    }
  }
  
  /**
   * 禁用生物识别
   */
  const disableBiometric = async (): Promise<void> => {
    biometricSettings.value.enabled = false
    await saveSettings()
    console.log('🚫 生物识别已禁用')
  }

  // ==================== 私有方法 ====================
  
  /**
   * 保存会话信息
   */
  const saveSession = async (): Promise<void> => {
    if (isLoggedIn.value) {
      const sessionData = {
        startTime: sessionStartTime.value,
        lastActiveTime: lastActiveTime.value,
        user: user.value
      }
      
      await SecureStorage.setItem('auth_session', sessionData, {
        expireIn: sessionTimeout.value * 60 * 1000
      })
    }
  }
  
  /**
   * 加载设置
   */
  const loadSettings = async (): Promise<void> => {
    try {
      const settings = await SecureStorage.getItem('auth_settings')
      if (settings) {
        sessionTimeout.value = settings.sessionTimeout || 15
        autoLockEnabled.value = settings.autoLockEnabled !== false
        autoLockTimeout.value = settings.autoLockTimeout || 5
        maxLoginAttempts.value = settings.maxLoginAttempts || 5
        biometricSettings.value = settings.biometricSettings || biometricSettings.value
      }
      
      // 检查锁定状态
      const lockoutTime = await SecureStorage.getItem('lockout_end_time')
      if (lockoutTime && lockoutTime > Date.now()) {
        lockoutEndTime.value = lockoutTime
      }
    } catch (error) {
      console.error('❌ 加载设置失败:', error)
    }
  }
  
  /**
   * 保存设置
   */
  const saveSettings = async (): Promise<void> => {
    try {
      const settings = {
        sessionTimeout: sessionTimeout.value,
        autoLockEnabled: autoLockEnabled.value,
        autoLockTimeout: autoLockTimeout.value,
        maxLoginAttempts: maxLoginAttempts.value,
        biometricSettings: biometricSettings.value
      }
      
      await SecureStorage.setItem('auth_settings', settings, { encrypt: false })
    } catch (error) {
      console.error('❌ 保存设置失败:', error)
    }
  }
  
  /**
   * 检查生物识别支持
   */
  const checkBiometricSupport = async (): Promise<void> => {
    try {
      // 检查运行环境
      const accountInfo = wx.getAccountInfoSync()
      const isDevTool = accountInfo.miniProgram.envVersion === 'develop'

      if (isDevTool) {
        console.log('🛠️ 开发者工具环境，跳过生物识别检查')
        biometricSettings.value.supportedMethods = []
        return
      }

      const result = await new Promise<string[]>((resolve) => {
        wx.checkIsSupportSoterAuthentication({
          success: (res) => {
            resolve(res.supportMode || [])
          },
          fail: () => {
            resolve([])
          }
        })
      })

      biometricSettings.value.supportedMethods = result
      console.log('🔐 生物识别支持:', result)
    } catch (error) {
      console.error('❌ 检查生物识别支持失败:', error)
      biometricSettings.value.supportedMethods = []
    }
  }

  // ==================== 返回 Store 接口 ====================
  
  return {
    // 状态
    isLoggedIn,
    isInitialized,
    isLoading,
    user,
    sessionStartTime,
    lastActiveTime,
    sessionTimeout,
    biometricSettings,
    autoLockEnabled,
    autoLockTimeout,
    maxLoginAttempts,
    loginAttempts,
    lockoutEndTime,
    
    // 计算属性
    isLockedOut,
    isSessionValid,
    sessionTimeRemaining,
    shouldAutoLock,
    
    // 方法
    initialize,
    login,
    biometricLogin,
    logout,
    updateActiveTime,
    checkSessionTimeout,
    checkAutoLock,
    enableBiometric,
    disableBiometric
  }
}, {
  // 持久化配置
  persist: [
    'sessionTimeout',
    'autoLockEnabled', 
    'autoLockTimeout',
    'maxLoginAttempts',
    'biometricSettings'
  ]
})
