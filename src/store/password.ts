/**
 * VaultKeeper 密码数据状态管理
 * 管理所有密码数据的 CRUD 操作和分类管理
 * 
 * 功能：
 * - 密码数据管理
 * - 分类管理
 * - 搜索和过滤
 * - 数据同步
 * - 安全分析
 */

import { ref, computed } from '@vue-mini/core'
import { defineStore } from '@vue-mini/pinia'
import { SecureStorage } from '../utils/storage'
import { CryptoUtils, PasswordStrength } from '../utils/crypto'
import { Validator } from '../utils/validator'

/**
 * 自定义字段接口
 */
export interface CustomField {
  name: string;
  value: string;
  protected?: boolean; // 是否为受保护字段（如密码类型）
}

/**
 * 附件接口
 */
export interface Attachment {
  name: string;
  data: string; // Base64 编码的文件数据
  size: number;
  mimeType?: string;
}

/**
 * 密码项接口
 */
export interface PasswordItem {
  id: string;
  title: string;
  username: string;
  password: string;
  website?: string;
  notes?: string;
  category: string;
  tags: string[];
  isFavorite: boolean;
  createdAt: string;
  updatedAt: string;
  lastUsedAt?: string;
  expiresAt?: string; // 过期时间 - KeePass 重要功能
  strength?: PasswordStrength;
  icon?: string;
  iconType?: 'fas' | 'far' | 'fab';
  iconColor?: string;
  customFields?: CustomField[]; // 自定义字段 - KeePass 支持
  attachments?: Attachment[]; // 附件 - KeePass 支持
  autoType?: string; // Auto-Type 序列 - KeePass 功能
}

/**
 * 密码分类接口
 */
export interface PasswordCategory {
  id: string;
  name: string;
  icon: string;
  color: string;
  count: number;
}

/**
 * 搜索过滤选项
 */
export interface SearchOptions {
  query: string;
  category?: string;
  tags?: string[];
  isFavorite?: boolean;
  sortBy: 'title' | 'createdAt' | 'updatedAt' | 'lastUsedAt';
  sortOrder: 'asc' | 'desc';
}

/**
 * 密码数据状态管理 Store
 */
export const usePasswordStore = defineStore('password', () => {
  // ==================== 状态定义 ====================

  // 密码数据
  const passwords = ref<PasswordItem[]>([])
  const isLoading = ref<boolean>(false)
  const lastSyncTime = ref<number | null>(null)

  // 分类数据
  const categories = ref<PasswordCategory[]>([
    { id: 'common', name: '常用', icon: 'star', color: '#6d4aff', count: 0 },
    { id: 'work', name: '工作', icon: 'briefcase', color: '#3742fa', count: 0 },
    { id: 'social', name: '社交', icon: 'users', color: '#2ed573', count: 0 },
    { id: 'finance', name: '金融', icon: 'credit-card', color: '#ffa502', count: 0 },
    { id: 'shopping', name: '购物', icon: 'shopping-cart', color: '#ff4757', count: 0 },
    { id: 'entertainment', name: '娱乐', icon: 'play', color: '#8c66ff', count: 0 },
    { id: 'other', name: '其他', icon: 'folder', color: '#888888', count: 0 }
  ])

  // 搜索和过滤
  const searchOptions = ref<SearchOptions>({
    query: '',
    category: undefined,
    tags: [],
    isFavorite: undefined,
    sortBy: 'updatedAt',
    sortOrder: 'desc'
  })

  // 统计数据
  const totalCount = ref<number>(0)
  const weakPasswordCount = ref<number>(0)
  const duplicatePasswordCount = ref<number>(0)
  const expiredPasswordCount = ref<number>(0)

  // ==================== 计算属性 ====================

  /**
   * 过滤后的密码列表
   */
  const filteredPasswords = computed(() => {
    let result = [...passwords.value]

    // 搜索过滤
    if (searchOptions.value.query) {
      const query = searchOptions.value.query.toLowerCase()
      result = result.filter(item =>
        item.title.toLowerCase().includes(query) ||
        item.username.toLowerCase().includes(query) ||
        (item.website && item.website.toLowerCase().includes(query)) ||
        (item.notes && item.notes.toLowerCase().includes(query))
      )
    }

    // 分类过滤
    if (searchOptions.value.category) {
      result = result.filter(item => item.category === searchOptions.value.category)
    }

    // 标签过滤
    if (searchOptions.value.tags && searchOptions.value.tags.length > 0) {
      result = result.filter(item =>
        searchOptions.value.tags!.some(tag => item.tags.includes(tag))
      )
    }

    // 收藏过滤
    if (searchOptions.value.isFavorite !== undefined) {
      result = result.filter(item => item.isFavorite === searchOptions.value.isFavorite)
    }

    // 排序
    result.sort((a, b) => {
      const field = searchOptions.value.sortBy
      const order = searchOptions.value.sortOrder === 'asc' ? 1 : -1

      let aValue = a[field]
      let bValue = b[field]

      if (field === 'title') {
        return aValue.localeCompare(bValue) * order
      } else {
        return (new Date(aValue).getTime() - new Date(bValue).getTime()) * order
      }
    })

    return result
  })

  /**
   * 收藏的密码列表
   */
  const favoritePasswords = computed(() => {
    return passwords.value.filter(item => item.isFavorite)
  })

  /**
   * 最近使用的密码列表
   */
  const recentPasswords = computed(() => {
    return passwords.value
      .filter(item => item.lastUsedAt)
      .sort((a, b) => new Date(b.lastUsedAt!).getTime() - new Date(a.lastUsedAt!).getTime())
      .slice(0, 10)
  })

  /**
   * 弱密码列表
   */
  const weakPasswords = computed(() => {
    return passwords.value.filter(item =>
      item.strength && item.strength.score < 60
    )
  })

  /**
   * 重复密码列表
   */
  const duplicatePasswords = computed(() => {
    const passwordMap = new Map<string, PasswordItem[]>()

    passwords.value.forEach(item => {
      if (!passwordMap.has(item.password)) {
        passwordMap.set(item.password, [])
      }
      passwordMap.get(item.password)!.push(item)
    })

    const duplicates: PasswordItem[] = []
    passwordMap.forEach(items => {
      if (items.length > 1) {
        duplicates.push(...items)
      }
    })

    return duplicates
  })

  /**
   * 按分类统计
   */
  const categoryStats = computed(() => {
    const stats = new Map<string, number>()

    passwords.value.forEach(item => {
      stats.set(item.category, (stats.get(item.category) || 0) + 1)
    })

    return categories.value.map(category => ({
      ...category,
      count: stats.get(category.id) || 0
    }))
  })

  // ==================== 操作方法 ====================

  /**
   * 初始化密码数据
   */
  const initialize = async (): Promise<void> => {
    try {
      isLoading.value = true
      console.log('📊 初始化密码数据...')

      // 加载密码数据
      await loadPasswords()

      // 更新统计数据
      updateStatistics()

      console.log('✅ 密码数据初始化完成')
    } catch (error) {
      console.error('❌ 密码数据初始化失败:', error)
      throw new Error('密码数据初始化失败')
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 添加密码
   * @param passwordData 密码数据
   * @returns 新添加的密码项
   */
  const addPassword = async (passwordData: Omit<PasswordItem, 'id' | 'createdAt' | 'updatedAt' | 'strength'>): Promise<PasswordItem> => {
    try {
      // 验证数据
      const validation = Validator.validatePasswordItem(passwordData)
      if (!Validator.isAllValid(validation)) {
        const errors = Validator.getAllErrors(validation)
        console.log(`数据验证: ${errors.join(', ')}`)
      }

      // 计算密码强度
      const strength = CryptoUtils.calculateStrength(passwordData.password)

      // 创建新密码项
      const newPassword: PasswordItem = {
        ...passwordData,
        id: generateId(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        strength
      }

      // 添加到列表
      passwords.value.push(newPassword)

      // 保存数据
      await savePasswords()

      // 更新统计
      updateStatistics()

      console.log('✅ 密码添加成功:', newPassword.title)
      return newPassword
    } catch (error) {
      console.error('❌ 密码添加失败:', error)
      throw error
    }
  }

  /**
   * 更新密码
   * @param id 密码ID
   * @param updates 更新数据
   * @returns 更新后的密码项
   */
  const updatePassword = async (id: string, updates: Partial<PasswordItem>): Promise<PasswordItem> => {
    try {
      const index = passwords.value.findIndex(item => item.id === id)
      if (index === -1) {
        throw new Error('密码项不存在')
      }

      // 合并更新数据
      const updatedPassword = {
        ...passwords.value[index],
        ...updates,
        updatedAt: new Date().toISOString()
      }

      // 如果密码发生变化，重新计算强度
      if (updates.password && updates.password !== passwords.value[index].password) {
        updatedPassword.strength = CryptoUtils.calculateStrength(updates.password)
      }

      // 验证数据
      const validation = Validator.validatePasswordItem(updatedPassword)
      if (!Validator.isAllValid(validation)) {
        const errors = Validator.getAllErrors(validation)
        throw new Error(`数据验证失败: ${errors.join(', ')}`)
      }

      // 更新列表
      passwords.value[index] = updatedPassword

      // 保存数据
      await savePasswords()

      // 更新统计
      updateStatistics()

      console.log('✅ 密码更新成功:', updatedPassword.title)
      return updatedPassword
    } catch (error) {
      console.error('❌ 密码更新失败:', error)
      throw error
    }
  }

  /**
   * 删除密码
   * @param id 密码ID
   */
  const deletePassword = async (id: string): Promise<void> => {
    try {
      const index = passwords.value.findIndex(item => item.id === id)
      if (index === -1) {
        throw new Error('密码项不存在')
      }

      const passwordTitle = passwords.value[index].title

      // 从列表中移除
      passwords.value.splice(index, 1)

      // 保存数据
      await savePasswords()

      // 更新统计
      updateStatistics()

      console.log('✅ 密码删除成功:', passwordTitle)
    } catch (error) {
      console.error('❌ 密码删除失败:', error)
      throw error
    }
  }

  /**
   * 获取密码详情
   * @param id 密码ID
   * @returns 密码项
   */
  const getPassword = (id: string): PasswordItem | null => {
    return passwords.value.find(item => item.id === id) || null
  }

  /**
   * 标记密码为已使用
   * @param id 密码ID
   */
  const markAsUsed = async (id: string): Promise<void> => {
    try {
      await updatePassword(id, {
        lastUsedAt: new Date().toISOString()
      })
    } catch (error) {
      console.error('❌ 标记使用失败:', error)
    }
  }

  /**
   * 切换收藏状态
   * @param id 密码ID
   */
  const toggleFavorite = async (id: string): Promise<void> => {
    try {
      const password = getPassword(id)
      if (!password) {
        throw new Error('密码项不存在')
      }

      await updatePassword(id, {
        isFavorite: !password.isFavorite
      })
    } catch (error) {
      console.error('❌ 切换收藏状态失败:', error)
      throw error
    }
  }

  /**
   * 更新搜索选项
   * @param options 搜索选项
   */
  const updateSearchOptions = (options: Partial<SearchOptions>): void => {
    searchOptions.value = {
      ...searchOptions.value,
      ...options
    }
  }

  /**
   * 清空搜索
   */
  const clearSearch = (): void => {
    searchOptions.value = {
      query: '',
      category: undefined,
      tags: [],
      isFavorite: undefined,
      sortBy: 'updatedAt',
      sortOrder: 'desc'
    }
  }

  /**
   * 批量添加密码（用于导入）
   * @param passwordDataList 密码数据列表
   * @returns 添加结果统计
   */
  const addPasswordsBatch = async (passwordDataList: Omit<PasswordItem, 'id' | 'createdAt' | 'updatedAt' | 'strength'>[]): Promise<{ success: number; failed: number; errors: string[] }> => {
    const result = {
      success: 0,
      failed: 0,
      errors: [] as string[]
    }

    try {
      isLoading.value = true
      console.log(`📥 开始批量添加 ${passwordDataList.length} 个密码...`)

      for (const passwordData of passwordDataList) {
        try {
          // 验证数据
          const validation = Validator.validatePasswordItem(passwordData)
          if (!Validator.isAllValid(validation)) {
            const errors = Validator.getAllErrors(validation)
            console.log(`数据验证警告 (${passwordData.title}): ${errors.join(', ')}`)
          }

          // 计算密码强度
          const strength = CryptoUtils.calculateStrength(passwordData.password)

          // 创建新密码项
          const newPassword: PasswordItem = {
            ...passwordData,
            id: generateId(),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            strength
          }

          // 添加到列表（不立即保存）
          passwords.value.push(newPassword)
          result.success++

          console.log(`✅ 已准备密码: ${newPassword.title}`)
        } catch (error) {
          console.error(`❌ 处理密码失败 (${passwordData.title}):`, error)
          result.failed++
          result.errors.push(`${passwordData.title}: ${error.message}`)
        }
      }

      // 批量保存所有密码
      if (result.success > 0) {
        await savePasswords()
        updateStatistics()
        console.log(`✅ 批量保存完成，成功添加 ${result.success} 个密码`)
      }

      return result
    } catch (error) {
      console.error('❌ 批量添加密码失败:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 批量更新密码强度
   */
  const updateAllPasswordStrengths = async (): Promise<void> => {
    try {
      isLoading.value = true
      console.log('🔄 批量更新密码强度...')

      for (const password of passwords.value) {
        password.strength = CryptoUtils.calculateStrength(password.password)
      }

      await savePasswords()
      updateStatistics()

      console.log('✅ 密码强度更新完成')
    } catch (error) {
      console.error('❌ 密码强度更新失败:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // ==================== 私有方法 ====================

  /**
   * 加载密码数据
   */
  const loadPasswords = async (): Promise<void> => {
    try {
      const data = await SecureStorage.getItem<PasswordItem[]>('passwords')
      if (data && Array.isArray(data) && data.length > 0) {
        passwords.value = data
        lastSyncTime.value = Date.now()
      } else {
        // 如果没有数据，添加一些测试数据
        await initializeTestData()
      }
    } catch (error) {
      console.error('❌ 加载密码数据失败:', error)
      // 加载失败时也添加测试数据
      await initializeTestData()
    }
  }

  /**
   * 初始化测试数据
   */
  const initializeTestData = async (): Promise<void> => {
    const testPasswords: PasswordItem[] = [
      {
        id: generateId(),
        title: 'Google',
        username: '<EMAIL>',
        password: 'MySecurePass123!',
        website: 'https://accounts.google.com',
        notes: '主要邮箱账户',
        category: 'work',
        tags: ['邮箱', '工作'],
        isFavorite: true,
        icon: 'google',
        iconType: 'fab',
        iconColor: '#4285f4',
        createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
        lastUsedAt: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),
        strength: CryptoUtils.calculateStrength('MySecurePass123!')
      },
      {
        id: generateId(),
        title: '中国银行',
        username: '****************',
        password: 'Bank@2024',
        website: 'https://www.boc.cn',
        notes: '工资卡账户',
        category: 'finance',
        tags: ['银行', '工资'],
        isFavorite: true,
        icon: 'university',
        iconType: 'fas',
        iconColor: '#c41e3a',
        createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
        lastUsedAt: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(),
        strength: CryptoUtils.calculateStrength('Bank@2024')
      },
      {
        id: generateId(),
        title: '微信',
        username: 'wechat_user',
        password: 'WeChat123',
        website: 'https://weixin.qq.com',
        notes: '日常聊天工具',
        category: 'social',
        tags: ['社交', '聊天'],
        isFavorite: false,
        icon: 'weixin',
        iconType: 'fab',
        iconColor: '#07c160',
        createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
        lastUsedAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
        strength: CryptoUtils.calculateStrength('WeChat123')
      },
      {
        id: generateId(),
        title: '淘宝',
        username: 'taobao_buyer',
        password: 'Shopping2024!',
        website: 'https://www.taobao.com',
        notes: '网购平台',
        category: 'shopping',
        tags: ['购物', '电商'],
        isFavorite: false,
        icon: 'shopping-cart',
        iconType: 'fas',
        iconColor: '#ff4400',
        createdAt: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - 8 * 24 * 60 * 60 * 1000).toISOString(),
        strength: CryptoUtils.calculateStrength('Shopping2024!')
      },
      {
        id: generateId(),
        title: 'GitHub',
        username: 'developer123',
        password: 'CodeSecure2024#',
        website: 'https://github.com',
        notes: '代码托管平台',
        category: 'work',
        tags: ['开发', '代码'],
        isFavorite: true,
        icon: 'github',
        iconType: 'fab',
        iconColor: '#333333',
        createdAt: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
        lastUsedAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
        strength: CryptoUtils.calculateStrength('CodeSecure2024#')
      },
      {
        id: generateId(),
        title: 'Netflix',
        username: 'movie_lover',
        password: 'Stream123',
        website: 'https://www.netflix.com',
        notes: '视频流媒体服务',
        category: 'entertainment',
        tags: ['视频', '娱乐'],
        isFavorite: false,
        icon: 'video',
        iconType: 'fas',
        iconColor: '#e50914',
        createdAt: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - 25 * 24 * 60 * 60 * 1000).toISOString(),
        strength: CryptoUtils.calculateStrength('Stream123')
      }
    ]

    passwords.value = testPasswords
    await savePasswords()
    console.log('✅ 测试数据初始化完成')
  }

  /**
   * 保存密码数据
   */
  const savePasswords = async (): Promise<void> => {
    try {
      await SecureStorage.setItem('passwords', passwords.value)
      lastSyncTime.value = Date.now()
    } catch (error) {
      console.error('❌ 保存密码数据失败:', error)
      throw new Error('保存密码数据失败')
    }
  }

  /**
   * 更新统计数据
   */
  const updateStatistics = (): void => {
    totalCount.value = passwords.value.length
    weakPasswordCount.value = weakPasswords.value.length
    duplicatePasswordCount.value = duplicatePasswords.value.length

    // 更新分类统计
    categories.value.forEach(category => {
      category.count = passwords.value.filter(item => item.category === category.id).length
    })
  }

  /**
   * 生成唯一ID
   */
  const generateId = (): string => {
    return 'pwd_' + Date.now().toString(36) + Math.random().toString(36).substr(2)
  }

  // ==================== 返回 Store 接口 ====================

  return {
    // 状态
    passwords,
    isLoading,
    lastSyncTime,
    categories,
    searchOptions,
    totalCount,
    weakPasswordCount,
    duplicatePasswordCount,
    expiredPasswordCount,

    // 计算属性
    filteredPasswords,
    favoritePasswords,
    recentPasswords,
    weakPasswords,
    duplicatePasswords,
    categoryStats,

    // 方法
    initialize,
    addPassword,
    addPasswordsBatch,
    updatePassword,
    deletePassword,
    getPassword,
    markAsUsed,
    toggleFavorite,
    updateSearchOptions,
    clearSearch,
    updateAllPasswordStrengths
  }
}, {
  // 持久化配置（密码数据通过 SecureStorage 加密存储，这里只持久化非敏感配置）
  persist: [
    'searchOptions'
  ]
})
