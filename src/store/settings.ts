/**
 * VaultKeeper 设置状态管理
 * 管理应用的各种设置和偏好配置
 * 
 * 功能：
 * - 主题设置
 * - 安全设置
 * - 通知设置
 * - 备份设置
 * - 显示设置
 */

import { ref, computed } from '@vue-mini/core'
import { defineStore } from '@vue-mini/pinia'
import { SecureStorage } from '../utils/storage'

/**
 * 主题类型
 */
export type ThemeType = 'dark' | 'light' | 'auto'

/**
 * 语言类型
 */
export type LanguageType = 'zh-CN' | 'en-US'

/**
 * 安全设置接口
 */
export interface SecuritySettings {
  autoLockEnabled: boolean;
  autoLockTimeout: number; // 分钟
  sessionTimeout: number; // 分钟
  biometricEnabled: boolean;
  requirePasswordForView: boolean;
  requirePasswordForCopy: boolean;
  clearClipboardTimeout: number; // 秒
  maxLoginAttempts: number;
  showPasswordStrength: boolean;
  checkWeakPasswords: boolean;
  checkDuplicatePasswords: boolean;
}

/**
 * 显示设置接口
 */
export interface DisplaySettings {
  theme: ThemeType;
  language: LanguageType;
  fontSize: 'small' | 'medium' | 'large';
  showPasswordPreview: boolean;
  hidePasswordsByDefault: boolean;
  showFavoritesFirst: boolean;
  defaultSortBy: 'title' | 'createdAt' | 'updatedAt' | 'lastUsedAt';
  defaultSortOrder: 'asc' | 'desc';
  itemsPerPage: number;
}

/**
 * 通知设置接口
 */
export interface NotificationSettings {
  enabled: boolean;
  weakPasswordAlert: boolean;
  duplicatePasswordAlert: boolean;
  securityTips: boolean;
  updateReminders: boolean;
  backupReminders: boolean;
}

/**
 * 备份设置接口
 */
export interface BackupSettings {
  autoBackupEnabled: boolean;
  backupFrequency: 'daily' | 'weekly' | 'monthly';
  backupLocation: 'local' | 'cloud';
  encryptBackup: boolean;
  maxBackupFiles: number;
  lastBackupTime?: string;
}

/**
 * 隐私设置接口
 */
export interface PrivacySettings {
  analyticsEnabled: boolean;
  crashReportingEnabled: boolean;
  usageStatisticsEnabled: boolean;
  shareAnonymousData: boolean;
}

/**
 * 设置状态管理 Store
 */
export const useSettingsStore = defineStore('settings', () => {
  // ==================== 状态定义 ====================
  
  // 安全设置
  const securitySettings = ref<SecuritySettings>({
    autoLockEnabled: true,
    autoLockTimeout: 5,
    sessionTimeout: 15,
    biometricEnabled: false,
    requirePasswordForView: false,
    requirePasswordForCopy: true,
    clearClipboardTimeout: 30,
    maxLoginAttempts: 5,
    showPasswordStrength: true,
    checkWeakPasswords: true,
    checkDuplicatePasswords: true
  })
  
  // 显示设置
  const displaySettings = ref<DisplaySettings>({
    theme: 'dark',
    language: 'zh-CN',
    fontSize: 'medium',
    showPasswordPreview: false,
    hidePasswordsByDefault: true,
    showFavoritesFirst: true,
    defaultSortBy: 'updatedAt',
    defaultSortOrder: 'desc',
    itemsPerPage: 20
  })
  
  // 通知设置
  const notificationSettings = ref<NotificationSettings>({
    enabled: true,
    weakPasswordAlert: true,
    duplicatePasswordAlert: true,
    securityTips: true,
    updateReminders: true,
    backupReminders: true
  })
  
  // 备份设置
  const backupSettings = ref<BackupSettings>({
    autoBackupEnabled: false,
    backupFrequency: 'weekly',
    backupLocation: 'local',
    encryptBackup: true,
    maxBackupFiles: 5,
    lastBackupTime: undefined
  })
  
  // 隐私设置
  const privacySettings = ref<PrivacySettings>({
    analyticsEnabled: false,
    crashReportingEnabled: true,
    usageStatisticsEnabled: false,
    shareAnonymousData: false
  })
  
  // 应用信息
  const appInfo = ref({
    version: '1.0.0',
    buildNumber: '1',
    installDate: new Date().toISOString(),
    lastUpdateCheck: null as string | null,
    totalUsageTime: 0, // 分钟
    launchCount: 0
  })
  
  // 加载状态
  const isLoading = ref<boolean>(false)
  const lastSyncTime = ref<number | null>(null)

  // ==================== 计算属性 ====================
  
  /**
   * 当前主题是否为深色
   */
  const isDarkTheme = computed(() => {
    if (displaySettings.value.theme === 'auto') {
      // 根据系统时间判断
      const hour = new Date().getHours()
      return hour < 6 || hour >= 18
    }
    return displaySettings.value.theme === 'dark'
  })
  
  /**
   * 字体大小对应的 CSS 值
   */
  const fontSizeValue = computed(() => {
    const sizeMap = {
      small: '26rpx',
      medium: '28rpx',
      large: '32rpx'
    }
    return sizeMap[displaySettings.value.fontSize]
  })
  
  /**
   * 是否启用了任何安全功能
   */
  const hasSecurityFeatures = computed(() => {
    return securitySettings.value.autoLockEnabled ||
           securitySettings.value.biometricEnabled ||
           securitySettings.value.requirePasswordForView ||
           securitySettings.value.requirePasswordForCopy
  })
  
  /**
   * 备份状态信息
   */
  const backupStatus = computed(() => {
    const lastBackup = backupSettings.value.lastBackupTime
    if (!lastBackup) {
      return { status: 'never', message: '从未备份' }
    }
    
    const lastBackupDate = new Date(lastBackup)
    const now = new Date()
    const diffHours = (now.getTime() - lastBackupDate.getTime()) / (1000 * 60 * 60)
    
    if (diffHours < 24) {
      return { status: 'recent', message: '最近已备份' }
    } else if (diffHours < 168) { // 7天
      return { status: 'warning', message: '需要备份' }
    } else {
      return { status: 'urgent', message: '急需备份' }
    }
  })

  // ==================== 操作方法 ====================
  
  /**
   * 初始化设置
   */
  const initialize = async (): Promise<void> => {
    try {
      isLoading.value = true
      console.log('⚙️ 初始化设置...')
      
      await loadSettings()
      
      // 更新启动次数
      appInfo.value.launchCount++
      await saveAppInfo()
      
      console.log('✅ 设置初始化完成')
    } catch (error) {
      console.error('❌ 设置初始化失败:', error)
      throw new Error('设置初始化失败')
    } finally {
      isLoading.value = false
    }
  }
  
  /**
   * 更新安全设置
   * @param updates 更新的设置
   */
  const updateSecuritySettings = async (updates: Partial<SecuritySettings>): Promise<void> => {
    try {
      securitySettings.value = {
        ...securitySettings.value,
        ...updates
      }
      
      await saveSettings()
      console.log('✅ 安全设置已更新')
    } catch (error) {
      console.error('❌ 安全设置更新失败:', error)
      throw error
    }
  }
  
  /**
   * 更新显示设置
   * @param updates 更新的设置
   */
  const updateDisplaySettings = async (updates: Partial<DisplaySettings>): Promise<void> => {
    try {
      displaySettings.value = {
        ...displaySettings.value,
        ...updates
      }
      
      await saveSettings()
      console.log('✅ 显示设置已更新')
    } catch (error) {
      console.error('❌ 显示设置更新失败:', error)
      throw error
    }
  }
  
  /**
   * 更新通知设置
   * @param updates 更新的设置
   */
  const updateNotificationSettings = async (updates: Partial<NotificationSettings>): Promise<void> => {
    try {
      notificationSettings.value = {
        ...notificationSettings.value,
        ...updates
      }
      
      await saveSettings()
      console.log('✅ 通知设置已更新')
    } catch (error) {
      console.error('❌ 通知设置更新失败:', error)
      throw error
    }
  }
  
  /**
   * 更新备份设置
   * @param updates 更新的设置
   */
  const updateBackupSettings = async (updates: Partial<BackupSettings>): Promise<void> => {
    try {
      backupSettings.value = {
        ...backupSettings.value,
        ...updates
      }
      
      await saveSettings()
      console.log('✅ 备份设置已更新')
    } catch (error) {
      console.error('❌ 备份设置更新失败:', error)
      throw error
    }
  }
  
  /**
   * 更新隐私设置
   * @param updates 更新的设置
   */
  const updatePrivacySettings = async (updates: Partial<PrivacySettings>): Promise<void> => {
    try {
      privacySettings.value = {
        ...privacySettings.value,
        ...updates
      }
      
      await saveSettings()
      console.log('✅ 隐私设置已更新')
    } catch (error) {
      console.error('❌ 隐私设置更新失败:', error)
      throw error
    }
  }
  
  /**
   * 重置所有设置为默认值
   */
  const resetToDefaults = async (): Promise<void> => {
    try {
      // 重置为默认值
      securitySettings.value = {
        autoLockEnabled: true,
        autoLockTimeout: 5,
        sessionTimeout: 15,
        biometricEnabled: false,
        requirePasswordForView: false,
        requirePasswordForCopy: true,
        clearClipboardTimeout: 30,
        maxLoginAttempts: 5,
        showPasswordStrength: true,
        checkWeakPasswords: true,
        checkDuplicatePasswords: true
      }
      
      displaySettings.value = {
        theme: 'dark',
        language: 'zh-CN',
        fontSize: 'medium',
        showPasswordPreview: false,
        hidePasswordsByDefault: true,
        showFavoritesFirst: true,
        defaultSortBy: 'updatedAt',
        defaultSortOrder: 'desc',
        itemsPerPage: 20
      }
      
      notificationSettings.value = {
        enabled: true,
        weakPasswordAlert: true,
        duplicatePasswordAlert: true,
        securityTips: true,
        updateReminders: true,
        backupReminders: true
      }
      
      backupSettings.value = {
        autoBackupEnabled: false,
        backupFrequency: 'weekly',
        backupLocation: 'local',
        encryptBackup: true,
        maxBackupFiles: 5,
        lastBackupTime: undefined
      }
      
      privacySettings.value = {
        analyticsEnabled: false,
        crashReportingEnabled: true,
        usageStatisticsEnabled: false,
        shareAnonymousData: false
      }
      
      await saveSettings()
      console.log('✅ 设置已重置为默认值')
    } catch (error) {
      console.error('❌ 重置设置失败:', error)
      throw error
    }
  }
  
  /**
   * 导出设置
   * @returns 设置数据
   */
  const exportSettings = (): string => {
    const settingsData = {
      securitySettings: securitySettings.value,
      displaySettings: displaySettings.value,
      notificationSettings: notificationSettings.value,
      backupSettings: backupSettings.value,
      privacySettings: privacySettings.value,
      exportTime: new Date().toISOString(),
      version: appInfo.value.version
    }
    
    return JSON.stringify(settingsData, null, 2)
  }
  
  /**
   * 导入设置
   * @param settingsJson 设置 JSON 字符串
   */
  const importSettings = async (settingsJson: string): Promise<void> => {
    try {
      const settingsData = JSON.parse(settingsJson)
      
      // 验证数据格式
      if (!settingsData || typeof settingsData !== 'object') {
        throw new Error('设置数据格式不正确')
      }
      
      // 导入各项设置
      if (settingsData.securitySettings) {
        securitySettings.value = { ...securitySettings.value, ...settingsData.securitySettings }
      }
      
      if (settingsData.displaySettings) {
        displaySettings.value = { ...displaySettings.value, ...settingsData.displaySettings }
      }
      
      if (settingsData.notificationSettings) {
        notificationSettings.value = { ...notificationSettings.value, ...settingsData.notificationSettings }
      }
      
      if (settingsData.backupSettings) {
        backupSettings.value = { ...backupSettings.value, ...settingsData.backupSettings }
      }
      
      if (settingsData.privacySettings) {
        privacySettings.value = { ...privacySettings.value, ...settingsData.privacySettings }
      }
      
      await saveSettings()
      console.log('✅ 设置导入成功')
    } catch (error) {
      console.error('❌ 设置导入失败:', error)
      throw new Error('设置导入失败: ' + error.message)
    }
  }
  
  /**
   * 记录使用时间
   * @param minutes 使用时间（分钟）
   */
  const recordUsageTime = async (minutes: number): Promise<void> => {
    try {
      appInfo.value.totalUsageTime += minutes
      await saveAppInfo()
    } catch (error) {
      console.error('❌ 记录使用时间失败:', error)
    }
  }

  // ==================== 私有方法 ====================
  
  /**
   * 加载设置
   */
  const loadSettings = async (): Promise<void> => {
    try {
      // 加载各项设置
      const security = await SecureStorage.getItem('security_settings')
      if (security) {
        securitySettings.value = { ...securitySettings.value, ...security }
      }
      
      const display = await SecureStorage.getItem('display_settings')
      if (display) {
        displaySettings.value = { ...displaySettings.value, ...display }
      }
      
      const notification = await SecureStorage.getItem('notification_settings')
      if (notification) {
        notificationSettings.value = { ...notificationSettings.value, ...notification }
      }
      
      const backup = await SecureStorage.getItem('backup_settings')
      if (backup) {
        backupSettings.value = { ...backupSettings.value, ...backup }
      }
      
      const privacy = await SecureStorage.getItem('privacy_settings')
      if (privacy) {
        privacySettings.value = { ...privacySettings.value, ...privacy }
      }
      
      // 加载应用信息
      const info = await SecureStorage.getItem('app_info')
      if (info) {
        appInfo.value = { ...appInfo.value, ...info }
      }
      
      lastSyncTime.value = Date.now()
    } catch (error) {
      console.error('❌ 加载设置失败:', error)
    }
  }
  
  /**
   * 保存设置
   */
  const saveSettings = async (): Promise<void> => {
    try {
      await Promise.all([
        SecureStorage.setItem('security_settings', securitySettings.value, { encrypt: false }),
        SecureStorage.setItem('display_settings', displaySettings.value, { encrypt: false }),
        SecureStorage.setItem('notification_settings', notificationSettings.value, { encrypt: false }),
        SecureStorage.setItem('backup_settings', backupSettings.value, { encrypt: false }),
        SecureStorage.setItem('privacy_settings', privacySettings.value, { encrypt: false })
      ])
      
      lastSyncTime.value = Date.now()
    } catch (error) {
      console.error('❌ 保存设置失败:', error)
      throw new Error('保存设置失败')
    }
  }
  
  /**
   * 保存应用信息
   */
  const saveAppInfo = async (): Promise<void> => {
    try {
      await SecureStorage.setItem('app_info', appInfo.value, { encrypt: false })
    } catch (error) {
      console.error('❌ 保存应用信息失败:', error)
    }
  }

  // ==================== 返回 Store 接口 ====================
  
  return {
    // 状态
    securitySettings,
    displaySettings,
    notificationSettings,
    backupSettings,
    privacySettings,
    appInfo,
    isLoading,
    lastSyncTime,
    
    // 计算属性
    isDarkTheme,
    fontSizeValue,
    hasSecurityFeatures,
    backupStatus,
    
    // 方法
    initialize,
    updateSecuritySettings,
    updateDisplaySettings,
    updateNotificationSettings,
    updateBackupSettings,
    updatePrivacySettings,
    resetToDefaults,
    exportSettings,
    importSettings,
    recordUsageTime
  }
}, {
  // 持久化配置（非敏感设置可以直接持久化）
  persist: [
    'displaySettings',
    'notificationSettings'
  ]
})
