# TabBar 图标生成说明

## 图标需求

为了确保 tabBar 图标在小程序中正确显示，我们使用 Unicode 字符图标系统。

### 当前图标映射

1. **密码库** (home) - 🏠 (U+1F3E0)
2. **生成器** (key) - 🔑 (U+1F511)  
3. **安全** (shield) - 🛡️ (U+1F6E1)
4. **设置** (cog) - ⚙️ (U+2699)

### 图标样式类

```css
.icon-home:before { content: "🏠"; }
.icon-key:before { content: "🔑"; }
.icon-shield:before { content: "🛡️"; }
.icon-cog:before { content: "⚙️"; }
```

### 使用方式

在 WXML 中使用：
```xml
<text class="iconfont icon-home"></text>
<text class="iconfont icon-key"></text>
<text class="iconfont icon-shield"></text>
<text class="iconfont icon-cog"></text>
```

### 注意事项

1. 小程序不支持直接使用 FontAwesome 字体文件
2. Unicode 字符图标在所有设备上显示一致
3. 图标大小通过 CSS font-size 控制
4. 图标颜色通过 CSS color 控制

### 备选方案

如果需要更精确的图标控制，可以考虑：
1. 使用 SVG 图标转换为 base64
2. 使用小程序原生 icon 组件
3. 使用图片图标（需要准备多套尺寸）
