/**
 * FontAwesome 图标样式 - 小程序版本
 * 使用 Emoji 和符号字符实现图标显示
 * 由于小程序不支持 ::before 和 content，使用直接字符映射
 */

/* 基础图标类 */
.fa, .fas, .far, .fab {
  font-style: normal;
  font-weight: normal;
  line-height: 1;
  display: inline-block;
  text-decoration: inherit;
}

/* 图标尺寸 */
.fa-xs { font-size: 24rpx; }
.fa-sm { font-size: 28rpx; }
.fa-lg { font-size: 40rpx; }
.fa-xl { font-size: 48rpx; }
.fa-2x { font-size: 64rpx; }
.fa-3x { font-size: 96rpx; }
.fa-4x { font-size: 128rpx; }
.fa-5x { font-size: 160rpx; }

/* 图标映射 - 使用 Emoji 和 Unicode 符号 */
.fa-home { font-family: inherit; }
.fa-home:after { content: "🏠"; }

.fa-search { font-family: inherit; }
.fa-search:after { content: "🔍"; }

.fa-plus { font-family: inherit; }
.fa-plus:after { content: "➕"; }

.fa-copy { font-family: inherit; }
.fa-copy:after { content: "📋"; }

.fa-shield-alt { font-family: inherit; }
.fa-shield-alt:after { content: "🛡️"; }

.fa-lock { font-family: inherit; }
.fa-lock:after { content: "🔒"; }

.fa-key { font-family: inherit; }
.fa-key:after { content: "🔑"; }

.fa-fingerprint { font-family: inherit; }
.fa-fingerprint:after { content: "👆"; }

.fa-google { font-family: inherit; }
.fa-google:after { content: "🌐"; }

.fa-github { font-family: inherit; }
.fa-github:after { content: "🐙"; }

.fa-university { font-family: inherit; }
.fa-university:after { content: "🏦"; }

.fa-shopping-cart { font-family: inherit; }
.fa-shopping-cart:after { content: "🛒"; }

.fa-globe { font-family: inherit; }
.fa-globe:after { content: "🌐"; }

.fa-wechat { font-family: inherit; }
.fa-wechat:after { content: "💬"; }

.fa-video { font-family: inherit; }
.fa-video:after { content: "🎥"; }

.fa-spinner { font-family: inherit; }
.fa-spinner:after { content: "⏳"; }

.fa-exclamation-circle { font-family: inherit; }
.fa-exclamation-circle:after { content: "⚠️"; }

/* 新增原型页面需要的图标 */
.fa-arrow-left { font-family: inherit; }
.fa-arrow-left:after { content: "←"; }

.fa-pen { font-family: inherit; }
.fa-pen:after { content: "✏️"; }

.fa-trash-alt { font-family: inherit; }
.fa-trash-alt:after { content: "🗑️"; }

.fa-eye { font-family: inherit; }
.fa-eye:after { content: "👁️"; }

.fa-external-link-alt { font-family: inherit; }
.fa-external-link-alt:after { content: "🔗"; }

.fa-redo { font-family: inherit; }
.fa-redo:after { content: "🔄"; }

.fa-minus { font-family: inherit; }
.fa-minus:after { content: "−"; }

.fa-chevron-down { font-family: inherit; }
.fa-chevron-down:after { content: "⌄"; }

.fa-chevron-right { font-family: inherit; }
.fa-chevron-right:after { content: "⌵"; }

.fa-save { font-family: inherit; }
.fa-save:after { content: "💾"; }

.fa-clone { font-family: inherit; }
.fa-clone:after { content: "📑"; }

.fa-sync-alt { font-family: inherit; }
.fa-sync-alt:after { content: "🔄"; }

.fa-exclamation-triangle { font-family: inherit; }
.fa-exclamation-triangle:after { content: "⚠️"; }

.fa-cog { font-family: inherit; }
.fa-cog:after { content: "⚙️"; }

.fa-cloud { font-family: inherit; }
.fa-cloud:after { content: "☁️"; }

.fa-file-import { font-family: inherit; }
.fa-file-import:after { content: "📥"; }

.fa-file-export { font-family: inherit; }
.fa-file-export:after { content: "📤"; }

.fa-palette { font-family: inherit; }
.fa-palette:after { content: "🎨"; }

.fa-question-circle { font-family: inherit; }
.fa-question-circle:after { content: "❓"; }

.fa-info-circle { font-family: inherit; }
.fa-info-circle:after { content: "ℹ️"; }

.fa-signal { font-family: inherit; }
.fa-signal:after { content: "📶"; }

.fa-wifi { font-family: inherit; }
.fa-wifi:after { content: "📶"; }

.fa-battery-full { font-family: inherit; }
.fa-battery-full:after { content: "🔋"; }

.fa-youtube { font-family: inherit; }
.fa-youtube:after { content: "📺"; }

.fa-facebook { font-family: inherit; }
.fa-facebook:after { content: "📘"; }

.fa-twitter { font-family: inherit; }
.fa-twitter:after { content: "🐦"; }

.fa-shopping-bag { font-family: inherit; }
.fa-shopping-bag:after { content: "🛍️"; }

.fa-music { font-family: inherit; }
.fa-music:after { content: "🎵"; }

.fa-bell { font-family: inherit; }
.fa-bell:after { content: "🔔"; }