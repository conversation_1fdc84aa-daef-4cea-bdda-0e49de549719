/**
 * VaultKeeper 密码强度指示器组件样式
 * 完美还原原型设计，支持多种强度级别显示
 */

/* ==================== 组件容器 ==================== */

.strength-meter {
  width: 100%;
}

/* ==================== 强度条 ==================== */

.strength-meter__bar {
  width: 100%;
  height: 8rpx;
  border-radius: var(--radius-xs);
  background: rgba(255, 255, 255, 0.1);
  overflow: hidden;
  position: relative;
}

.strength-meter__fill {
  height: 100%;
  border-radius: var(--radius-xs);
  transition: width 0.3s ease, background-color 0.3s ease;
  position: relative;
}

/* 强度级别颜色 */
.strength-meter__fill--weak {
  background: var(--strength-weak);
}

.strength-meter__fill--medium {
  background: var(--strength-medium);
}

.strength-meter__fill--strong {
  background: var(--strength-strong);
}

.strength-meter__fill--very-strong {
  background: var(--strength-very-strong);
}

/* ==================== 强度信息 ==================== */

.strength-meter__info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 8rpx;
}

.strength-meter__text {
  font-size: var(--font-xs);
  font-weight: 500;
  transition: color 0.3s ease;
}

.strength-meter__text--weak {
  color: var(--strength-weak);
}

.strength-meter__text--medium {
  color: var(--strength-medium);
}

.strength-meter__text--strong {
  color: var(--strength-strong);
}

.strength-meter__text--very-strong {
  color: var(--strength-very-strong);
}

.strength-meter__score {
  font-size: var(--font-xs);
  color: var(--text-muted);
  font-weight: 400;
}

/* ==================== 强度建议 ==================== */

.strength-meter__feedback {
  margin-top: 8rpx;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.strength-meter__feedback-item {
  font-size: var(--font-xs);
  color: var(--text-muted);
  line-height: 1.4;
}

.strength-meter__feedback-item::before {
  content: "• ";
  color: var(--accent-color);
  font-weight: bold;
  margin-right: 4rpx;
}

/* ==================== 响应式适配 ==================== */

@media (max-width: 375px) {
  .strength-meter__bar {
    height: 6rpx;
  }
  
  .strength-meter__text,
  .strength-meter__score,
  .strength-meter__feedback-item {
    font-size: 22rpx;
  }
}

/* ==================== 动画效果 ==================== */

.strength-meter__fill {
  animation: strength-fill 0.6s ease-out;
}

@keyframes strength-fill {
  from {
    width: 0%;
    opacity: 0.5;
  }
  to {
    opacity: 1;
  }
}

/* ==================== 无障碍支持 ==================== */

.strength-meter[aria-hidden="true"] {
  display: none;
}

/* ==================== 主题适配 ==================== */

.strength-meter {
  --strength-weak: #ff4757;
  --strength-medium: #ffa502;
  --strength-strong: #2ed573;
  --strength-very-strong: #1dd1a1;
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .strength-meter__bar {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
  }
  
  .strength-meter__fill {
    border: 1px solid currentColor;
  }
}
