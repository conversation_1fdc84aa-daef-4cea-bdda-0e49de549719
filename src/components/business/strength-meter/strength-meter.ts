/**
 * VaultKeeper 密码强度指示器组件
 * 完美还原原型设计，显示密码强度和建议
 * 
 * 功能特性：
 * - 实时密码强度显示
 * - 多级强度指示（弱、中等、强、很强）
 * - 强度建议和反馈
 * - 动画效果和颜色变化
 * - 可配置显示选项
 */

import { defineComponent, PropType, ref, watch, onMounted } from '@vue-mini/wechat'

/**
 * 密码强度数据接口
 */
interface PasswordStrength {
  score: number
  level: 'weak' | 'medium' | 'strong' | 'very-strong'
  feedback: string[]
}

defineComponent({
  /**
   * 组件属性定义
   */
  properties: {
    // 基础属性
    customClass: {
      type: String,
      value: ''
    },
    customStyle: {
      type: String,
      value: ''
    },
    
    // 强度数据
    strength: {
      type: Object as PropType<PasswordStrength>,
      value: null
    },
    
    // 显示选项
    showScore: {
      type: Boolean,
      value: false
    },
    showFeedback: {
      type: Boolean,
      value: true
    },
    
    // 动画选项
    animated: {
      type: Boolean,
      value: true
    }
  },

  /**
   * 组件设置
   */
  setup(props, { emit }) {
    console.log('📊 StrengthMeter 组件初始化:', props)

    // 响应式数据
    const strengthLevel = ref('weak')
    const strengthText = ref('弱')
    const strengthScore = ref(0)
    const strengthWidth = ref(0)
    const feedback = ref([])

    /**
     * 监听强度数据变化
     */
    const watchStrength = () => {
      if (!props.strength) {
        strengthLevel.value = 'weak'
        strengthText.value = '弱'
        strengthScore.value = 0
        strengthWidth.value = 0
        feedback.value = []
        return
      }

      const { score, level, feedback: strengthFeedback } = props.strength

      // 更新强度信息
      strengthLevel.value = level
      strengthText.value = getStrengthText(level)
      strengthScore.value = score
      strengthWidth.value = calculateWidth(score)
      feedback.value = strengthFeedback || []

      console.log('📊 密码强度更新:', {
        level,
        score,
        width: calculateWidth(score)
      })
    }

    // 监听强度数据变化
    watch(() => props.strength, (newStrength) => {
      watchStrength()
    }, { immediate: true })

    // 组件挂载时初始化
    onMounted(() => {
      if (props.strength) {
        watchStrength()
      }
    })

    /**
     * 获取强度文本
     */
    const getStrengthText = (level: string): string => {
      const textMap = {
        'weak': '弱',
        'medium': '中等',
        'strong': '强',
        'very-strong': '很强'
      }
      return textMap[level] || '弱'
    }

    /**
     * 计算强度条宽度
     */
    const calculateWidth = (score: number): number => {
      // 确保分数在 0-100 范围内
      const normalizedScore = Math.max(0, Math.min(100, score))
      
      // 根据分数计算宽度百分比
      if (normalizedScore === 0) return 0
      if (normalizedScore <= 30) return Math.max(15, normalizedScore * 0.5) // 弱密码最少显示15%
      if (normalizedScore <= 60) return 30 + (normalizedScore - 30) * 1.0 // 中等密码30-60%
      if (normalizedScore <= 80) return 60 + (normalizedScore - 60) * 1.5 // 强密码60-90%
      return 90 + (normalizedScore - 80) * 0.5 // 很强密码90-100%
    }

    /**
     * 手动更新强度数据
     * @param strength 强度数据
     */
    const updateStrength = (strength: PasswordStrength) => {
      // 这里应该通过 props 更新，而不是直接设置
      watchStrength()
    }

    /**
     * 获取当前强度信息
     */
    const getStrengthInfo = () => {
      return {
        level: strengthLevel.value,
        text: strengthText.value,
        score: strengthScore.value,
        width: strengthWidth.value,
        feedback: feedback.value
      }
    }

    // 返回响应式数据和方法
    return {
      // 响应式数据
      strengthLevel,
      strengthText,
      strengthScore,
      strengthWidth,
      feedback,

      // 方法
      watchStrength,
      getStrengthText,
      calculateWidth,
      updateStrength,
      getStrengthInfo
    }
  }
})
