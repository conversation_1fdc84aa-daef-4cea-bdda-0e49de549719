/**
 * VaultKeeper 密码条目组件样式
 * 完美还原原型设计，密码列表项展示
 */

.password-item {
  margin-bottom: var(--spacing-md);
  transition: var(--transition-all);
}

.password-item:active {
  transform: scale(0.98);
}

/* ==================== 主要内容 ==================== */

.password-item__content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-lg);
}

.password-item__left {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.password-item__right {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  margin-left: var(--spacing-md);
}

/* ==================== 图标 ==================== */

.password-item__icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--spacing-md);
  position: relative;
  overflow: hidden;
}

.password-item__icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.2) 0%, 
    rgba(255, 255, 255, 0.1) 100%);
  pointer-events: none;
}

.password-item__icon .iconfont {
  font-size: var(--font-xl);
  color: var(--text-white);
  position: relative;
  z-index: 1;
}

.password-item__icon-text {
  font-size: var(--font-lg);
  font-weight: var(--font-weight-bold);
  color: var(--text-white);
  text-transform: uppercase;
  position: relative;
  z-index: 1;
}

/* ==================== 信息区域 ==================== */

.password-item__info {
  flex: 1;
  min-width: 0;
}

.password-item__title-row {
  display: flex;
  align-items: center;
  margin-bottom: 4rpx;
  gap: var(--spacing-xs);
}

.password-item__title {
  font-size: var(--font-base);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.password-item__favorite {
  color: var(--error-color);
  font-size: var(--font-sm);
}

.password-item__category {
  background: var(--accent-color);
  color: var(--text-white);
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: var(--radius-xs);
  line-height: 1;
}

.password-item__category-text {
  font-weight: var(--font-weight-medium);
}

.password-item__subtitle-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 4rpx;
}

.password-item__username {
  font-size: var(--font-sm);
  color: var(--text-secondary);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
}

.password-item__website {
  font-size: var(--font-xs);
  color: var(--text-muted);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-bottom: 4rpx;
}

.password-item__last-used {
  font-size: var(--font-xs);
  color: var(--text-muted);
}

/* ==================== 密码强度 ==================== */

.password-item__strength {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.password-item__strength-bar {
  width: 40rpx;
  height: 6rpx;
  border-radius: var(--radius-xs);
  position: relative;
  overflow: hidden;
}

.password-item__strength-bar--weak {
  background: var(--strength-weak);
}

.password-item__strength-bar--medium {
  background: var(--strength-medium);
}

.password-item__strength-bar--strong {
  background: var(--strength-strong);
}

.password-item__strength-bar--very-strong {
  background: var(--strength-very-strong);
}

.password-item__strength-text {
  font-size: 20rpx;
  color: var(--text-muted);
  font-weight: var(--font-weight-medium);
}

/* ==================== 操作按钮 ==================== */

.password-item__action {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-secondary);
  font-size: var(--font-base);
  cursor: pointer;
  transition: var(--transition-all);
}

.password-item__action:active {
  transform: scale(0.9);
}

.password-item__action--copy {
  background: rgba(46, 213, 115, 0.2);
  color: var(--success-color);
}

.password-item__action--copy:active {
  background: rgba(46, 213, 115, 0.3);
}

.password-item__action--favorite {
  background: rgba(255, 71, 87, 0.2);
  color: var(--error-color);
}

.password-item__action--favorite:active {
  background: rgba(255, 71, 87, 0.3);
}

.password-item__action--more:active {
  background: rgba(255, 255, 255, 0.2);
  color: var(--text-primary);
}

/* ==================== 安全警告 ==================== */

.password-item__warnings {
  display: flex;
  gap: var(--spacing-xs);
  padding: 0 var(--spacing-lg) var(--spacing-md);
  flex-wrap: wrap;
}

.password-item__warning {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 6rpx 12rpx;
  border-radius: var(--radius-xs);
  font-size: 20rpx;
  line-height: 1;
}

.password-item__warning--weak {
  background: rgba(255, 71, 87, 0.2);
  color: var(--error-color);
}

.password-item__warning--duplicate {
  background: rgba(255, 165, 2, 0.2);
  color: var(--warning-color);
}

.password-item__warning--expired {
  background: rgba(255, 165, 2, 0.2);
  color: var(--warning-color);
}

.password-item__warning .iconfont {
  font-size: 24rpx;
}

.password-item__warning-text {
  font-weight: var(--font-weight-medium);
}

/* ==================== 状态变体 ==================== */

.password-item--compact .password-item__content {
  padding: var(--spacing-md) var(--spacing-lg);
}

.password-item--compact .password-item__icon {
  width: 64rpx;
  height: 64rpx;
}

.password-item--compact .password-item__icon .iconfont {
  font-size: var(--font-lg);
}

.password-item--compact .password-item__title {
  font-size: var(--font-sm);
}

.password-item--compact .password-item__username {
  font-size: var(--font-xs);
}

/* ==================== 响应式适配 ==================== */

@media (max-width: 375px) {
  .password-item__content {
    padding: var(--spacing-md);
  }
  
  .password-item__icon {
    width: 72rpx;
    height: 72rpx;
    margin-right: var(--spacing-sm);
  }
  
  .password-item__icon .iconfont {
    font-size: var(--font-lg);
  }
  
  .password-item__right {
    gap: 4rpx;
    margin-left: var(--spacing-sm);
  }
  
  .password-item__action {
    width: 56rpx;
    height: 56rpx;
    font-size: var(--font-sm);
  }
}

/* ==================== 动画效果 ==================== */

.password-item {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(20rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ==================== 深色模式适配 ==================== */

@media (prefers-color-scheme: dark) {
  .password-item__action {
    background: rgba(255, 255, 255, 0.08);
  }
  
  .password-item__action:active {
    background: rgba(255, 255, 255, 0.15);
  }
}
