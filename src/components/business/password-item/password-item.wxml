<!--
  Vault<PERSON>eeper 密码条目组件
  完美还原原型设计，展示密码信息和快速操作
-->

<glass-card 
  class="password-item {{customClass}}"
  clickable="{{true}}"
  bind:tap="onItemTap"
>
  <view class="password-item__content">
    <!-- 左侧图标和信息 -->
    <view class="password-item__left">
      <!-- 网站图标 -->
      <view class="password-item__icon" style="background-color: {{iconColor}};">
        <text wx:if="{{password.icon}}" class="{{password.iconType || 'fab'}} fa-{{password.icon}}"></text>
        <text wx:else class="password-item__icon-text">{{iconText}}</text>
      </view>
      
      <!-- 密码信息 -->
      <view class="password-item__info">
        <view class="password-item__title-row">
          <text class="password-item__title">{{password.title}}</text>
          
          <!-- 收藏标识 -->
          <view wx:if="{{password.isFavorite}}" class="password-item__favorite">
            <text class="fas fa-heart"></text>
          </view>
          
          <!-- 分类标签 -->
          <view wx:if="{{showCategory}}" class="password-item__category">
            <text class="password-item__category-text">{{categoryName}}</text>
          </view>
        </view>
        
        <view class="password-item__subtitle-row">
          <text class="password-item__username">{{password.username || '无用户名'}}</text>
          
          <!-- 密码强度指示器 -->
          <view wx:if="{{password.strength}}" class="password-item__strength">
            <view class="password-item__strength-bar password-item__strength-bar--{{strengthLevel}}"></view>
            <text class="password-item__strength-text">{{strengthText}}</text>
          </view>
        </view>
        
        <!-- 网站地址 -->
        <text wx:if="{{password.website}}" class="password-item__website">{{password.website}}</text>
        
        <!-- 最后使用时间 -->
        <text wx:if="{{showLastUsed && password.lastUsedAt}}" class="password-item__last-used">
          最后使用：{{lastUsedText}}
        </text>
      </view>
    </view>
    
    <!-- 右侧操作按钮 -->
    <view class="password-item__right">
      <!-- 复制按钮 -->
      <view
        class="password-item__action password-item__action--copy"
        bind:tap="onCopyTap"
        catch:tap="onCopyTap"
      >
        <text class="far fa-copy"></text>
      </view>

      <!-- 收藏按钮 -->
      <view
        class="password-item__action password-item__action--favorite"
        bind:tap="onFavoriteTap"
        catch:tap="onFavoriteTap"
      >
        <text class="{{password.isFavorite ? 'fas' : 'far'}} fa-heart"></text>
      </view>

      <!-- 更多操作按钮 -->
      <view
        class="password-item__action password-item__action--more"
        bind:tap="onMoreTap"
        catch:tap="onMoreTap"
      >
        <text class="fas fa-ellipsis-h"></text>
      </view>
    </view>
  </view>
  
  <!-- 安全警告 -->
  <view wx:if="{{showWarnings && hasWarnings}}" class="password-item__warnings">
    <view wx:if="{{isWeak}}" class="password-item__warning password-item__warning--weak">
      <text class="fas fa-exclamation-triangle"></text>
      <text class="password-item__warning-text">弱密码</text>
    </view>

    <view wx:if="{{isDuplicate}}" class="password-item__warning password-item__warning--duplicate">
      <text class="fas fa-clone"></text>
      <text class="password-item__warning-text">重复密码</text>
    </view>

    <view wx:if="{{isExpired}}" class="password-item__warning password-item__warning--expired">
      <text class="fas fa-clock"></text>
      <text class="password-item__warning-text">需要更新</text>
    </view>
  </view>
</glass-card>
