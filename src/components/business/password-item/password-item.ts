/**
 * VaultKeeper 密码条目组件
 * 完美还原原型设计，展示密码信息和快速操作
 * 
 * 功能特性：
 * - 密码信息展示
 * - 快速复制和收藏
 * - 密码强度指示
 * - 安全警告提示
 * - 分类和时间显示
 */

import { defineComponent, ref, watch } from '@vue-mini/core'
import { PasswordItem } from '../../../store/password'

defineComponent({
  /**
   * 组件属性定义
   */
  properties: {
    // 基础属性
    customClass: {
      type: String,
      value: ''
    },
    
    // 密码数据
    password: {
      type: Object as PropType<PasswordItem>,
      value: null
    },
    
    // 显示选项
    showCategory: {
      type: Boolean,
      value: false
    },
    showLastUsed: {
      type: Boolean,
      value: false
    },
    showWarnings: {
      type: Boolean,
      value: true
    },
    
    // 样式选项
    compact: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 事件定义
   */
  emits: {
    tap: (_password: PasswordItem) => true,
    copy: (_password: PasswordItem) => true,
    favorite: (_password: PasswordItem) => true,
    more: (_password: PasswordItem) => true
  },

  /**
   * 组件设置
   */
  setup(props, { emit }) {
    console.log('🔑 PasswordItem 组件初始化:', props.password?.title)

    // 响应式数据
    const iconText = ref('')
    const iconColor = ref('#6d4aff')
    const categoryName = ref('')
    const strengthLevel = ref('medium')
    const strengthText = ref('中等')
    const lastUsedText = ref('')
    const hasWarnings = ref(false)
    const isWeak = ref(false)
    const isDuplicate = ref(false)
    const isExpired = ref(false)

    // 监听密码数据变化
    watch(() => props.password, (newVal: PasswordItem) => {
      if (newVal) {
        updateDisplayData()
      }
    }, { immediate: true })

    // 组件挂载时初始化
    attached(() => {
      updateDisplayData()
    })

    /**
     * 更新显示数据
     */
    const updateDisplayData = () => {
      if (!props.password) return

      // 生成图标文本和颜色
      const iconData = generateIconData(props.password)
      
      // 获取分类名称
      const categoryNameValue = getCategoryName(props.password.category)

      // 格式化密码强度
      const strengthData = formatPasswordStrength(props.password.strength)

      // 格式化最后使用时间
      const lastUsedTextValue = formatLastUsedTime(props.password.lastUsedAt)

      // 检查安全警告
      const warningData = checkSecurityWarnings(props.password)

      // 更新响应式数据
      iconText.value = iconData.text
      iconColor.value = iconData.color
      categoryName.value = categoryNameValue
      strengthLevel.value = strengthData.level
      strengthText.value = strengthData.text
      lastUsedText.value = lastUsedTextValue
      hasWarnings.value = warningData.hasWarnings
      isWeak.value = warningData.isWeak
      isDuplicate.value = warningData.isDuplicate
      isExpired.value = warningData.isExpired
    }

    /**
     * 生成图标数据
     */
    const generateIconData = (password: PasswordItem) => {
      // 如果有自定义图标，使用默认颜色
      if (password.icon) {
        return {
          text: '',
          color: '#6d4aff'
        }
      }
      
      // 根据标题生成首字母图标
      const title = password.title || 'P'
      const firstChar = title.charAt(0).toUpperCase()
      
      // 根据首字母生成颜色
      const colors = [
        '#6d4aff', '#3742fa', '#2ed573', '#ffa502', 
        '#ff4757', '#8c66ff', '#00d4aa', '#ff6b7a'
      ]
      const colorIndex = firstChar.charCodeAt(0) % colors.length
      
      return {
        text: firstChar,
        color: colors[colorIndex]
      }
    }

    /**
     * 获取分类名称
     */
    const getCategoryName = (categoryId: string) => {
      const categoryMap = {
        common: '常用',
        work: '工作',
        social: '社交',
        finance: '金融',
        shopping: '购物',
        entertainment: '娱乐',
        other: '其他'
      }
      
      return categoryMap[categoryId] || '其他'
    }

    /**
     * 格式化密码强度
     */
    const formatPasswordStrength = (strength: any) => {
      if (!strength) {
        return { level: 'medium', text: '中等' }
      }
      
      const levelMap = {
        'weak': { level: 'weak', text: '弱' },
        'medium': { level: 'medium', text: '中等' },
        'strong': { level: 'strong', text: '强' },
        'very-strong': { level: 'very-strong', text: '很强' }
      }
      
      return levelMap[strength.level] || { level: 'medium', text: '中等' }
    }

    /**
     * 格式化最后使用时间
     */
    const formatLastUsedTime = (lastUsedAt?: string) => {
      if (!lastUsedAt) return ''
      
      const now = new Date()
      const lastUsed = new Date(lastUsedAt)
      const diffMs = now.getTime() - lastUsed.getTime()
      const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
      const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
      const diffMinutes = Math.floor(diffMs / (1000 * 60))
      
      if (diffDays > 0) {
        return `${diffDays}天前`
      } else if (diffHours > 0) {
        return `${diffHours}小时前`
      } else if (diffMinutes > 0) {
        return `${diffMinutes}分钟前`
      } else {
        return '刚刚'
      }
    }

    /**
     * 检查安全警告
     */
    const checkSecurityWarnings = (password: PasswordItem) => {
      const isWeak = password.strength && password.strength.score < 60
      const isDuplicate = false // 需要从 store 中获取重复密码信息
      const isExpired = false // 需要根据密码创建时间判断是否过期
      
      return {
        hasWarnings: isWeak || isDuplicate || isExpired,
        isWeak,
        isDuplicate,
        isExpired
      }
    }

    /**
     * 处理条目点击
     */
    const onItemTap = () => {
      console.log('👆 点击密码条目:', props.password?.title)
      emit('tap', props.password)
    }

    /**
     * 处理复制点击
     */
    const onCopyTap = () => {
      console.log('📋 复制密码:', props.password?.title)
      emit('copy', props.password)
      
      // 触觉反馈
      if (wx.vibrateShort) {
        wx.vibrateShort({
          type: 'light'
        })
      }
    }

    /**
     * 处理收藏点击
     */
    const onFavoriteTap = () => {
      console.log('⭐ 切换收藏:', props.password?.title)
      emit('favorite', props.password)
      
      // 触觉反馈
      if (wx.vibrateShort) {
        wx.vibrateShort({
          type: 'light'
        })
      }
    }

    /**
     * 处理更多操作点击
     */
    const onMoreTap = () => {
      console.log('⋯ 更多操作:', props.password?.title)
      emit('more', props.password)
      
      // 显示操作菜单
      wx.showActionSheet({
        itemList: ['编辑', '删除', '分享'],
        success: (res) => {
          console.log('选择操作:', res.tapIndex)
          // 这里可以根据选择的操作进行相应处理
        }
      })
    }

    // 返回响应式数据和方法
    return {
      // 响应式数据
      iconText,
      iconColor,
      categoryName,
      strengthLevel,
      strengthText,
      lastUsedText,
      hasWarnings,
      isWeak,
      isDuplicate,
      isExpired,

      // 方法
      updateDisplayData,
      generateIconData,
      getCategoryName,
      formatPasswordStrength,
      formatLastUsedTime,
      checkSecurityWarnings,
      onItemTap,
      onCopyTap,
      onFavoriteTap,
      onMoreTap
    }
  }
})
