/**
 * Vault<PERSON>eeper 玻璃拟态卡片组件样式
 * 完美还原原型设计，适配小程序环境
 */

.glass-card {
  position: relative;
  background: var(--glass-bg);
  border: 1rpx solid var(--glass-border);
  border-radius: var(--radius-md);
  overflow: hidden;
  transition: var(--transition-all);
  
  /* 小程序环境下的玻璃效果优化 */
  box-shadow: 
    0 16rpx 64rpx rgba(0, 0, 0, 0.3),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.1);
}

/* 玻璃效果背景层 */
.glass-card__background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.1) 0%, 
    rgba(255, 255, 255, 0.05) 50%, 
    rgba(255, 255, 255, 0.02) 100%);
  pointer-events: none;
  z-index: 1;
}

/* 高光效果层 */
.glass-card__highlight {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1rpx;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(255, 255, 255, 0.2) 50%, 
    transparent 100%);
  pointer-events: none;
  z-index: 2;
}

/* 内容区域 */
.glass-card__content {
  position: relative;
  z-index: 3;
  padding: var(--spacing-lg);
}

/* 可点击状态 */
.glass-card--clickable {
  cursor: pointer;
  transition: all var(--transition-normal);
}

.glass-card--clickable:active {
  transform: scale(0.98);
  box-shadow: 
    0 8rpx 32rpx rgba(0, 0, 0, 0.4),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.15);
}

/* 悬浮状态（更高的阴影） */
.glass-card--elevated {
  box-shadow: 
    0 24rpx 80rpx rgba(0, 0, 0, 0.4),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.12);
}

/* 加载状态 */
.glass-card__loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(23, 25, 35, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  border-radius: var(--radius-md);
}

/* 加载动画 */
.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 3rpx solid rgba(255, 255, 255, 0.2);
  border-top: 3rpx solid var(--accent-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 不同尺寸变体 */
.glass-card--sm .glass-card__content {
  padding: var(--spacing-sm);
}

.glass-card--lg .glass-card__content {
  padding: var(--spacing-xl);
}

.glass-card--xl .glass-card__content {
  padding: var(--spacing-xxl);
}

/* 不同圆角变体 */
.glass-card--rounded-sm {
  border-radius: var(--radius-sm);
}

.glass-card--rounded-lg {
  border-radius: var(--radius-lg);
}

.glass-card--rounded-xl {
  border-radius: var(--radius-xl);
}

/* 颜色变体 */
.glass-card--primary {
  background: rgba(109, 74, 255, 0.15);
  border-color: rgba(109, 74, 255, 0.3);
}

.glass-card--success {
  background: rgba(46, 213, 115, 0.15);
  border-color: rgba(46, 213, 115, 0.3);
}

.glass-card--warning {
  background: rgba(255, 165, 2, 0.15);
  border-color: rgba(255, 165, 2, 0.3);
}

.glass-card--error {
  background: rgba(255, 71, 87, 0.15);
  border-color: rgba(255, 71, 87, 0.3);
}

/* 无边框变体 */
.glass-card--borderless {
  border: none;
}

/* 透明变体 */
.glass-card--transparent {
  background: rgba(23, 25, 35, 0.3);
}

/* 实心变体 */
.glass-card--solid {
  background: var(--bg-card);
  box-shadow: var(--shadow-card);
}

/* 响应式适配 */
@media (max-width: 375px) {
  .glass-card__content {
    padding: var(--spacing-md);
  }
  
  .glass-card--sm .glass-card__content {
    padding: var(--spacing-xs);
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .glass-card {
    background: var(--glass-bg);
    border-color: var(--glass-border);
  }
}

/* 高对比度模式适配 */
@media (prefers-contrast: high) {
  .glass-card {
    border-width: 2rpx;
    border-color: rgba(255, 255, 255, 0.3);
  }
}
