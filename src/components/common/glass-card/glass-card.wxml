<!--
  Vault<PERSON>eeper 玻璃拟态卡片组件
  核心设计元素，完美还原原型中的玻璃效果
-->

<view 
  class="glass-card {{customClass}} {{clickable ? 'glass-card--clickable' : ''}} {{elevated ? 'glass-card--elevated' : ''}}"
  style="{{customStyle}}"
  bind:tap="handleTap"
>
  <!-- 玻璃效果背景层 -->
  <view class="glass-card__background"></view>
  
  <!-- 高光效果层 -->
  <view class="glass-card__highlight"></view>
  
  <!-- 内容区域 -->
  <view class="glass-card__content" style="padding: {{padding}};">
    <slot></slot>
  </view>
  
  <!-- 可选的加载状态 -->
  <view wx:if="{{loading}}" class="glass-card__loading">
    <view class="loading-spinner"></view>
  </view>
</view>
