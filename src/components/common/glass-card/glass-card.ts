/**
 * VaultKeeper 玻璃拟态卡片组件
 * 核心设计元素，提供统一的卡片容器
 * 
 * 功能特性：
 * - 完美还原原型玻璃拟态效果
 * - 支持点击交互和加载状态
 * - 多种尺寸和颜色变体
 * - 响应式设计适配
 * - 无障碍访问支持
 */

import { defineComponent } from '@vue-mini/core'

/**
 * 卡片尺寸类型
 */
type CardSize = 'sm' | 'md' | 'lg' | 'xl'

/**
 * 卡片颜色变体类型
 */
type CardVariant = 'default' | 'primary' | 'success' | 'warning' | 'error' | 'transparent' | 'solid'

/**
 * 圆角大小类型
 */
type BorderRadius = 'sm' | 'md' | 'lg' | 'xl'

defineComponent({
  /**
   * 组件属性定义
   */
  properties: {
    // 基础属性
    customClass: {
      type: String,
      value: ''
    },
    customStyle: {
      type: String,
      value: ''
    },
    
    // 交互属性
    clickable: {
      type: Boolean,
      value: false
    },
    disabled: {
      type: Boolean,
      value: false
    },
    loading: {
      type: Boolean,
      value: false
    },
    
    // 样式属性
    size: {
      type: String as PropType<CardSize>,
      value: 'md'
    },
    variant: {
      type: String as PropType<CardVariant>,
      value: 'default'
    },
    borderRadius: {
      type: String as PropType<BorderRadius>,
      value: 'md'
    },
    elevated: {
      type: Boolean,
      value: false
    },
    borderless: {
      type: Boolean,
      value: false
    },
    
    // 内容属性
    padding: {
      type: String,
      value: ''
    },
    
    // 无障碍属性
    ariaLabel: {
      type: String,
      value: ''
    },
    role: {
      type: String,
      value: ''
    }
  },

  /**
   * 事件定义
   */
  emits: {
    tap: (_event: WechatMiniprogram.TouchEvent) => true,
    longpress: (_event: WechatMiniprogram.TouchEvent) => true,
    touchstart: (_event: WechatMiniprogram.TouchEvent) => true,
    touchend: (_event: WechatMiniprogram.TouchEvent) => true
  },

  /**
   * 组件设置
   */
  setup(props, { emit }) {
    console.log('🎨 GlassCard 组件初始化:', props)

    /**
     * 处理点击事件
     * @param event 触摸事件
     */
    const handleTap = (event: WechatMiniprogram.TouchEvent) => {
      // 如果禁用或加载中，不处理点击
      if (props.disabled || props.loading) {
        return
      }

      // 如果不可点击，不处理点击
      if (!props.clickable) {
        return
      }

      console.log('👆 GlassCard 点击事件:', event)
      
      // 触发点击事件
      emit('tap', event)
      
      // 触觉反馈（小程序特有）
      if (wx.vibrateShort) {
        wx.vibrateShort({
          type: 'light'
        })
      }
    }

    /**
     * 处理长按事件
     * @param event 触摸事件
     */
    const handleLongPress = (event: WechatMiniprogram.TouchEvent) => {
      if (props.disabled || props.loading) {
        return
      }

      console.log('👆 GlassCard 长按事件:', event)
      emit('longpress', event)
      
      // 长按触觉反馈
      if (wx.vibrateShort) {
        wx.vibrateShort({
          type: 'medium'
        })
      }
    }

    /**
     * 处理触摸开始事件
     * @param event 触摸事件
     */
    const handleTouchStart = (event: WechatMiniprogram.TouchEvent) => {
      if (props.disabled || props.loading) {
        return
      }

      emit('touchstart', event)
    }

    /**
     * 处理触摸结束事件
     * @param event 触摸事件
     */
    const handleTouchEnd = (event: WechatMiniprogram.TouchEvent) => {
      if (props.disabled || props.loading) {
        return
      }

      emit('touchend', event)
    }

    /**
     * 获取组件完整的 CSS 类名
     */
    const getCardClasses = () => {
      const classes = ['glass-card']
      
      // 尺寸类
      if (props.size !== 'md') {
        classes.push(`glass-card--${props.size}`)
      }
      
      // 变体类
      if (props.variant !== 'default') {
        classes.push(`glass-card--${props.variant}`)
      }
      
      // 圆角类
      if (props.borderRadius !== 'md') {
        classes.push(`glass-card--rounded-${props.borderRadius}`)
      }
      
      // 状态类
      if (props.clickable) {
        classes.push('glass-card--clickable')
      }
      
      if (props.elevated) {
        classes.push('glass-card--elevated')
      }
      
      if (props.borderless) {
        classes.push('glass-card--borderless')
      }
      
      if (props.disabled) {
        classes.push('glass-card--disabled')
      }
      
      if (props.loading) {
        classes.push('glass-card--loading')
      }
      
      // 自定义类
      if (props.customClass) {
        classes.push(props.customClass)
      }
      
      return classes.join(' ')
    }

    /**
     * 获取内容区域的内边距
     */
    const getContentPadding = () => {
      if (props.padding) {
        return props.padding
      }
      
      // 根据尺寸返回默认内边距
      const paddingMap = {
        sm: 'var(--spacing-sm)',
        md: 'var(--spacing-lg)',
        lg: 'var(--spacing-xl)',
        xl: 'var(--spacing-xxl)'
      }
      
      return paddingMap[props.size] || paddingMap.md
    }

    /**
     * 获取无障碍属性
     */
    const getAccessibilityProps = () => {
      const accessibilityProps: Record<string, any> = {}
      
      if (props.ariaLabel) {
        accessibilityProps['aria-label'] = props.ariaLabel
      }
      
      if (props.role) {
        accessibilityProps.role = props.role
      }
      
      if (props.clickable) {
        accessibilityProps.role = accessibilityProps.role || 'button'
        accessibilityProps.tabindex = props.disabled ? -1 : 0
      }
      
      return accessibilityProps
    }

    // 返回组件方法和计算属性
    return {
      handleTap,
      handleLongPress,
      handleTouchStart,
      handleTouchEnd,
      getCardClasses,
      getContentPadding,
      getAccessibilityProps
    }
  }
})
