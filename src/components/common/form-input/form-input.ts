/**
 * VaultKeeper 表单输入框组件
 * 完美还原原型设计，支持密码显示/隐藏和多种验证状态
 * 
 * 功能特性：
 * - 多种输入类型支持（text, password, number, email等）
 * - 密码显示/隐藏切换
 * - 实时验证和错误提示
 * - 清除按钮和字符计数
 * - 多种尺寸和样式变体
 * - 无障碍访问支持
 */

import { defineComponent, ref } from '@vue-mini/core'

/**
 * 输入框类型
 */
type InputType = 'text' | 'number' | 'idcard' | 'digit' | 'password' | 'email' | 'url' | 'search'

/**
 * 输入框尺寸
 */
type InputSize = 'sm' | 'md' | 'lg'

/**
 * 输入框变体
 */
type InputVariant = 'default' | 'filled' | 'outlined' | 'borderless'

/**
 * 输入框状态
 */
type InputStatus = 'default' | 'success' | 'warning' | 'error'

/**
 * 确认按钮类型
 */
type ConfirmType = 'send' | 'search' | 'next' | 'go' | 'done'

defineComponent({
  /**
   * 组件属性定义
   */
  properties: {
    // 基础属性
    customClass: {
      type: String,
      value: ''
    },
    customStyle: {
      type: String,
      value: ''
    },

    // 输入框属性
    value: {
      type: String,
      value: '',
      observer: function (newVal: any, oldVal: any) {
        // 确保值是字符串类型，处理响应式数据传递的情况
        if (typeof newVal !== 'string') {
          console.warn('⚠️ form-input value 应该是字符串类型，收到:', typeof newVal, newVal)

          let stringValue = ''

          // 如果是响应式对象（ref），尝试获取其值
          if (newVal && typeof newVal === 'object' && 'value' in newVal) {
            stringValue = String(newVal.value || '')
            console.log('🔄 检测到响应式数据，提取值:', stringValue)
          } else if (newVal !== null && newVal !== undefined) {
            // 强制转换为字符串
            stringValue = String(newVal)
          }

          // 更新组件数据
          this.setData({
            value: stringValue
          })
        }
      }
    },
    type: {
      type: String,
      value: 'text'
    },
    placeholder: {
      type: String,
      value: ''
    },
    placeholderStyle: {
      type: String,
      value: ''
    },

    // 标签和帮助文本
    label: {
      type: String,
      value: ''
    },
    helpText: {
      type: String,
      value: ''
    },
    errorText: {
      type: String,
      value: '',
    },

    // 样式属性
    size: {
      type: String as PropType<InputSize>,
      value: 'md'
    },
    variant: {
      type: String as PropType<InputVariant>,
      value: 'default'
    },
    status: {
      type: String as PropType<InputStatus>,
      value: 'default'
    },

    // 图标
    leftIcon: {
      type: String,
      value: ''
    },
    rightIcon: {
      type: String,
      value: ''
    },

    // 功能属性
    required: {
      type: Boolean,
      value: false
    },
    disabled: {
      type: Boolean,
      value: false
    },
    readonly: {
      type: Boolean,
      value: false
    },
    clearable: {
      type: Boolean,
      value: false
    },
    showCount: {
      type: Boolean,
      value: false
    },

    // 输入限制
    maxlength: {
      type: Number,
      value: 140
    },

    // 小程序原生属性
    focus: {
      type: Boolean,
      value: false
    },
    confirmType: {
      type: String as PropType<ConfirmType>,
      value: 'done'
    },
    confirmHold: {
      type: Boolean,
      value: false
    },
    cursor: {
      type: Number,
      value: -1
    },
    selectionStart: {
      type: Number,
      value: -1
    },
    selectionEnd: {
      type: Number,
      value: -1
    },
    adjustPosition: {
      type: Boolean,
      value: true
    },
    holdKeyboard: {
      type: Boolean,
      value: false
    },

    // 安全键盘属性
    safePasswordCertPath: {
      type: String,
      value: ''
    },
    safePasswordLength: {
      type: Number,
      value: 0
    },
    safePasswordTimeStamp: {
      type: Number,
      value: 0
    },
    safePasswordNonce: {
      type: String,
      value: ''
    },
    safePasswordSalt: {
      type: String,
      value: ''
    },
    safePasswordCustomHash: {
      type: String,
      value: ''
    }
  },

  /**
   * 事件定义
   */
  emits: {
    input: (_event: WechatMiniprogram.Input) => true,
    focus: (_event: WechatMiniprogram.InputFocus) => true,
    blur: (_event: WechatMiniprogram.InputBlur) => true,
    confirm: (_event: WechatMiniprogram.InputConfirm) => true,
    clear: () => true,
    keyboardheightchange: (_event: WechatMiniprogram.InputKeyboardHeightChange) => true
  },

  /**
   * 组件设置
   */
  setup(props, { emit }) {
    console.log('📝 FormInput 组件初始化:', props)

    // 响应式数据
    const inputId = ref('')
    const passwordVisible = ref(false)
    const focused = ref(false)
    const currentType = ref('text')

    // 组件挂载时初始化
    attached(() => {
      // 生成唯一ID
      inputId.value = 'input_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
      currentType.value = props.type
    })

    /**
     * 处理输入事件
     * @param event 输入事件
     */
    const handleInput = (event: WechatMiniprogram.Input) => {
      const value = event.detail.value
      console.log('📝 输入内容:', value)
      emit('input', event)
    }

    /**
     * 处理聚焦事件
     * @param event 聚焦事件
     */
    const handleFocus = (event: WechatMiniprogram.InputFocus) => {
      console.log('👁️ 输入框聚焦')

      // 更新聚焦状态
      focused.value = true

      emit('focus', event)
    }

    /**
     * 处理失焦事件
     * @param event 失焦事件
     */
    const handleBlur = (event: WechatMiniprogram.InputBlur) => {
      console.log('👁️ 输入框失焦')

      // 更新聚焦状态
      focused.value = false

      emit('blur', event)
    }

    /**
     * 处理确认事件
     * @param event 确认事件
     */
    const handleConfirm = (event: WechatMiniprogram.InputConfirm) => {
      console.log('✅ 输入确认:', event.detail.value)

      emit('confirm', event)
    }

    /**
     * 处理键盘高度变化
     * @param event 键盘高度变化事件
     */
    const handleKeyboardHeightChange = (event: WechatMiniprogram.InputKeyboardHeightChange) => {
      console.log('⌨️ 键盘高度变化:', event.detail.height)

      emit('keyboardheightchange', event)
    }

    /**
     * 切换密码显示/隐藏
     */
    const togglePasswordVisibility = () => {
      const newVisible = !passwordVisible.value
      const newType = newVisible ? 'text' : 'password'

      console.log('👁️ 切换密码显示:', newVisible)

      passwordVisible.value = newVisible
      currentType.value = newType

      // 触觉反馈
      if (wx.vibrateShort) {
        wx.vibrateShort({
          type: 'light'
        })
      }
    }

    /**
     * 清除输入内容
     */
    const handleClear = () => {
      console.log('🗑️ 清除输入内容')

      // 触发清除事件
      emit('clear')

      // 触觉反馈
      if (wx.vibrateShort) {
        wx.vibrateShort({
          type: 'light'
        })
      }
    }

    /**
     * 获取输入框完整的 CSS 类名
     */
    const getInputClasses = () => {
      const classes = ['form-input']

      // 尺寸类
      if (props.size !== 'md') {
        classes.push(`form-input--${props.size}`)
      }

      // 变体类
      if (props.variant !== 'default') {
        classes.push(`form-input--${props.variant}`)
      }

      // 状态类
      if (props.status !== 'default') {
        classes.push(`form-input--${props.status}`)
      }

      // 聚焦状态
      if (focused.value) {
        classes.push('form-input--focused')
      }

      // 禁用状态
      if (props.disabled) {
        classes.push('form-input--disabled')
      }

      // 只读状态
      if (props.readonly) {
        classes.push('form-input--readonly')
      }

      // 错误状态
      if (props.errorText) {
        classes.push('form-input--error')
      }

      // 类型特定类
      if (props.type === 'password') {
        classes.push('form-input--password')
      } else if (props.type === 'search') {
        classes.push('form-input--search')
      } else if (props.type === 'number' || props.type === 'digit') {
        classes.push('form-input--number')
      }

      return classes.join(' ')
    }

    /**
     * 获取字符计数的 CSS 类名
     */
    const getCountClasses = () => {
      const classes = ['form-input__count-text']

      // 检查是否超过80%的字符限制
      const currentLength = (props.value || '').length
      const maxLength = props.maxlength || 140

      if (currentLength > maxLength * 0.8) {
        classes.push('form-input__count--warning')
      }

      return classes.join(' ')
    }

    // 返回响应式数据和方法
    return {
      // 响应式数据
      inputId,
      passwordVisible,
      focused,
      currentType,

      // 方法
      handleInput,
      handleFocus,
      handleBlur,
      handleConfirm,
      handleKeyboardHeightChange,
      togglePasswordVisibility,
      handleClear,
      getInputClasses,
      getCountClasses
    }
  }
})
