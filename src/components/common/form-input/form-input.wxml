<!--
  Vault<PERSON>eeper 表单输入框组件
  完美还原原型中的输入框设计，支持密码显示/隐藏
-->

<view class="form-input-wrapper {{customClass}}">
  <!-- 标签 -->
  <label wx:if="{{label}}" class="form-input__label" for="{{inputId}}">
    {{label}}
    <text wx:if="{{required}}" class="form-input__required">*</text>
  </label>
  
  <!-- 输入框容器 -->
  <view class="form-input {{getInputClasses()}}" style="{{customStyle}}">
    <!-- 左侧图标 -->
    <view wx:if="{{leftIcon}}" class="form-input__icon form-input__icon--left">
      <text class="iconfont icon-{{leftIcon}}"></text>
    </view>
    
    <!-- 输入框 -->
    <input
      id="{{inputId}}"
      class="form-input__field"
      type="{{currentType}}"
      value="{{value || ''}}"
      placeholder="{{placeholder}}"
      placeholder-style="{{placeholderStyle}}"
      disabled="{{disabled}}"
      maxlength="{{maxlength}}"
      focus="{{focus}}"
      confirm-type="{{confirmType}}"
      confirm-hold="{{confirmHold}}"
      cursor="{{cursor}}"
      selection-start="{{selectionStart}}"
      selection-end="{{selectionEnd}}"
      adjust-position="{{adjustPosition}}"
      hold-keyboard="{{holdKeyboard}}"
      safe-password-cert-path="{{safePasswordCertPath}}"
      safe-password-length="{{safePasswordLength}}"
      safe-password-time-stamp="{{safePasswordTimeStamp}}"
      safe-password-nonce="{{safePasswordNonce}}"
      safe-password-salt="{{safePasswordSalt}}"
      safe-password-custom-hash="{{safePasswordCustomHash}}"
      bind:input="handleInput"
      bind:focus="handleFocus"
      bind:blur="handleBlur"
      bind:confirm="handleConfirm"
      bind:keyboardheightchange="handleKeyboardHeightChange"
    />
    
    <!-- 密码显示/隐藏按钮 -->
    <view 
      wx:if="{{type === 'password'}}" 
      class="form-input__icon form-input__icon--right form-input__password-toggle"
      bind:tap="togglePasswordVisibility"
    >
      <text class="iconfont icon-{{passwordVisible ? 'eye-off' : 'eye'}}"></text>
    </view>
    
    <!-- 右侧图标 -->
    <view wx:elif="{{rightIcon}}" class="form-input__icon form-input__icon--right">
      <text class="iconfont icon-{{rightIcon}}"></text>
    </view>
    
    <!-- 清除按钮 -->
    <view 
      wx:if="{{clearable && value && !disabled}}" 
      class="form-input__icon form-input__icon--right form-input__clear"
      bind:tap="handleClear"
    >
      <text class="iconfont icon-close-circle"></text>
    </view>
    
    <!-- 输入框边框效果 -->
    <view class="form-input__border"></view>
  </view>
  
  <!-- 帮助文本 -->
  <view wx:if="{{helpText || errorText}}" class="form-input__help">
    <text wx:if="{{errorText}}" class="form-input__error">{{errorText || ''}}</text>
    <text wx:elif="{{helpText}}" class="form-input__help-text">{{helpText || ''}}</text>
  </view>
  
  <!-- 字符计数 -->
  <view wx:if="{{showCount && maxlength}}" class="form-input__count">
    <text class="{{getCountClasses()}}">
      <!-- {{(value || '').length}}/{{maxlength}} -->
    </text>
  </view>
</view>
