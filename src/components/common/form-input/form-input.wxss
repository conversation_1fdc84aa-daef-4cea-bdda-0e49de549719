/**
 * Vault<PERSON>eeper 表单输入框组件样式
 * 完美还原原型设计，支持多种状态和交互
 */

.form-input-wrapper {
  position: relative;
  margin-bottom: var(--form-item-margin);
}

/* 标签样式 */
.form-input__label {
  display: block;
  margin-bottom: var(--form-label-margin);
  font-size: var(--font-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
  line-height: var(--line-height-normal);
}

.form-input__required {
  color: var(--error-color);
  margin-left: 4rpx;
}

/* 输入框容器 */
.form-input {
  position: relative;
  display: flex;
  align-items: center;
  background: rgba(40, 44, 58, 0.7);
  border: 1rpx solid var(--glass-border);
  border-radius: var(--radius-sm);
  transition: var(--transition-all);
  overflow: hidden;
}

/* 输入框字段 */
.form-input__field {
  flex: 1;
  padding: var(--form-input-padding);
  background: transparent;
  border: none;
  outline: none;
  font-size: var(--font-base);
  color: var(--text-primary);
  line-height: var(--line-height-normal);
}

.form-input__field::placeholder {
  color: var(--text-muted);
}

/* 图标样式 */
.form-input__icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  height: 100%;
  font-size: var(--font-lg);
  color: var(--text-muted);
  transition: var(--transition-colors);
}

.form-input__icon--left {
  padding-left: var(--spacing-sm);
}

.form-input__icon--right {
  padding-right: var(--spacing-sm);
}

/* 密码切换按钮 */
.form-input__password-toggle {
  cursor: pointer;
  color: var(--text-secondary);
  transition: var(--transition-colors);
}

.form-input__password-toggle:active {
  color: var(--accent-color);
  transform: scale(0.95);
}

/* 清除按钮 */
.form-input__clear {
  cursor: pointer;
  color: var(--text-muted);
  transition: var(--transition-colors);
}

.form-input__clear:active {
  color: var(--text-secondary);
  transform: scale(0.95);
}

/* 边框效果 */
.form-input__border {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2rpx;
  background: var(--accent-color);
  transform: scaleX(0);
  transition: transform var(--transition-normal);
}

/* 帮助文本 */
.form-input__help {
  margin-top: var(--spacing-xs);
  font-size: var(--font-xs);
  line-height: var(--line-height-normal);
}

.form-input__help-text {
  color: var(--text-muted);
}

.form-input__error {
  color: var(--error-color);
}

/* 字符计数 */
.form-input__count {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: var(--spacing-xs);
  font-size: var(--font-xs);
  color: var(--text-muted);
}

.form-input__count-text {
  color: var(--text-muted);
}

.form-input__count--warning {
  color: var(--warning-color);
}

/* ==================== 输入框状态 ==================== */

/* 聚焦状态 */
.form-input--focused {
  border-color: var(--accent-color);
  box-shadow: 0 0 0 2rpx rgba(109, 74, 255, 0.2);
}

.form-input--focused .form-input__border {
  transform: scaleX(1);
}

.form-input--focused .form-input__icon {
  color: var(--accent-color);
}

/* 错误状态 */
.form-input--error {
  border-color: var(--error-color);
  box-shadow: 0 0 0 2rpx rgba(255, 71, 87, 0.2);
}

.form-input--error .form-input__border {
  background: var(--error-color);
  transform: scaleX(1);
}

.form-input--error .form-input__icon {
  color: var(--error-color);
}

/* 成功状态 */
.form-input--success {
  border-color: var(--success-color);
}

.form-input--success .form-input__border {
  background: var(--success-color);
  transform: scaleX(1);
}

.form-input--success .form-input__icon {
  color: var(--success-color);
}

/* 禁用状态 */
.form-input--disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: rgba(40, 44, 58, 0.3);
}

.form-input--disabled .form-input__field {
  cursor: not-allowed;
  color: var(--text-disabled);
}

.form-input--disabled .form-input__icon {
  color: var(--text-disabled);
}

/* 只读状态 */
.form-input--readonly {
  background: rgba(40, 44, 58, 0.3);
  cursor: default;
}

.form-input--readonly .form-input__field {
  cursor: default;
}

/* ==================== 输入框尺寸 ==================== */

/* 小尺寸 */
.form-input--sm .form-input__field {
  padding: 16rpx 24rpx;
  font-size: var(--font-sm);
}

.form-input--sm .form-input__icon {
  width: 64rpx;
  font-size: var(--font-base);
}

/* 大尺寸 */
.form-input--lg .form-input__field {
  padding: 32rpx 40rpx;
  font-size: var(--font-lg);
}

.form-input--lg .form-input__icon {
  width: 96rpx;
  font-size: var(--font-xl);
}

/* ==================== 输入框变体 ==================== */

/* 无边框变体 */
.form-input--borderless {
  border: none;
  background: transparent;
}

.form-input--borderless .form-input__border {
  display: block;
}

/* 填充变体 */
.form-input--filled {
  background: var(--bg-tertiary);
  border: 1rpx solid transparent;
}

.form-input--filled:hover {
  background: rgba(42, 45, 58, 0.8);
}

/* 轮廓变体 */
.form-input--outlined {
  background: transparent;
  border: 2rpx solid var(--border-color);
}

.form-input--outlined.form-input--focused {
  border-color: var(--accent-color);
  box-shadow: none;
}

/* ==================== 特殊输入框类型 ==================== */

/* 密码输入框 */
.form-input--password .form-input__field {
  font-family: var(--font-family-mono);
  letter-spacing: var(--password-letter-spacing);
}

/* 搜索输入框 */
.form-input--search {
  border-radius: var(--radius-round);
}

.form-input--search .form-input__field {
  text-align: center;
}

/* 数字输入框 */
.form-input--number .form-input__field {
  text-align: right;
  font-family: var(--font-family-mono);
}

/* ==================== 响应式适配 ==================== */

@media (max-width: 375px) {
  .form-input__field {
    padding: 20rpx 28rpx;
    font-size: var(--font-sm);
  }
  
  .form-input__icon {
    width: 72rpx;
    font-size: var(--font-base);
  }
}

/* ==================== 动画效果 ==================== */

.form-input__field:focus {
  animation: inputFocus 0.2s ease-out;
}

@keyframes inputFocus {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
  100% {
    transform: scale(1);
  }
}

/* 错误抖动动画 */
.form-input--error {
  animation: inputError 0.3s ease-in-out;
}

@keyframes inputError {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-4rpx);
  }
  75% {
    transform: translateX(4rpx);
  }
}
