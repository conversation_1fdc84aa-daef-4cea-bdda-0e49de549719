/**
 * Vault<PERSON>eeper 模态框组件样式
 * 完美还原原型设计，支持多种尺寸和动画效果
 */

.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: var(--z-index-modal);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-lg);
}

/* 遮罩层 */
.modal__mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--bg-overlay);
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.modal__mask--visible {
  opacity: 1;
}

/* 模态框容器 */
.modal__container {
  position: relative;
  z-index: 1;
  width: 100%;
  max-width: var(--modal-max-width);
  max-height: 80vh;
  transform: scale(0.9) translateY(60rpx);
  opacity: 0;
  transition: all var(--transition-normal) var(--ease-smooth);
}

.modal__container--visible {
  transform: scale(1) translateY(0);
  opacity: 1;
}

/* 模态框内容 */
.modal__content {
  position: relative;
  background: var(--glass-bg);
  border: 1rpx solid var(--glass-border);
  border-radius: var(--modal-radius);
  box-shadow: var(--shadow-modal);
  overflow: hidden;
  
  /* 玻璃拟态效果 */
  backdrop-filter: blur(20rpx);
  -webkit-backdrop-filter: blur(20rpx);
}

.modal__content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.1) 0%, 
    rgba(255, 255, 255, 0.05) 50%, 
    rgba(255, 255, 255, 0.02) 100%);
  pointer-events: none;
  z-index: 1;
}

/* 关闭按钮 */
.modal__close {
  position: absolute;
  top: var(--spacing-md);
  right: var(--spacing-md);
  z-index: 10;
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  color: var(--text-secondary);
  font-size: var(--font-lg);
  cursor: pointer;
  transition: var(--transition-all);
}

.modal__close:active {
  background: rgba(255, 255, 255, 0.2);
  color: var(--text-primary);
  transform: scale(0.95);
}

/* 头部 */
.modal__header {
  position: relative;
  z-index: 2;
  padding: var(--modal-padding);
  padding-bottom: 0;
  text-align: center;
}

.modal__title {
  margin-bottom: var(--spacing-sm);
}

.modal__title-text {
  display: block;
  font-size: var(--font-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  line-height: var(--line-height-tight);
}

.modal__subtitle {
  display: block;
  margin-top: var(--spacing-xs);
  font-size: var(--font-sm);
  color: var(--text-secondary);
  line-height: var(--line-height-normal);
}

/* 主体内容 */
.modal__body {
  position: relative;
  z-index: 2;
  padding: var(--modal-padding);
  max-height: 60vh;
  overflow-y: auto;
  color: var(--text-primary);
  line-height: var(--line-height-normal);
}

/* 底部 */
.modal__footer {
  position: relative;
  z-index: 2;
  padding: var(--modal-padding);
  padding-top: 0;
  border-top: 1rpx solid var(--glass-border);
  background: rgba(255, 255, 255, 0.02);
}

.modal__actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: flex-end;
}

.modal__button {
  flex: 1;
  height: 80rpx;
  border: none;
  border-radius: var(--radius-sm);
  font-size: var(--font-base);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: var(--transition-all);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
}

.modal__button--cancel {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-secondary);
  border: 1rpx solid var(--glass-border);
}

.modal__button--cancel:active {
  background: rgba(255, 255, 255, 0.15);
  color: var(--text-primary);
  transform: scale(0.98);
}

.modal__button--confirm {
  background: var(--accent-color);
  color: var(--text-white);
  box-shadow: 0 8rpx 24rpx rgba(109, 74, 255, 0.3);
}

.modal__button--confirm:active {
  background: var(--secondary-accent);
  transform: scale(0.98);
}

.modal__button--confirm.modal__button--disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

/* 加载动画 */
.loading-spinner {
  width: 32rpx;
  height: 32rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-top: 2rpx solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-spinner--sm {
  width: 24rpx;
  height: 24rpx;
}

/* ==================== 模态框尺寸变体 ==================== */

/* 小尺寸 */
.modal__content--sm {
  max-width: 480rpx;
}

.modal__content--sm .modal__header,
.modal__content--sm .modal__body,
.modal__content--sm .modal__footer {
  padding: var(--spacing-lg);
}

/* 大尺寸 */
.modal__content--lg {
  max-width: 800rpx;
}

.modal__content--lg .modal__header,
.modal__content--lg .modal__body,
.modal__content--lg .modal__footer {
  padding: var(--spacing-xxl);
}

/* 全屏 */
.modal__content--fullscreen {
  width: 100vw;
  height: 100vh;
  max-width: none;
  max-height: none;
  border-radius: 0;
}

/* ==================== 模态框位置变体 ==================== */

/* 顶部 */
.modal--top .modal__container {
  align-self: flex-start;
  margin-top: 20vh;
  transform: scale(0.9) translateY(-60rpx);
}

.modal--top .modal__container--visible {
  transform: scale(1) translateY(0);
}

/* 底部 */
.modal--bottom .modal__container {
  align-self: flex-end;
  margin-bottom: 0;
  transform: scale(0.9) translateY(60rpx);
}

.modal--bottom .modal__container--visible {
  transform: scale(1) translateY(0);
}

/* ==================== 响应式适配 ==================== */

@media (max-width: 375px) {
  .modal {
    padding: var(--spacing-md);
  }
  
  .modal__content {
    border-radius: var(--radius-md);
  }
  
  .modal__header,
  .modal__body,
  .modal__footer {
    padding: var(--spacing-lg);
  }
  
  .modal__actions {
    flex-direction: column;
  }
  
  .modal__button {
    height: 88rpx;
  }
}

/* ==================== 动画效果 ==================== */

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 模态框进入动画 */
@keyframes modalEnter {
  0% {
    opacity: 0;
    transform: scale(0.9) translateY(60rpx);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 模态框退出动画 */
@keyframes modalExit {
  0% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
  100% {
    opacity: 0;
    transform: scale(0.9) translateY(60rpx);
  }
}

/* 遮罩进入动画 */
@keyframes maskEnter {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

/* 遮罩退出动画 */
@keyframes maskExit {
  0% { opacity: 1; }
  100% { opacity: 0; }
}
