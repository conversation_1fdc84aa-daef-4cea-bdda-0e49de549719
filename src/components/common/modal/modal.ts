/**
 * VaultKeeper 模态框组件
 * 完美还原原型设计，支持多种尺寸和动画效果
 * 
 * 功能特性：
 * - 多种尺寸和位置变体
 * - 平滑的进入/退出动画
 * - 可自定义头部、内容和底部
 * - 支持确认/取消操作
 * - 遮罩点击关闭
 * - 无障碍访问支持
 */

import { defineComponent, ref, watch } from '@vue-mini/core'

/**
 * 模态框尺寸类型
 */
type ModalSize = 'sm' | 'md' | 'lg' | 'fullscreen'

/**
 * 模态框位置类型
 */
type ModalPosition = 'center' | 'top' | 'bottom'

defineComponent({
  /**
   * 组件属性定义
   */
  properties: {
    // 基础属性
    customClass: {
      type: String,
      value: ''
    },
    
    // 显示控制
    visible: {
      type: Boolean,
      value: false
    },
    
    // 内容属性
    title: {
      type: String,
      value: ''
    },
    subtitle: {
      type: String,
      value: ''
    },
    
    // 样式属性
    size: {
      type: String as PropType<ModalSize>,
      value: 'md'
    },
    position: {
      type: String as PropType<ModalPosition>,
      value: 'center'
    },
    
    // 交互属性
    closable: {
      type: Boolean,
      value: true
    },
    maskClosable: {
      type: Boolean,
      value: true
    },
    
    // 底部按钮
    showFooter: {
      type: Boolean,
      value: true
    },
    showCancel: {
      type: Boolean,
      value: true
    },
    showConfirm: {
      type: Boolean,
      value: true
    },
    cancelText: {
      type: String,
      value: '取消'
    },
    confirmText: {
      type: String,
      value: '确定'
    },
    confirmLoading: {
      type: Boolean,
      value: false
    },
    
    // 样式定制
    zIndex: {
      type: Number,
      value: 1000
    },
    maskStyle: {
      type: String,
      value: ''
    },
    contentStyle: {
      type: String,
      value: ''
    }
  },

  /**
   * 事件定义
   */
  emits: {
    close: () => true,
    cancel: () => true,
    confirm: () => true,
    maskTap: () => true
  },

  /**
   * 组件设置
   */
  setup(props, { emit }) {
    console.log('🔲 Modal 组件初始化:', props)

    // 响应式数据
    const maskVisible = ref(false)
    const containerVisible = ref(false)
    const animating = ref(false)

    // 监听 visible 属性变化
    watch(() => props.visible, (newVal: boolean) => {
      if (newVal) {
        show()
      } else {
        hide()
      }
    })

    /**
     * 显示模态框
     */
    const show = () => {
      if (animating.value) return

      console.log('📱 显示模态框')
      animating.value = true

      // 先显示遮罩
      maskVisible.value = true

      // 延迟显示容器以实现动画效果
      setTimeout(() => {
        containerVisible.value = true
        animating.value = false
      }, 50)
    }

    /**
     * 隐藏模态框
     */
    const hide = () => {
      if (animating.value) return

      console.log('📱 隐藏模态框')
      animating.value = true

      // 先隐藏容器
      containerVisible.value = false

      // 延迟隐藏遮罩
      setTimeout(() => {
        maskVisible.value = false
        animating.value = false
      }, 200)
    }

    /**
     * 处理遮罩点击
     */
    const handleMaskTap = () => {
      console.log('👆 点击遮罩')
      
      emit('maskTap')
      
      if (props.maskClosable) {
        handleClose()
      }
    }

    /**
     * 处理容器点击（阻止冒泡）
     */
    const handleContainerTap = () => {
      // 阻止事件冒泡到遮罩
    }

    /**
     * 处理关闭按钮点击
     */
    const handleClose = () => {
      console.log('❌ 关闭模态框')
      
      emit('close')
      
      // 触觉反馈
      if (wx.vibrateShort) {
        wx.vibrateShort({
          type: 'light'
        })
      }
    }

    /**
     * 处理取消按钮点击
     */
    const handleCancel = () => {
      console.log('🚫 取消操作')
      
      emit('cancel')
      
      // 触觉反馈
      if (wx.vibrateShort) {
        wx.vibrateShort({
          type: 'light'
        })
      }
    }

    /**
     * 处理确认按钮点击
     */
    const handleConfirm = () => {
      if (props.confirmLoading) {
        return
      }
      
      console.log('✅ 确认操作')
      
      emit('confirm')
      
      // 触觉反馈
      if (wx.vibrateShort) {
        wx.vibrateShort({
          type: 'medium'
        })
      }
    }

    /**
     * 获取内容区域的 CSS 类名
     */
    const getContentClasses = () => {
      const classes = ['modal__content']
      
      // 尺寸类
      if (props.size !== 'md') {
        classes.push(`modal__content--${props.size}`)
      }
      
      return classes.join(' ')
    }

    /**
     * 获取模态框的 CSS 类名
     */
    const getModalClasses = () => {
      const classes = ['modal']
      
      // 位置类
      if (props.position !== 'center') {
        classes.push(`modal--${props.position}`)
      }
      
      // 自定义类
      if (props.customClass) {
        classes.push(props.customClass)
      }
      
      return classes.join(' ')
    }

    // 返回响应式数据和方法
    return {
      // 响应式数据
      maskVisible,
      containerVisible,
      animating,

      // 方法
      show,
      hide,
      handleMaskTap,
      handleContainerTap,
      handleClose,
      handleCancel,
      handleConfirm,
      getContentClasses,
      getModalClasses
    }
  }
})
