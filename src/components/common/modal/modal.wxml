<!--
  Vault<PERSON>eeper 模态框组件
  完美还原原型中的模态框设计和动画效果
-->

<view 
  wx:if="{{visible}}" 
  class="modal {{customClass}}"
  style="z-index: {{zIndex}};"
  catch:tap="handleMaskTap"
>
  <!-- 遮罩层 -->
  <view 
    class="modal__mask {{maskVisible ? 'modal__mask--visible' : ''}}"
    style="{{maskStyle}}"
  ></view>
  
  <!-- 模态框容器 -->
  <view 
    class="modal__container {{containerVisible ? 'modal__container--visible' : ''}}"
    catch:tap="handleContainerTap"
  >
    <!-- 模态框内容 -->
    <view 
      class="modal__content {{getContentClasses()}}"
      style="{{contentStyle}}"
    >
      <!-- 关闭按钮 -->
      <view 
        wx:if="{{closable}}" 
        class="modal__close"
        bind:tap="handleClose"
      >
        <text class="iconfont icon-close"></text>
      </view>
      
      <!-- 头部 -->
      <view wx:if="{{title || $slots.header}}" class="modal__header">
        <slot wx:if="{{$slots.header}}" name="header"></slot>
        <view wx:else class="modal__title">
          <text class="modal__title-text">{{title}}</text>
          <text wx:if="{{subtitle}}" class="modal__subtitle">{{subtitle}}</text>
        </view>
      </view>
      
      <!-- 主体内容 -->
      <view class="modal__body">
        <slot></slot>
      </view>
      
      <!-- 底部 -->
      <view wx:if="{{$slots.footer || showFooter}}" class="modal__footer">
        <slot wx:if="{{$slots.footer}}" name="footer"></slot>
        <view wx:else class="modal__actions">
          <button 
            wx:if="{{showCancel}}"
            class="modal__button modal__button--cancel"
            bind:tap="handleCancel"
          >
            {{cancelText}}
          </button>
          <button 
            wx:if="{{showConfirm}}"
            class="modal__button modal__button--confirm"
            bind:tap="handleConfirm"
            disabled="{{confirmLoading}}"
          >
            <view wx:if="{{confirmLoading}}" class="loading-spinner loading-spinner--sm"></view>
            <text>{{confirmText}}</text>
          </button>
        </view>
      </view>
    </view>
  </view>
</view>
