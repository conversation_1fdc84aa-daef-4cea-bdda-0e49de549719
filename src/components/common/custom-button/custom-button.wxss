/**
 * VaultKeeper 自定义按钮组件样式
 * 完美还原原型设计，支持多种变体和状态
 */

.custom-button {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: var(--radius-sm);
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-base);
  line-height: 1;
  text-align: center;
  cursor: pointer;
  transition: var(--transition-all);
  overflow: hidden;
  user-select: none;
  outline: none;
  
  /* 重置小程序默认按钮样式 */
  background: none;
  padding: 0;
  margin: 0;
  
  /* 防止文本选择 */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* 按钮内容容器 */
.custom-button__content {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  padding: var(--button-padding-y) var(--button-padding-x);
}

/* 按钮文本 */
.custom-button__text {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 按钮图标 */
.custom-button__icon {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2em;
}

.custom-button__icon--left {
  margin-right: var(--spacing-xs);
}

.custom-button__icon--right {
  margin-left: var(--spacing-xs);
}

/* 按钮背景 */
.custom-button__background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  transition: var(--transition-all);
}

/* 加载状态 */
.custom-button__loading {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  padding: var(--button-padding-y) var(--button-padding-x);
}

.custom-button__loading-text {
  margin-left: var(--spacing-xs);
  font-size: 0.9em;
}

/* 加载动画 */
.loading-spinner {
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-top: 2rpx solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-spinner--sm {
  width: 24rpx;
  height: 24rpx;
}

.loading-spinner--md {
  width: 32rpx;
  height: 32rpx;
}

.loading-spinner--lg {
  width: 40rpx;
  height: 40rpx;
}

/* 波纹效果 */
.custom-button__ripple {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  pointer-events: none;
  z-index: 3;
}

/* ==================== 按钮变体 ==================== */

/* Primary 按钮 */
.custom-button--primary .custom-button__background {
  background: var(--accent-color);
  box-shadow: var(--shadow-button);
}

.custom-button--primary {
  color: var(--text-white);
}

.custom-button--primary:active .custom-button__background {
  background: var(--secondary-accent);
  transform: scale(0.98);
}

/* Secondary 按钮 */
.custom-button--secondary .custom-button__background {
  background: rgba(255, 255, 255, 0.1);
  border: 1rpx solid var(--glass-border);
}

.custom-button--secondary {
  color: var(--text-primary);
}

.custom-button--secondary:active .custom-button__background {
  background: rgba(255, 255, 255, 0.15);
  transform: scale(0.98);
}

/* Ghost 按钮 */
.custom-button--ghost .custom-button__background {
  background: transparent;
  border: 1rpx solid var(--accent-color);
}

.custom-button--ghost {
  color: var(--accent-color);
}

.custom-button--ghost:active .custom-button__background {
  background: rgba(109, 74, 255, 0.1);
}

/* Text 按钮 */
.custom-button--text .custom-button__background {
  background: transparent;
}

.custom-button--text {
  color: var(--accent-color);
}

.custom-button--text:active .custom-button__background {
  background: rgba(109, 74, 255, 0.1);
}

/* Danger 按钮 */
.custom-button--danger .custom-button__background {
  background: var(--error-color);
}

.custom-button--danger {
  color: var(--text-white);
}

.custom-button--danger:active .custom-button__background {
  background: var(--error-light);
  transform: scale(0.98);
}

/* Success 按钮 */
.custom-button--success .custom-button__background {
  background: var(--success-color);
  box-shadow: 0 4rpx 16rpx rgba(46, 213, 115, 0.3);
}

.custom-button--success {
  color: var(--text-white);
  font-weight: var(--font-weight-semibold);
}

.custom-button--success:active .custom-button__background {
  background: var(--success-light);
  transform: scale(0.96);
  box-shadow: 0 2rpx 8rpx rgba(46, 213, 115, 0.2);
}

/* ==================== 按钮尺寸 ==================== */

/* 小尺寸 */
.custom-button--sm {
  height: var(--button-height-sm);
  font-size: var(--font-sm);
  border-radius: var(--radius-xs);
}

.custom-button--sm .custom-button__content {
  padding: 12rpx 24rpx;
}

/* 中等尺寸（默认） */
.custom-button--md {
  height: var(--button-height-md);
  font-size: var(--font-base);
}

.custom-button--md .custom-button__content {
  padding: var(--button-padding-y) var(--button-padding-x);
}

/* 大尺寸 */
.custom-button--lg {
  height: var(--button-height-lg);
  font-size: var(--font-lg);
  border-radius: var(--radius-md);
}

.custom-button--lg .custom-button__content {
  padding: 20rpx 40rpx;
}

/* 块级按钮 */
.custom-button--block {
  width: 100%;
  display: flex;
}

/* 圆形按钮 */
.custom-button--round {
  border-radius: var(--radius-round);
}

.custom-button--round.custom-button--sm {
  width: var(--button-height-sm);
  padding: 0;
}

.custom-button--round.custom-button--md {
  width: var(--button-height-md);
  padding: 0;
}

.custom-button--round.custom-button--lg {
  width: var(--button-height-lg);
  padding: 0;
}

/* ==================== 按钮状态 ==================== */

/* 禁用状态 */
.custom-button--disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.custom-button--disabled .custom-button__background {
  background: var(--bg-tertiary) !important;
  box-shadow: none !important;
  transform: none !important;
}

/* 加载状态 */
.custom-button--loading {
  pointer-events: none;
}

.custom-button--loading .custom-button__text {
  opacity: 0.7;
}

/* ==================== 响应式适配 ==================== */

@media (max-width: 375px) {
  .custom-button--lg {
    height: var(--button-height-md);
    font-size: var(--font-base);
  }
  
  .custom-button--lg .custom-button__content {
    padding: var(--button-padding-y) var(--button-padding-x);
  }
}

/* ==================== 动画效果 ==================== */

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes ripple {
  0% {
    width: 0;
    height: 0;
    opacity: 1;
  }
  100% {
    width: 200rpx;
    height: 200rpx;
    opacity: 0;
  }
}

.custom-button__ripple {
  animation: ripple 0.6s ease-out;
}
