<!--
  Vault<PERSON>eeper 自定义按钮组件
  完美还原原型中的按钮设计和交互
-->

<button 
  class="custom-button {{getButtonClasses()}}"
  style="{{customStyle}}"
  disabled="{{disabled || loading}}"
  form-type="{{formType}}"
  open-type="{{openType}}"
  bind:tap="handleTap"
  bind:touchstart="handleTouchStart"
  bind:touchend="handleTouchEnd"
  bind:getuserinfo="handleGetUserInfo"
  bind:contact="handleContact"
  bind:getphonenumber="handleGetPhoneNumber"
  bind:error="handleError"
  bind:opensetting="handleOpenSetting"
  bind:chooseavatar="handleChooseAvatar"
>
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="custom-button__loading">
    <view class="loading-spinner loading-spinner--{{size}}"></view>
    <text wx:if="{{loadingText}}" class="custom-button__loading-text">{{loadingText}}</text>
  </view>
  
  <!-- 正常状态内容 -->
  <view wx:else class="custom-button__content">
    <!-- 左侧图标 -->
    <view wx:if="{{leftIcon}}" class="custom-button__icon custom-button__icon--left">
      <text class="iconfont icon-{{leftIcon}}"></text>
    </view>
    
    <!-- 按钮文本 -->
    <text wx:if="{{text}}" class="custom-button__text">{{text}}</text>
    
    <!-- 插槽内容 -->
    <slot wx:else></slot>
    
    <!-- 右侧图标 -->
    <view wx:if="{{rightIcon}}" class="custom-button__icon custom-button__icon--right">
      <text class="iconfont icon-{{rightIcon}}"></text>
    </view>
  </view>
  
  <!-- 按钮背景效果 -->
  <view class="custom-button__background"></view>
  
  <!-- 点击波纹效果 -->
  <view wx:if="{{ripple && !disabled}}" class="custom-button__ripple"></view>
</button>
