/**
 * VaultKeeper 自定义按钮组件
 * 完美还原原型设计，支持多种变体和交互状态
 * 
 * 功能特性：
 * - 多种按钮变体（primary, secondary, ghost, text, danger, success）
 * - 多种尺寸（sm, md, lg）和形状（round, block）
 * - 加载状态和禁用状态
 * - 图标支持和波纹效果
 * - 小程序原生按钮功能集成
 * - 触觉反馈和无障碍支持
 */

import { defineComponent } from '@vue-mini/core'

/**
 * 按钮变体类型
 */
type ButtonVariant = 'primary' | 'secondary' | 'ghost' | 'text' | 'danger' | 'success'

/**
 * 按钮尺寸类型
 */
type ButtonSize = 'sm' | 'md' | 'lg'

/**
 * 按钮形状类型
 */
type ButtonShape = 'default' | 'round'

/**
 * 小程序按钮开放能力类型
 */
type OpenType = 'contact' | 'share' | 'getPhoneNumber' | 'getUserInfo' | 'launchApp' | 'openSetting' | 'feedback' | 'chooseAvatar'

/**
 * 表单类型
 */
type FormType = 'submit' | 'reset'

defineComponent({
  /**
   * 组件属性定义
   */
  properties: {
    // 基础属性
    customClass: {
      type: String,
      value: ''
    },
    customStyle: {
      type: String,
      value: ''
    },
    
    // 按钮内容
    text: {
      type: String,
      value: ''
    },
    leftIcon: {
      type: String,
      value: ''
    },
    rightIcon: {
      type: String,
      value: ''
    },
    
    // 按钮样式
    variant: {
      type: String as PropType<ButtonVariant>,
      value: 'primary'
    },
    size: {
      type: String as PropType<ButtonSize>,
      value: 'md'
    },
    shape: {
      type: String as PropType<ButtonShape>,
      value: 'default'
    },
    block: {
      type: Boolean,
      value: false
    },
    
    // 按钮状态
    disabled: {
      type: Boolean,
      value: false
    },
    loading: {
      type: Boolean,
      value: false
    },
    loadingText: {
      type: String,
      value: ''
    },
    
    // 交互效果
    ripple: {
      type: Boolean,
      value: true
    },
    hapticFeedback: {
      type: Boolean,
      value: true
    },
    
    // 小程序原生属性
    formType: {
      type: String as PropType<FormType>,
      value: ''
    },
    openType: {
      type: String as PropType<OpenType>,
      value: ''
    },
    
    // 无障碍属性
    ariaLabel: {
      type: String,
      value: ''
    }
  },

  /**
   * 事件定义
   */
  emits: {
    tap: (_event: WechatMiniprogram.TouchEvent) => true,
    touchstart: (_event: WechatMiniprogram.TouchEvent) => true,
    touchend: (_event: WechatMiniprogram.TouchEvent) => true,
    getuserinfo: (_event: WechatMiniprogram.GetUserInfoCallbackResult) => true,
    contact: (_event: WechatMiniprogram.ContactCallbackResult) => true,
    getphonenumber: (_event: WechatMiniprogram.GetPhoneNumberCallbackResult) => true,
    error: (_event: WechatMiniprogram.ButtonErrorCallbackResult) => true,
    opensetting: (_event: WechatMiniprogram.OpenSettingCallbackResult) => true,
    chooseavatar: (_event: WechatMiniprogram.ChooseAvatarCallbackResult) => true
  },

  /**
   * 组件设置
   */
  setup(props, { emit }) {
    console.log('🔘 CustomButton 组件初始化:', props)

    /**
     * 处理点击事件
     * @param event 触摸事件
     */
    const handleTap = (event: WechatMiniprogram.TouchEvent) => {
      if (props.disabled || props.loading) {
        return
      }

      console.log('👆 CustomButton 点击事件:', event)
      
      // 触觉反馈
      if (props.hapticFeedback && wx.vibrateShort) {
        wx.vibrateShort({
          type: 'light'
        })
      }
      
      // 触发波纹效果
      if (props.ripple) {
        triggerRippleEffect(event)
      }
      
      emit('tap', event)
    }

    /**
     * 处理触摸开始事件
     * @param event 触摸事件
     */
    const handleTouchStart = (event: WechatMiniprogram.TouchEvent) => {
      if (props.disabled || props.loading) {
        return
      }

      emit('touchstart', event)
    }

    /**
     * 处理触摸结束事件
     * @param event 触摸事件
     */
    const handleTouchEnd = (event: WechatMiniprogram.TouchEvent) => {
      if (props.disabled || props.loading) {
        return
      }

      emit('touchend', event)
    }

    /**
     * 处理获取用户信息
     * @param event 用户信息回调
     */
    const handleGetUserInfo = (event: WechatMiniprogram.GetUserInfoCallbackResult) => {
      console.log('👤 获取用户信息:', event)
      emit('getuserinfo', event)
    }

    /**
     * 处理客服会话
     * @param event 客服回调
     */
    const handleContact = (event: WechatMiniprogram.ContactCallbackResult) => {
      console.log('💬 客服会话:', event)
      emit('contact', event)
    }

    /**
     * 处理获取手机号
     * @param event 手机号回调
     */
    const handleGetPhoneNumber = (event: WechatMiniprogram.GetPhoneNumberCallbackResult) => {
      console.log('📱 获取手机号:', event)
      emit('getphonenumber', event)
    }

    /**
     * 处理错误事件
     * @param event 错误回调
     */
    const handleError = (event: WechatMiniprogram.ButtonErrorCallbackResult) => {
      console.error('❌ 按钮错误:', event)
      emit('error', event)
    }

    /**
     * 处理打开设置
     * @param event 设置回调
     */
    const handleOpenSetting = (event: WechatMiniprogram.OpenSettingCallbackResult) => {
      console.log('⚙️ 打开设置:', event)
      emit('opensetting', event)
    }

    /**
     * 处理选择头像
     * @param event 头像回调
     */
    const handleChooseAvatar = (event: WechatMiniprogram.ChooseAvatarCallbackResult) => {
      console.log('🖼️ 选择头像:', event)
      emit('chooseavatar', event)
    }

    /**
     * 触发波纹效果
     * @param event 触摸事件
     */
    const triggerRippleEffect = (event: WechatMiniprogram.TouchEvent) => {
      // 波纹效果实现（简化版）
      console.log('🌊 触发波纹效果')
    }

    /**
     * 获取按钮完整的 CSS 类名
     */
    const getButtonClasses = () => {
      const classes = ['custom-button']
      
      // 变体类
      classes.push(`custom-button--${props.variant}`)
      
      // 尺寸类
      classes.push(`custom-button--${props.size}`)
      
      // 形状类
      if (props.shape === 'round') {
        classes.push('custom-button--round')
      }
      
      // 块级按钮
      if (props.block) {
        classes.push('custom-button--block')
      }
      
      // 状态类
      if (props.loading) {
        classes.push('custom-button--loading')
      }

      if (props.disabled) {
        classes.push('custom-button--disabled')
      }

      // 自定义类
      if (props.customClass) {
        classes.push(props.customClass)
      }
      
      return classes.join(' ')
    }

    // 返回组件方法
    return {
      handleTap,
      handleTouchStart,
      handleTouchEnd,
      handleGetUserInfo,
      handleContact,
      handleGetPhoneNumber,
      handleError,
      handleOpenSetting,
      handleChooseAvatar,
      getButtonClasses
    }
  }
})
