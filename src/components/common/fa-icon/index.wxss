/**
 * FontAwesome 图标组件样式
 * 完全遵循 FontAwesome 官方规范
 */

/* 引入动画样式 */
@import "styles/animation/beat.wxss";
@import "styles/animation/bounce.wxss";
@import "styles/animation/fade.wxss";
@import "styles/animation/flip.wxss";
@import "styles/animation/shake.wxss";
@import "styles/animation/spin.wxss";

/* 基础图标样式 */
.fa-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: color 0.2s ease-in-out;
  color: currentColor;
  position: relative;
  /* vertical-align: -0.125em; */
}

/* 固定宽度 */
.fa-fw {
  text-align: center;
  width: 1.25em;
}

/* 边框 */
.fa-border {
  border-color: var(--fa-border-color, #eee);
  border-radius: var(--fa-border-radius, 0.1em);
  border-style: var(--fa-border-style, solid);
  border-width: var(--fa-border-width, 0.08em);
  padding: var(--fa-border-padding, 0.2em 0.25em 0.15em);
}

/* 拉取 */
.fa-pull-left {
  float: left;
  margin-right: var(--fa-pull-margin, 0.3em);
}

.fa-pull-right {
  float: right;
  margin-left: var(--fa-pull-margin, 0.3em);
}

/* 旋转 */
.fa-rotate-90 {
  transform: rotate(90deg);
}

.fa-rotate-180 {
  transform: rotate(180deg);
}

.fa-rotate-270 {
  transform: rotate(270deg);
}

/* 翻转 */
.fa-flip-horizontal {
  transform: scale(-1, 1);
}

.fa-flip-vertical {
  transform: scale(1, -1);
}

.fa-flip-both,
.fa-flip-horizontal.fa-flip-vertical {
  transform: scale(-1, -1);
}

/* 反色 */
.fa-inverse {
  color: var(--fa-inverse, #fff);
}