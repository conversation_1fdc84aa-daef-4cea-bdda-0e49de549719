<!-- FontAwesome 图标组件 -->
<image src="{{ iconStr }}" style="{{ icon.setStyles(size, color) }} {{ customStyle }}" class="fa-class {{ icon.getAnimationClasses(animation) }} {{ icon.setClasses(classes,border, pull, inverse,fixedWidth) }}" aria-role="{{name}}" />
<wxs module="icon">
  /**
   * 设置图标样式
   */
  function setStyles(size, color) {
    var styles = []
    if (size) {
      styles.push('width:' + size)
      styles.push('height:' + size)
    }

    if (color && color !== 'currentColor') {
      styles.push('color:' + color)
    }

    return styles.join(";")
  }

  /**
   * 获取动画类名
   */
  function getAnimationClasses(animations) {
    if (!animations || animations.length === 0) {
      return ''
    }

    var classes = []
    for (var i = 0; i < animations.length; i++) {
      classes.push('fa-' + animations[i])
    }

    return classes.join(' ')
  }

  /**
   * 获取布局样式类名
   */
  function setClasses(classes, border, pull, inverse, fixedWidth) {
    if (fixedWidth) {
      classes.push('fa-fw')
    }

    if (border) {
      classes.push('fa-border')
    }

    if (pull) {
      classes.push('fa-pull-' + pull)
    }

    if (inverse) {
      classes.push('fa-inverse')
    }

    return classes.join(' ')
  }

  module.exports = {
    setStyles: setStyles,
    setClasses: setClasses,
    getAnimationClasses: getAnimationClasses
  }
</wxs>