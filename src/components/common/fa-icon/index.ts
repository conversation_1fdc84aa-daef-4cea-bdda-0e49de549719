import { iconToSvg } from "./core/svg"

const defaultSize = 40
/**
 * FontAwesome 图标组件
 * 完全遵循 FontAwesome Vue 官方规范
 * 支持所有官方功能：尺寸、动画、变换、遮罩等
 */
Component({
  externalClasses: ['fa-class', 'class'],
  // options: {
  //   addGlobalClass: true,
  // },
  properties: {
    // 基础属性
    icon: {
      type: Object,
      optionalTypes: [String], // 支持图标链接
      observer(newVal, oldVal) {
        if (newVal && newVal !== oldVal) {
          this.updateIcon()
        }
      }
    },

    // 颜色属性
    color: {
      type: String,
      value: 'currentColor'
    },

    // 尺寸属性 - 支持官方标准尺寸
    // size?: '2xs' | 'xs' | 'sm' | 'lg' | 'xl' | '2xl'
    // | '1x' | '2x' | '3x' | '4x' | '5x' | '6x' | '7x' | '8x' | '9x' | '10x'
    size: {
      type: String,
      optionalTypes: [Number],
      value: defaultSize.toString(),
    },

    // 固定宽度
    /** 是否启用等宽对齐（添加 fa-fw）；常用于导航或列表垂直对齐 */
    fixedWidth: {
      type: Boolean,
      value: false,
    },

    // 旋转角度 (0, 90, 180, 270)
    rotation: {
      type: Number,
      value: 0,
    },

    // 翻转 ('horizontal', 'vertical', 'both')
    flip: {
      type: String,
      value: '',
      observer() {
        this.updateIcon()
      }
    },

    // 动画效果数组
    //   animation?: (
    //     | 'spin' | 'spin-pulse' | 'spin-reverse'
    //     | 'beat' | 'beat-fade'
    //     | 'bounce' | 'fade' | 'flip' | 'shake'
    // )[]
    animation: {
      type: Array,
      value: [],
    },

    /** 是否给图标绘制细边框（添加 fa-border） */
    border: {
      type: Boolean,
      value: false,
      observer() {
        this.updateIcon()
      }
    },

    /** 文本环绕定位：'left' | 'right'；对应 fa-pull-left/right */
    pull: {
      type: String,
      value: ''
    },

    // Power Transform 字符串
    transform: {
      type: Object
    },

    // 遮罩图标
    mask: {
      type: Object,
      value: undefined,
      observer() {
        this.updateIcon()
      }
    },

    // 反色
    inverse: {
      type: Boolean,
      value: false
    },

    // Duotone 相关
    primaryColor: {
      type: String,
      value: '',
      observer() {
        this.updateIcon()
      }
    },

    secondaryColor: {
      type: String,
      value: '',
      observer() {
        this.updateIcon()
      }
    },

    primaryOpacity: {
      type: Number,
      value: 1,
      observer() {
        this.updateIcon()
      }
    },

    secondaryOpacity: {
      type: Number,
      value: 0.4,
      observer() {
        this.updateIcon()
      }
    },

    swapOpacity: {
      type: Boolean,
      value: false,
      observer() {
        this.updateIcon()
      }
    },

    // 自定义样式
    customStyle: {
      type: String,
      value: ''
    }
  },
  methods: {
    stringToUint8Array(str: string) {
      let arr: number[] = [];
      for (let i = 0; i < str.length; i++) {
        // 由于报错类型 “any” 的参数不能赋给类型 “never”，推测 arr 被推断为 never[] 类型，这里明确指定 arr 为 number[] 类型
        // 确保可以正确 push 字符编码
        const charCode = str.charCodeAt(i);
        (arr as number[]).push(charCode);
      }
      const tmpUint8Array = new Uint8Array(arr);
      return tmpUint8Array.buffer;
    },
    arrayBufferToBase64(buffer: ArrayBuffer) {
      const keyStr = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';
      const bytes = new Uint8Array(buffer);
      let result = '';

      for (let i = 0; i < bytes.length; i += 3) {
        const byte1 = bytes[i] & 0xff;
        const byte2 = bytes[i + 1] & 0xff;
        const byte3 = bytes[i + 2] & 0xff;

        const char1 = byte1 >> 2;
        const char2 = ((byte1 & 0x3) << 4) | (byte2 >> 4);
        const char3 = ((byte2 & 0xf) << 2) | (byte3 >> 6);
        const char4 = byte3 & 0x3f;

        result += keyStr.charAt(char1) + keyStr.charAt(char2)
          + (i + 1 < bytes.length ? keyStr.charAt(char3) : '=')
          + (i + 2 < bytes.length ? keyStr.charAt(char4) : '=');
      }
      return result;
    },
    svgToBase64(svgStr: string) {
      return `data:image/svg+xml;base64,${this.arrayBufferToBase64(this.stringToUint8Array(svgStr))}`;
    },
    /**
     * 解析尺寸值
     */
    parseSize(size: string) {
      if (!size) return defaultSize + '';

      const sizeMap: Record<string, number> = {
        '2xs': 0.625,
        xs: 0.75,
        sm: 0.875,
        lg: 1.25,
        xl: 1.5,
        '2xl': 2,
      }
      // 检查是否是标准尺寸
      if (sizeMap[size]) {
        return `${defaultSize * sizeMap[size]}rpx`
      }

      // 检查是否是 X 倍数尺寸 (1x-10x)
      const xMatch = size.match(/^(\d+)x$/);
      if (xMatch) {
        return `${defaultSize * parseInt(xMatch[1])}rpx`
      }

      // 数字尺寸，转换为 rpx
      if (/^\d+$/.test(size)) {
        return `${size}rpx`
      }

      // 其他情况直接返回
      return size
    },

    /**
     * 更新图标
     * 根据所有属性生成完整的 SVG
     */
    updateIcon() {
      const {
        icon, color, size, rotation, flip, transform, mask,
        primaryColor, secondaryColor, primaryOpacity, secondaryOpacity, swapOpacity
      } = this.properties;

      // 如果是字符串（图片链接），直接使用
      if (typeof icon === 'string') {
        this.setData({
          iconStr: icon,
          size: this.parseSize(size)
        });
        return;
      }

      // 构建 iconToSvg 选项
      const options = {
        color: primaryColor || color || 'currentColor',
        rotation: rotation || undefined,
        flip: flip || undefined,
        transform: transform || undefined,
        mask: mask || undefined,
        primaryColor,
        secondaryColor,
        primaryOpacity,
        secondaryOpacity,
        swapOpacity
      };

      // 清理 undefined 值
      Object.keys(options).forEach(key => {
        if (options[key] === undefined || options[key] === '' || options[key] === 0) {
          delete options[key];
        }
      });

      try {
        const svgStr = iconToSvg(icon.definition || icon, options);
        this.setData({
          name: icon.iconName,
          iconStr: this.svgToBase64(svgStr),
          size: this.parseSize(size)
        });
      } catch (error) {
        console.error('fa-icon 更新失败:', error);
        this.setData({
          name: icon.iconName,
          iconStr: '',
          size: this.parseSize(size)
        });
      }
    }
  },
  data: {
    iconStr: '',
    classes: ['fa-icon'],
    size: '40',
    name: '',
  },
  lifetimes: {
    // 在组件加载前生成 Base64 的 SVG 字符串
    attached() {
      if (!this.properties.icon) return
      this.updateIcon()
    }
  }
})