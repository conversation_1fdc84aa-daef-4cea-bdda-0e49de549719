

## 使用示例
```
<!-- FontAwesome 图标测试 -->
<view class="icon-test-container">
    <!-- 基础图标 -->
    <fa-icon icon="{{bellIcon}}" color="#6d4aff" size="lg" />
    <!-- 带动画的图标 -->
    <fa-icon icon="{{'https://www.jsdelivr.com/assets/c8cd6195c9319e6d27e4bd5f532f0369414ce0c5/img/jsdelivr-horizontal-regular.svg'}}" color="#ff4757" size="xl" animation="{{['beat']}}" />
    <!-- 旋转图标 -->
    <fa-icon icon="{{eyeIcon}}" color="#2ed573" rotation="{{90}}" />
    <!-- 翻转图标 -->
    <fa-icon icon="{{barChartIcon}}" color="#ffa502" flip="vertical" animation="{{['beat']}}" />
    <!-- 边框图标 -->
    <fa-icon icon="{{bellIcon}}" color="#3742fa" border="{{true}}" />
    <!-- 固定宽度图标 -->
    <fa-icon icon="{{heartIcon}}" color="#ff6b6b" fixed-width="{{true}}" />
</view>
```