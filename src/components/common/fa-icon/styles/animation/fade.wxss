.fa-fade {
    animation-name: fa-fade;
    animation-delay: var(--fa-animation-delay, 0s);
    animation-direction: var(--fa-animation-direction, normal);
    animation-duration: var(--fa-animation-duration, 1s);
    animation-iteration-count: var(--fa-animation-iteration-count, infinite);
    animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));
}

@media (prefers-reduced-motion: reduce) {
    .fa-fade {
        animation-delay: -1ms;
        animation-duration: 1ms;
        animation-iteration-count: 1;
        transition-delay: 0s;
        transition-duration: 0s;
    }
}

@keyframes fa-fade {
    50% {
        opacity: var(--fa-fade-opacity, 0.4);
    }
}