/*  
`@media (prefers-reduced-motion: reduce)` 是一个媒体查询，用于检测用户是否在系统设置中启用了减少动画或运动效果的选项。
 当用户启用此选项时，浏览器会匹配这个媒体查询，从而应用其中定义的样式规则。
 在这里，它主要用于减少 `.fa-beat` 和 `.fa-beat-fade` 类的动画和过渡效果，以符合用户对减少运动的偏好。
*/
@media (prefers-reduced-motion: reduce) {

    .fa-beat,
    .fa-beat-fade {
        animation-delay: -1ms;
        animation-duration: 1ms;
        animation-iteration-count: 1;
        transition-delay: 0s;
        transition-duration: 0s;
    }
}

.fa-beat {
    animation-name: fa-beat;
    animation-delay: var(--fa-animation-delay, 0s);
    animation-direction: var(--fa-animation-direction, normal);
    animation-duration: var(--fa-animation-duration, 1s);
    animation-iteration-count: var(--fa-animation-iteration-count, infinite);
    animation-timing-function: var(--fa-animation-timing, ease-in-out);
}

.fa-beat-fade {
    animation-name: fa-beat-fade;
    animation-delay: var(--fa-animation-delay, 0s);
    animation-direction: var(--fa-animation-direction, normal);
    animation-duration: var(--fa-animation-duration, 1s);
    animation-iteration-count: var(--fa-animation-iteration-count, infinite);
    animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));
}

@keyframes fa-beat {

    0%,
    90% {
        transform: scale(1)
    }

    45% {
        transform: scale(var(--fa-beat-scale, 1.25))
    }
}

@keyframes fa-beat-fade {

    0%,
    100% {
        opacity: var(--fa-beat-fade-opacity, 0.4);
        transform: scale(1);
    }

    50% {
        opacity: 1;
        transform: scale(var(--fa-beat-fade-scale, 1.125));
    }
}