@media (prefers-reduced-motion: reduce) {
    .fa-shake {
        animation-delay: -1ms;
        animation-duration: 1ms;
        animation-iteration-count: 1;
        transition-delay: 0s;
        transition-duration: 0s;
    }
}

.fa-shake {
    animation-name: fa-shake;
    animation-delay: var(--fa-animation-delay, 0s);
    animation-direction: var(--fa-animation-direction, normal);
    animation-duration: var(--fa-animation-duration, 1s);
    animation-iteration-count: var(--fa-animation-iteration-count, infinite);
    animation-timing-function: var(--fa-animation-timing, linear);
}

@keyframes fa-shake {
    0% {
        transform: rotate(-15deg);
    }

    4% {
        transform: rotate(15deg);
    }

    8%,
    24% {
        transform: rotate(-18deg);
    }

    12%,
    28% {
        transform: rotate(18deg);
    }

    16% {
        transform: rotate(-22deg);
    }

    20% {
        transform: rotate(22deg);
    }

    32% {
        transform: rotate(-12deg);
    }

    36% {
        transform: rotate(12deg);
    }

    40%,
    100% {
        transform: rotate(0deg);
    }
}