/* -------------------------------------------------------------
 * Icon 定义 → SVG 字符串 转换函数
 * ------------------------------------------------------------- */
/**
 * 将 Font Awesome 图标定义转换为 SVG 字符串
 * ------------------------------------------------
 * 只关心 `icon` 数组中的五个元素：
 * [0] width   —— 原始视口宽度
 * [1] height  —— 原始视口高度
 * [2] ligatures（可忽略）
 * [3] unicode（可忽略）
 * [4] svgPathData（可能是 string 或 string[]）
 */
type FAIconTuple = [number, number, any[], string, string | string[]]

export interface FAIconDefinition {
    prefix: string
    iconName: string
    icon: FAIconTuple
}

/* Power Transform 对象可根据需要扩展；此处列最常用成员 */
export interface PowerTransformObject {
    size?: number               // grow- / shrink- 数值；正数 grow，负数 shrink
    x?: number                  // left-/right- 的单位(1 = 0.5em)
    y?: number                  // up-/down-
    rotate?: number             // 角度
    flipX?: boolean             // flip-h
    flipY?: boolean             // flip-v
}

export interface IconToSvgOptions {
    /* ===== 基础设置 ===== */
    /** 单色图标填充色；若为 Duotone 则作主/副色默认值；默认 currentColor */
    color?: string
    /** 追加到 <svg> 元素上的任意属性（aria-hidden/role 等） */
    attrs?: Record<string, string>

    /* 方向 / 变形 */
    /** 顺时针旋转角度：0 / 90 / 180 / 270；底层转 CSS transform 或 Power Transform */
    rotation?: 0 | 90 | 180 | 270
    /** 翻转：水平 horizontal、垂直 vertical、双向 both */
    flip?: 'horizontal' | 'vertical' | 'both'
    /**
     * Power Transform 字符串或对象：
     * 支持 grow-# / shrink-# / up|down|left|right-# / rotate-### / flip-h|v|both 等
     * 示例：'shrink-8 left-2 rotate-90'
     */
    transform?: string | PowerTransformObject

    /* 颜色（Duotone）*/
    primaryColor?: string
    secondaryColor?: string
    primaryOpacity?: number
    secondaryOpacity?: number
    swapOpacity?: boolean

    /* 高级 */
    mask?: FAIconDefinition
    maskTransform?: string
    asSymbol?: boolean
}

/* 唯一 ID 生成器（闭包自增）*/
const genId = (() => {
    let n = 0
    return (prefix = 'fa'): string => `${prefix}-${++n}`
})()

/**
 * 解析 Power Transform 字符串（如 "shrink-8 left-2 rotate-90"）
 * 这里只做演示级解析：grow/shrink ±8、位移整数、旋转、flip
 */
function parseTransform(str: string): PowerTransformObject {
    const o: PowerTransformObject = {}
    str.split(/\s+/).forEach(t => {
        if (/^(grow|shrink)-/.test(t)) {
            const [, k, v] = /(grow|shrink)-(\d+)/.exec(t)!
            o.size = (k === 'grow' ? 1 : -1) * +v
        } else if (/^(left|right|up|down)-/.test(t)) {
            const [, dir, v] = /(left|right|up|down)-(\d+)/.exec(t)!
            const d = +v
            if (dir === 'left') o.x = -(d)
            if (dir === 'right') o.x = d
            if (dir === 'up') o.y = -(d)
            if (dir === 'down') o.y = d
        } else if (/^rotate-/.test(t)) {
            o.rotate = +t.split('-')[1]
        } else if (t === 'flip-h') o.flipX = true
        else if (t === 'flip-v') o.flipY = true
        else if (t === 'flip-both') o.flipX = o.flipY = true
    })
    return o
}

/* 把 Power Transform 对象 → transform 字符串（单位按官方 0.0625em / 步计算） */
function composeTransform(
    t: PowerTransformObject | undefined,
    w: number, h: number      // 传入 viewBox 宽高
): string | undefined {
    if (!t) return
    const cx = w / 2, cy = h / 2   // 计算 viewBox 中心
    const cmd: string[] = []
    /* 1) 位移：步长 1/16em 与官方一致 */
    const STEP = 1 / 16;    // 0.0625em

    /* translate → rotate → scale → flip —— 顺序与官方一致 */
    if (t.x || t.y) cmd.push(`translate(${(t.x ?? 0) * STEP}em ${(t.y ?? 0) * STEP}em)`);

    /* 2) 旋转：带中心点，纯数字角度 */
    if (t.rotate) cmd.push(`rotate(${t.rotate} ${cx} ${cy})`);

    /* 3) 缩放 / 放大 */
    if (t.size) cmd.push(`scale(${1 + t.size * STEP})`);

    /* 4) 翻转：translate 先写在左侧，保证实际执行顺序「翻 → 搬」 */
    if (t.flipX) cmd.push(`translate(${w} 0) scale(-1 1)`);
    if (t.flipY) cmd.push(`translate(0 ${h}) scale(1 -1)`);

    return cmd.join(' ')
}

/* ****************************** 主函数 ******************************** */
export function iconToSvg(
    { icon, iconName }: FAIconDefinition,
    opt: IconToSvgOptions = {}
): string {
    /* -------- 解构 icon 基础数据 -------- */
    const [origW, origH, , , rawPath] = icon
    const paths = Array.isArray(rawPath) ? rawPath : [rawPath]

    /* -------- 处理类名 -------- */
    const cls: string[] = []

    /* -------- Power Transform 计算 -------- */
    let ptx: PowerTransformObject | undefined
    if (typeof opt.transform === 'string') {
        ptx = parseTransform(opt.transform)
    } else if (opt.transform) {
        ptx = opt.transform
    }

    /* rotation / flip 融合进 transform */
    if (opt.rotation) (ptx ??= {}).rotate = (ptx?.rotate ?? 0) + opt.rotation
    if (opt.flip) {
        ptx ??= {}
        if (opt.flip === 'horizontal' || opt.flip === 'both') ptx.flipX = !ptx.flipX
        if (opt.flip === 'vertical' || opt.flip === 'both') ptx.flipY = !ptx.flipY
    }
    const transformStr = composeTransform(ptx, origW, origH)

    /* -------- 颜色 & 不透明度 (Duotone) -------- */
    const isDuotone = paths.length === 2
    const {
        primaryColor, secondaryColor,
        primaryOpacity, secondaryOpacity,
        swapOpacity
    } = opt

    const fillDefault = opt.color ?? 'currentColor'

    const pathElems = paths.map((d, idx) => {
        /* 确定配色层次 */
        const primaryIdx = swapOpacity ? 1 : 0
        const isPrimary = idx === primaryIdx

        const color = isPrimary
            ? (primaryColor ?? fillDefault)
            : (secondaryColor ?? fillDefault)

        const opacity = isPrimary
            ? (primaryOpacity !== undefined ? primaryOpacity : 1)
            : (secondaryOpacity !== undefined ? secondaryOpacity : 0.4)

        const clsName = isDuotone ? (isPrimary ? 'fa-primary' : 'fa-secondary') : undefined
        return `<path d="${d}" fill="${color}"${isDuotone ? ` class="${clsName}"` : ''}${isDuotone ? ` opacity="${opacity}"` : ''
            }></path>`
    }).join('')

    /* -------- Mask 处理 -------- */
    let defs = ''
    let maskAttr = ''
    if (opt.mask) {
        const maskId = genId('mask')
        const maskSvg = iconToSvg(opt.mask,
            { ...opt, transform: opt.maskTransform, asSymbol: false })
            .replace(/^<svg[^>]*>|<\/svg>$/g, '') // 仅取内部 <g>/<path>

        defs += `<mask id="${maskId}">${maskSvg}</mask>`
        maskAttr = ` mask="url(#${maskId})"`
    }

    /* -------- Symbol 处理 -------- */
    const symbolId = opt.asSymbol ? genId(iconName) : undefined
    let symbolDef = ''
    if (symbolId) {
        /* 把完整图形放入 <symbol>，后面用 <use> 引用 */
        const inner = `<g${transformStr ? ` transform="${transformStr}"` : ''}${maskAttr}>${pathElems}</g>`
        symbolDef = `<symbol id="${symbolId}" viewBox="0 0 ${origW} ${origH}">${inner}</symbol>`
    }

    /* -------- 拼接 SVG/USE 字符串 -------- */
    const svgAttrs: string[] = [
        `xmlns="http://www.w3.org/2000/svg"`,
        `viewBox="0 0 ${origW} ${origH}"`,
        `class="${cls.join(' ')}"`,
    ]

    if (opt.attrs) {
        Object.entries(opt.attrs).forEach(([k, v]) => svgAttrs.push(`${k}="${v}"`))
    }

    /* 返回 <symbol> + <svg><use>…</svg> 或 纯 <svg> */
    const svgBody = symbolId
        ? `<use href="#${symbolId}"></use>`
        : `<g${transformStr ? ` transform="${transformStr}"` : ''}${maskAttr}>${pathElems}</g>`

    const svg = `<svg ${svgAttrs.join(' ')}>${svgBody}${defs || symbolDef ? `<defs>${defs}${symbolDef}</defs>` : ''
        }</svg>`

    console.log(svg)
    return svg.replace(/\s{2,}|\n/g, '')
}
