/**
 * VaultKeeper 图标组件
 * 使用 FontAwesome 字体图标
 *
 * 功能特性：
 * - 支持 FontAwesome 图标
 * - 可配置尺寸和颜色
 * - 支持不同图标类型 (fas, far, fab)
 */

import { defineComponent, computed } from '@vue-mini/core'

// FontAwesome 图标映射表
const ICON_MAP: Record<string, { icon: string; type: string }> = {
  // 导航图标
  'home': { icon: 'home', type: 'fas' },
  'key': { icon: 'key', type: 'fas' },
  'shield-alt': { icon: 'shield-alt', type: 'fas' },
  'cog': { icon: 'cog', type: 'fas' },
  'settings': { icon: 'cog', type: 'fas' },

  // 操作图标
  'plus': { icon: 'plus', type: 'fas' },
  'search': { icon: 'search', type: 'fas' },
  'copy': { icon: 'copy', type: 'far' },
  'eye': { icon: 'eye', type: 'far' },
  'eye-slash': { icon: 'eye-slash', type: 'far' },
  'arrow-left': { icon: 'arrow-left', type: 'fas' },
  'pen': { icon: 'pen', type: 'fas' },
  'edit': { icon: 'pen', type: 'fas' },
  'trash-alt': { icon: 'trash-alt', type: 'fas' },
  'delete': { icon: 'trash-alt', type: 'fas' },
  'external-link-alt': { icon: 'external-link-alt', type: 'fas' },
  'redo': { icon: 'redo', type: 'fas' },
  'refresh': { icon: 'redo', type: 'fas' },
  'minus': { icon: 'minus', type: 'fas' },
  'save': { icon: 'save', type: 'fas' },
  'chevron-down': { icon: 'chevron-down', type: 'fas' },
  'chevron-right': { icon: 'chevron-right', type: 'fas' },
  'chevron-up': { icon: 'chevron-up', type: 'fas' },
  'chevron-left': { icon: 'chevron-left', type: 'fas' },

  // 状态图标
  'exclamation-circle': { icon: 'exclamation-circle', type: 'fas' },
  'exclamation-triangle': { icon: 'exclamation-triangle', type: 'fas' },
  'warning': { icon: 'exclamation-triangle', type: 'fas' },
  'clone': { icon: 'clone', type: 'fas' },
  'fingerprint': { icon: 'fingerprint', type: 'fas' },
  'signal': { icon: 'signal', type: 'fas' },
  'wifi': { icon: 'wifi', type: 'fas' },
  'battery-full': { icon: 'battery-full', type: 'fas' },
  'check': { icon: 'check', type: 'fas' },
  'times': { icon: 'times', type: 'fas' },
  'close': { icon: 'times', type: 'fas' },

  // 品牌图标
  'google': { icon: 'google', type: 'fab' },
  'github': { icon: 'github', type: 'fab' },
  'youtube': { icon: 'youtube', type: 'fab' },
  'wechat': { icon: 'weixin', type: 'fab' },
  'qq': { icon: 'qq', type: 'fab' },
  'alipay': { icon: 'alipay', type: 'fab' },
  'twitter': { icon: 'twitter', type: 'fab' },
  'facebook': { icon: 'facebook', type: 'fab' },
  'amazon': { icon: 'amazon', type: 'fab' },

  // 其他图标
  'university': { icon: 'university', type: 'fas' },
  'bank': { icon: 'university', type: 'fas' },
  'shopping-cart': { icon: 'shopping-cart', type: 'fas' },
  'cart': { icon: 'shopping-cart', type: 'fas' },
  'lock': { icon: 'lock', type: 'fas' },
  'unlock': { icon: 'unlock', type: 'fas' },
  'user': { icon: 'user', type: 'fas' },
  'users': { icon: 'users', type: 'fas' },
  'star': { icon: 'star', type: 'fas' },
  'heart': { icon: 'heart', type: 'fas' },
  'bookmark': { icon: 'bookmark', type: 'fas' },
  'tag': { icon: 'tag', type: 'fas' },
  'folder': { icon: 'folder', type: 'fas' },
  'file': { icon: 'file', type: 'fas' },
  'image': { icon: 'image', type: 'fas' },
  'video': { icon: 'video', type: 'fas' },
  'music': { icon: 'music', type: 'fas' },
  'phone': { icon: 'phone', type: 'fas' },
  'email': { icon: 'envelope', type: 'fas' },
  'calendar': { icon: 'calendar', type: 'fas' },
  'clock': { icon: 'clock', type: 'fas' },
  'location': { icon: 'map-marker-alt', type: 'fas' },
  'map': { icon: 'map', type: 'fas' },
  'camera': { icon: 'camera', type: 'fas' },
  'microphone': { icon: 'microphone', type: 'fas' },
  'volume': { icon: 'volume-up', type: 'fas' },
  'download': { icon: 'download', type: 'fas' },
  'upload': { icon: 'upload', type: 'fas' },
  'share': { icon: 'share', type: 'fas' },
  'print': { icon: 'print', type: 'fas' },
  'qrcode': { icon: 'qrcode', type: 'fas' },
  'barcode': { icon: 'barcode', type: 'fas' },
  'chart': { icon: 'chart-line', type: 'fas' },
  'pie-chart': { icon: 'chart-pie', type: 'fas' },
  'calculator': { icon: 'calculator', type: 'fas' },
  'tools': { icon: 'tools', type: 'fas' },
  'wrench': { icon: 'wrench', type: 'fas' },
  'hammer': { icon: 'hammer', type: 'fas' },
  'magic': { icon: 'magic', type: 'fas' },
  'fire': { icon: 'fire', type: 'fas' },
  'lightning': { icon: 'bolt', type: 'fas' },
  'sun': { icon: 'sun', type: 'fas' },
  'moon': { icon: 'moon', type: 'fas' },
  'cloud': { icon: 'cloud', type: 'fas' },
  'rain': { icon: 'cloud-rain', type: 'fas' },
  'snow': { icon: 'snowflake', type: 'fas' },
  'globe': { icon: 'globe', type: 'fas' }
}

defineComponent({
  /**
   * 组件属性定义
   */
  properties: {
    // 基础属性
    customClass: {
      type: String,
      value: ''
    },
    customStyle: {
      type: String,
      value: ''
    },

    // 图标名称
    name: {
      type: String,
      value: 'home',
      required: true
    },

    // 图标类型 (fas, far, fab)
    type: {
      type: String,
      value: ''
    },

    // 图标尺寸 (rpx)
    size: {
      type: Number,
      value: 32
    },

    // 图标颜色
    color: {
      type: String,
      value: 'inherit'
    }
  },

  /**
   * 组件设置
   */
  setup(props) {
    console.log('🎨 Icon 组件初始化:', props.name)

    /**
     * 获取图标类名
     */
    const iconClass = computed(() => {
      const iconInfo = ICON_MAP[props.name]
      if (!iconInfo) {
        return 'fas fa-question-circle'
      }

      const iconType = props.type || iconInfo.type
      return `${iconType} fa-${iconInfo.icon}`
    })

    // 返回响应式数据和方法
    return {
      iconClass
    }
  }
})
