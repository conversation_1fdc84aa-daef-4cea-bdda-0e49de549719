/**
 * VaultKeeper 浮动按钮组件
 * 完美还原原型设计，提供统一的浮动按钮交互
 * 
 * 功能特性：
 * - 完全按照原型的样式设计
 * - 支持自定义图标和内容
 * - 内置触觉反馈
 * - 支持自定义位置和样式
 * - 响应式交互动画
 */

import { defineComponent } from '@vue-mini/core'

defineComponent({
  /**
   * 组件属性定义
   */
  properties: {
    // 基础属性
    customClass: {
      type: String,
      value: ''
    },
    customStyle: {
      type: String,
      value: ''
    },

    // 图标属性
    icon: {
      type: String,
      value: 'plus'
    },
    iconClass: {
      type: String,
      value: ''
    },

    // 交互属性
    hapticFeedback: {
      type: Boolean,
      value: true
    },
    disabled: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件设置
   */
  setup(props, { emit }) {
    console.log('🔘 FloatingButton 组件初始化:', props)

    /**
     * 处理点击事件
     * @param event 触摸事件
     */
    const handleTap = (event: WechatMiniprogram.TouchEvent) => {
      if (props.disabled) {
        return
      }

      console.log('👆 FloatingButton 点击事件:', event)

      // 触觉反馈
      if (props.hapticFeedback && wx.vibrateShort) {
        wx.vibrateShort({
          type: 'light'
        })
      }

      emit('tap', event)
    }

    return {
      handleTap
    }
  }
})
