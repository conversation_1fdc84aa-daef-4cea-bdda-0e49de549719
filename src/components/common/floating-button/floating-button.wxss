/**
 * Vault<PERSON>eeper 浮动按钮组件样式
 * 完美还原原型设计，提供统一的浮动按钮样式
 */

.floating-button {
  position: absolute;
  bottom: 200rpx;
  right: 40rpx;
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  background: var(--accent-color);
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 16rpx 32rpx rgba(109, 74, 255, 0.3);
  z-index: 40;
  transition: transform 0.2s ease;
  cursor: pointer;
}

.floating-button:active {
  transform: scale(0.95);
}

/* 禁用状态 */
.floating-button--disabled {
  opacity: 0.5;
  pointer-events: none;
}

/* 不同位置的变体 */
.floating-button--bottom-left {
  left: 40rpx;
  right: auto;
}

.floating-button--top-right {
  top: 60rpx;
  bottom: auto;
}

.floating-button--top-left {
  top: 60rpx;
  left: 40rpx;
  right: auto;
  bottom: auto;
}

/* 不同尺寸的变体 */
.floating-button--sm {
  width: 96rpx;
  height: 96rpx;
  border-radius: 48rpx;
  bottom: 40rpx;
  right: 32rpx;
}

.floating-button--lg {
  width: 144rpx;
  height: 144rpx;
  border-radius: 72rpx;
  bottom: 80rpx;
  right: 48rpx;
}
