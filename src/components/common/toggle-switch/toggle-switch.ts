/**
 * VaultKeeper 开关组件
 * 完美还原原型设计，支持多种状态和交互
 * 
 * 功能特性：
 * - 开关状态切换
 * - 多种尺寸和主题
 * - 加载和禁用状态
 * - 触觉反馈
 * - 表单集成支持
 * - 无障碍支持
 */

import { defineComponent, ref, watch } from '@vue-mini/core'

/**
 * 开关尺寸类型
 */
type ToggleSize = 'small' | 'medium' | 'large'

/**
 * 开关主题类型
 */
type ToggleTheme = 'default' | 'success' | 'warning' | 'danger'

defineComponent({
  /**
   * 组件属性定义
   */
  properties: {
    // 基础属性
    customClass: {
      type: String,
      value: ''
    },
    customStyle: {
      type: String,
      value: ''
    },

    // 开关状态
    checked: {
      type: Boolean,
      value: false
    },
    disabled: {
      type: Boolean,
      value: false
    },
    loading: {
      type: Boolean,
      value: false
    },

    // 表单属性
    name: {
      type: String,
      value: ''
    },
    value: {
      type: String,
      value: ''
    },

    // 样式属性
    size: {
      type: String as PropType<ToggleSize>,
      value: 'medium'
    },
    theme: {
      type: String as PropType<ToggleTheme>,
      value: 'default'
    },

    // 震动反馈
    hapticFeedback: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 事件定义
   */
  emits: {
    change: (_event: { checked: boolean; value: string }) => true,
    toggle: (_checked: boolean) => true
  },

  /**
   * 组件设置
   */
  setup(props, { emit }) {
    console.log('🔘 ToggleSwitch 组件初始化:', props)

    // 响应式数据 - 初始化为props.checked的值
    const internalChecked = ref(props.checked)

    // 监听 checked 属性变化
    watch(() => props.checked, (newVal: boolean, oldVal: boolean) => {
      // 只有当外部传入的值与内部状态不同时才更新
      if (newVal !== internalChecked.value) {
        console.log('🔘 外部状态变化:', { from: oldVal, to: newVal })
        internalChecked.value = newVal
      }
    })

    /**
     * 处理开关切换
     */
    const handleToggle = () => {
      // 如果禁用或加载中，不处理切换
      if (props.disabled || props.loading) {
        console.log('🔘 开关被禁用或加载中，忽略切换')
        return
      }

      const newChecked = !internalChecked.value

      console.log('🔘 开关状态切换:', {
        from: internalChecked.value,
        to: newChecked
      })

      // 更新内部状态
      internalChecked.value = newChecked

      // 触觉反馈
      if (props.hapticFeedback && wx.vibrateShort) {
        wx.vibrateShort({
          type: newChecked ? 'medium' : 'light'
        })
      }

      // 触发变化事件
      emit('change', {
        checked: newChecked,
        value: props.value || newChecked.toString()
      })

      // 兼容性事件
      emit('toggle', newChecked)
    }



    /**
     * 获取组件样式类
     */
    const getComponentClass = () => {
      const classes = ['toggle-switch']

      if (props.size !== 'medium') {
        classes.push(`toggle-switch--${props.size}`)
      }

      if (props.theme !== 'default') {
        classes.push(`toggle-switch--${props.theme}`)
      }

      if (props.disabled) {
        classes.push('toggle-switch--disabled')
      }

      if (internalChecked.value) {
        classes.push('toggle-switch--checked')
      }

      if (props.customClass) {
        classes.push(props.customClass)
      }

      return classes.join(' ')
    }

    /**
     * 手动设置开关状态
     * @param checked 是否选中
     * @param triggerEvent 是否触发事件
     */
    const setChecked = (checked: boolean, triggerEvent: boolean = false) => {
      internalChecked.value = checked

      if (triggerEvent) {
        emit('change', {
          checked,
          value: props.value || checked.toString()
        })
      }
    }

    /**
     * 获取当前状态
     */
    const getChecked = (): boolean => {
      return internalChecked.value
    }

    /**
     * 切换状态
     */
    const toggle = () => {
      handleToggle()
    }

    // 返回响应式数据和方法
    return {
      // 响应式数据
      internalChecked,

      // 方法
      handleToggle,
      getComponentClass,
      setChecked,
      getChecked,
      toggle
    }
  }
})
