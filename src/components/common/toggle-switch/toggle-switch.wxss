/**
 * VaultKeeper 开关组件样式
 * 完美还原原型设计，支持玻璃拟态效果
 */

/* ==================== 组件容器 ==================== */

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 100rpx;
  height: 56rpx;
  cursor: pointer;
  user-select: none;
  transition: opacity 0.3s ease;
}

.toggle-switch--disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* ==================== 开关轨道 ==================== */

.toggle-switch__track {
  width: 100%;
  height: 100%;
  border-radius: 28rpx;
  background: rgba(255, 255, 255, 0.1);
  border: 2rpx solid rgba(255, 255, 255, 0.1);
  position: relative;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.toggle-switch--checked .toggle-switch__track {
  background: var(--accent-color);
  border-color: var(--accent-color);
  box-shadow: 0 0 20rpx rgba(109, 74, 255, 0.3);
}

/* ==================== 开关滑块 ==================== */

.toggle-switch__thumb {
  position: absolute;
  top: 4rpx;
  left: 4rpx;
  width: 44rpx;
  height: 44rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  box-shadow: 
    0 4rpx 12rpx rgba(0, 0, 0, 0.15),
    0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.toggle-switch--checked .toggle-switch__thumb {
  transform: translateX(44rpx);
  background: rgba(255, 255, 255, 1);
  box-shadow: 
    0 6rpx 16rpx rgba(0, 0, 0, 0.2),
    0 3rpx 6rpx rgba(0, 0, 0, 0.15);
}

/* ==================== 加载状态 ==================== */

.toggle-switch__loading {
  width: 24rpx;
  height: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toggle-switch__spinner {
  width: 16rpx;
  height: 16rpx;
  border: 2rpx solid rgba(109, 74, 255, 0.2);
  border-top: 2rpx solid var(--accent-color);
  border-radius: 50%;
  animation: toggle-spin 1s linear infinite;
}

@keyframes toggle-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ==================== 交互状态 ==================== */

.toggle-switch:active .toggle-switch__thumb {
  transform: scale(0.95);
}

.toggle-switch--checked:active .toggle-switch__thumb {
  transform: translateX(44rpx) scale(0.95);
}

.toggle-switch--disabled:active .toggle-switch__thumb {
  transform: none;
}

.toggle-switch--disabled.toggle-switch--checked:active .toggle-switch__thumb {
  transform: translateX(44rpx);
}

/* ==================== 焦点状态 ==================== */

.toggle-switch:focus-within .toggle-switch__track {
  outline: 4rpx solid rgba(109, 74, 255, 0.2);
  outline-offset: 2rpx;
}

/* ==================== 尺寸变体 ==================== */

.toggle-switch--small {
  width: 80rpx;
  height: 44rpx;
}

.toggle-switch--small .toggle-switch__thumb {
  width: 36rpx;
  height: 36rpx;
}

.toggle-switch--small.toggle-switch--checked .toggle-switch__thumb {
  transform: translateX(32rpx);
}

.toggle-switch--large {
  width: 120rpx;
  height: 68rpx;
}

.toggle-switch--large .toggle-switch__track {
  border-radius: 34rpx;
}

.toggle-switch--large .toggle-switch__thumb {
  width: 56rpx;
  height: 56rpx;
  top: 6rpx;
  left: 6rpx;
}

.toggle-switch--large.toggle-switch--checked .toggle-switch__thumb {
  transform: translateX(52rpx);
}

/* ==================== 主题变体 ==================== */

.toggle-switch--success .toggle-switch--checked .toggle-switch__track {
  background: var(--success-color);
  border-color: var(--success-color);
  box-shadow: 0 0 20rpx rgba(46, 213, 115, 0.3);
}

.toggle-switch--warning .toggle-switch--checked .toggle-switch__track {
  background: var(--warning-color);
  border-color: var(--warning-color);
  box-shadow: 0 0 20rpx rgba(255, 165, 2, 0.3);
}

.toggle-switch--danger .toggle-switch--checked .toggle-switch__track {
  background: var(--error-color);
  border-color: var(--error-color);
  box-shadow: 0 0 20rpx rgba(255, 71, 87, 0.3);
}

/* ==================== 响应式适配 ==================== */

@media (max-width: 375px) {
  .toggle-switch {
    width: 90rpx;
    height: 50rpx;
  }
  
  .toggle-switch__thumb {
    width: 40rpx;
    height: 40rpx;
  }
  
  .toggle-switch--checked .toggle-switch__thumb {
    transform: translateX(38rpx);
  }
}

/* ==================== 无障碍支持 ==================== */

.toggle-switch[aria-disabled="true"] {
  opacity: 0.5;
  pointer-events: none;
}

/* ==================== 高对比度模式 ==================== */

@media (prefers-contrast: high) {
  .toggle-switch__track {
    border-width: 3rpx;
    background: rgba(255, 255, 255, 0.2);
  }
  
  .toggle-switch--checked .toggle-switch__track {
    background: var(--accent-color);
    border-color: rgba(255, 255, 255, 0.8);
  }
  
  .toggle-switch__thumb {
    background: rgba(255, 255, 255, 1);
    border: 2rpx solid rgba(0, 0, 0, 0.2);
  }
}
