<!--
  Vault<PERSON>eeper 开关组件
  完美还原原型设计，支持多种状态和交互
-->

<view
  class="toggle-switch {{customClass}} {{disabled ? 'toggle-switch--disabled' : ''}} {{internalChecked ? 'toggle-switch--checked' : ''}}"
  style="{{customStyle}}"
  bind:tap="handleToggle"
>
  <!-- 开关轨道 -->
  <view class="toggle-switch__track">
    <!-- 开关滑块 -->
    <view class="toggle-switch__thumb">
      <!-- 加载状态 -->
      <view wx:if="{{loading}}" class="toggle-switch__loading">
        <view class="toggle-switch__spinner"></view>
      </view>
    </view>
  </view>
  
  <!-- 隐藏的原生input，用于表单提交 -->
  <input
    wx:if="{{name}}"
    type="checkbox"
    name="{{name}}"
    value="{{value}}"
    checked="{{internalChecked}}"
    style="display: none;"
  />
</view>
