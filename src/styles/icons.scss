/**
 * FontAwesome 图标样式 - 严格按照原型设计实现
 * 使用 Unicode 字符确保在小程序中正常显示
 * 支持 FontAwesome 6.x 版本的 solid, regular, brands 图标
 */

/* ==================== FontAwesome 字体定义 ==================== */

@font-face {
  font-family: 'Font Awesome 6 Free';
  font-style: normal;
  font-weight: 400;
  font-display: block;
  src: url("https://npm.elemecdn.com/@fortawesome/fontawesome-free/webfonts/fa-regular-400.woff2") format("woff2"),
    url("https://npm.elemecdn.com/@fortawesome/fontawesome-free/webfonts/fa-regular-400.woff") format("woff");
}

@font-face {
  font-family: 'Font Awesome 6 Free';
  font-style: normal;
  font-weight: 900;
  font-display: block;
  src: url("https://npm.elemecdn.com/@fortawesome/fontawesome-free/webfonts/fa-solid-900.woff2") format("woff2"),
    url("https://npm.elemecdn.com/@fortawesome/fontawesome-free/webfonts/fa-solid-900.woff") format("woff");
}

@font-face {
  font-family: 'Font Awesome 6 Brands';
  font-style: normal;
  font-weight: 400;
  font-display: block;
  src: url("https://npm.elemecdn.com/@fortawesome/fontawesome-free/webfonts/fa-brands-400.woff2") format("woff2"),
    url("https://npm.elemecdn.com/@fortawesome/fontawesome-free/webfonts/fa-brands-400.woff") format("woff");
}

/* ==================== 基础图标类 ==================== */

.fas,
.fa-solid {
  font-family: 'Font Awesome 6 Free';
  font-weight: 900;
}

.far,
.fa-regular {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}

.fab,
.fa-brands {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}

/* 通用图标样式 */
.fas::before,
.far::before,
.fab::before,
.fa-solid::before,
.fa-regular::before,
.fa-brands::before {
  display: inline-block;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
}

/* ==================== TabBar 图标 ==================== */

/* 主页/密码库 */
.fa-home::before {
  content: "\f015";
}

/* 密码生成器 */
.fa-key::before {
  content: "\f084";
}

/* 安全分析 */
.fa-shield-alt::before {
  content: "\f3ed";
}

/* 设置 */
.fa-cog::before {
  content: "\f013";
}

/* ==================== 功能图标 ==================== */

/* 搜索 */
.fa-search::before {
  content: "\f002";
}

/* 复制 */
.fa-copy::before {
  content: "\f0c5";
}

/* 添加 */
.fa-plus::before {
  content: "\f067";
}

/* 编辑 */
.fa-pen::before {
  content: "\f304";
}

/* 删除 */
.fa-trash-alt::before {
  content: "\f2ed";
}

.fa-trash::before {
  content: "\f1f8";
}

/* 返回 */
.fa-arrow-left::before {
  content: "\f060";
}

/* 显示/隐藏密码 */
.fa-eye::before {
  content: "\f06e";
}

/* 外部链接 */
.fa-external-link-alt::before {
  content: "\f35d";
}

/* 重新生成 */
.fa-redo::before {
  content: "\f01e";
}

.fa-refresh::before {
  content: "\f021";
}

/* 保存 */
.fa-save::before {
  content: "\f0c7";
}

/* 展开/收起 */
.fa-chevron-down::before {
  content: "\f078";
}

.fa-chevron-right::before {
  content: "\f054";
}

.fa-chevron-up::before {
  content: "\f077";
}

/* 增加/减少 */
.fa-minus::before {
  content: "\f068";
}

/* ==================== 状态图标 ==================== */

/* 信号 */
.fa-signal::before {
  content: "\f012";
}

/* WiFi */
.fa-wifi::before {
  content: "\f1eb";
}

/* 电池 */
.fa-battery-full::before {
  content: "\f240";
}

/* 指纹 */
.fa-fingerprint::before {
  content: "\f577";
}

/* 盾牌 */
.fa-shield::before {
  content: "\f132";
}

/* 警告 */
.fa-exclamation-triangle::before {
  content: "\f071";
}

.fa-exclamation-circle::before {
  content: "\f06a";
}

/* 成功 */
.fa-check-circle::before {
  content: "\f058";
}

/* 克隆/重复 */
.fa-clone::before {
  content: "\f24d";
}

/* 同步 */
.fa-sync-alt::before {
  content: "\f2f1";
}

/* 省略号/更多 */
.fa-ellipsis-h::before {
  content: "\f141";
}

/* 眼睛斜杠/隐藏 */
.fa-eye-slash::before {
  content: "\f070";
}

.fa-eye-off::before {
  content: "\f070";
}

/* 心形 */
.fa-heart::before {
  content: "\f004";
}

/* 下载 */
.fa-download::before {
  content: "\f019";
}

/* 上传 */
.fa-upload::before {
  content: "\f093";
}

/* 锁定 */
.fa-lock::before {
  content: "\f023";
}

/* 铃铛/通知 */
.fa-bell::before {
  content: "\f0f3";
}

/* 调色板 */
.fa-palette::before {
  content: "\f53f";
}

/* 帮助/问号 */
.fa-question-circle::before {
  content: "\f059";
}

/* 信息 */
.fa-info-circle::before {
  content: "\f05a";
}

/* 检查/勾选 */
.fa-check::before {
  content: "\f00c";
}

/* 云 */
.fa-cloud::before {
  content: "\f0c2";
}

/* ==================== 品牌图标 ==================== */

/* Google */
.fa-google::before {
  content: "\f1a0";
}

/* GitHub */
.fa-github::before {
  content: "\f09b";
}

/* YouTube */
.fa-youtube::before {
  content: "\f167";
}

/* Twitter */
.fa-twitter::before {
  content: "\f099";
}

/* Facebook */
.fa-facebook::before {
  content: "\f09a";
}

/* ==================== 服务/功能图标 ==================== */

/* 银行/大学 */
.fa-university::before {
  content: "\f19c";
}

/* 购物车 */
.fa-shopping-cart::before {
  content: "\f07a";
}

/* 购物袋 */
.fa-shopping-bag::before {
  content: "\f290";
}

/* 全球/网站 */
.fa-globe::before {
  content: "\f0ac";
}

/* ==================== 兼容性别名 ==================== */

/* 为了兼容现有代码，保留一些自定义类名映射 */
.icon-home::before {
  content: "\f015";
}

.icon-key::before {
  content: "\f084";
}

.icon-shield::before {
  content: "\f3ed";
}

.icon-cog::before {
  content: "\f013";
}

.icon-search::before {
  content: "\f002";
}

.icon-copy::before {
  content: "\f0c5";
}

.icon-plus::before {
  content: "\f067";
}

.icon-google::before {
  content: "\f1a0";
}

.icon-github::before {
  content: "\f09b";
}

.icon-university::before {
  content: "\f19c";
}

.icon-youtube::before {
  content: "\f167";
}

.icon-shopping-cart::before {
  content: "\f07a";
}

/* ==================== 通用样式类 ==================== */

/* 确保所有图标类都有基础样式 */
[class^="fa-"]::before,
[class*=" fa-"]::before,
[class^="icon-"]::before,
[class*=" icon-"]::before {
  font-family: inherit;
  display: inline-block;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 为自定义图标类设置字体 */
[class^="icon-"],
[class*=" icon-"] {
  font-family: 'Font Awesome 6 Free';
  font-weight: 900;
}

/* 品牌图标使用品牌字体 */
.icon-google,
.icon-github,
.icon-youtube,
.icon-twitter,
.icon-facebook {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}