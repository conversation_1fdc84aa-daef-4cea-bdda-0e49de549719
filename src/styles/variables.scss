/**
 * V<PERSON><PERSON>eeper CSS 变量定义
 * 统一管理主题色彩、间距、字体等设计令牌
 */

/* ==================== 主题色彩 ==================== */
page {
  /* 主色调 */
  --primary-color: #6d4aff;
  --primary-light: #8c66ff;
  --primary-dark: #5a3dd9;

  /* 辅助色 */
  --secondary-color: #3742fa;
  --accent-color: var(--primary-color);

  /* 背景色 */
  --bg-primary: #1a1c25;
  --bg-secondary: #0f1118;
  --bg-tertiary: #2a2d3a;
  --bg-card: rgba(30, 33, 45, 0.95);
  --bg-overlay: rgba(0, 0, 0, 0.7);

  /* 玻璃拟态 */
  --glass-bg: rgba(23, 25, 35, 0.85);
  --glass-border: rgba(255, 255, 255, 0.08);
  --glass-highlight: rgba(255, 255, 255, 0.1);

  /* 文字色彩 */
  --text-primary: #e0e0e0;
  --text-secondary: #aaaaaa;
  --text-muted: #888888;
  --text-disabled: #666666;
  --text-white: #ffffff;
  --text-black: #000000;

  /* 状态色彩 */
  --success-color: #2ed573;
  --success-light: #55eaa0;
  --warning-color: #ffa502;
  --warning-light: #ffb733;
  --error-color: #ff4757;
  --error-light: #ff6b7a;
  --info-color: #3742fa;
  --info-light: #5352ed;

  /* 边框色彩 */
  --border-color: rgba(255, 255, 255, 0.1);
  --border-light: rgba(255, 255, 255, 0.05);
  --border-dark: rgba(255, 255, 255, 0.15);

  /* 阴影色彩 */
  --shadow-color: rgba(0, 0, 0, 0.3);
  --shadow-light: rgba(0, 0, 0, 0.1);
  --shadow-dark: rgba(0, 0, 0, 0.5);
}

/* ==================== 间距系统 ==================== */
page {
  /* 基础间距 */
  --spacing-xs: 8rpx;
  --spacing-sm: 16rpx;
  --spacing-md: 24rpx;
  --spacing-lg: 32rpx;
  --spacing-xl: 48rpx;
  --spacing-xxl: 64rpx;
  --spacing-xxxl: 96rpx;

  /* 组件间距 */
  --component-padding: var(--spacing-md);
  --component-margin: var(--spacing-sm);
  --section-padding: var(--spacing-xl);
  --page-padding: var(--spacing-md);

  /* 表单间距 */
  --form-item-margin: var(--spacing-lg);
  --form-input-padding: 36rpx 40rpx;
  /* 增加内边距确保更好的触摸体验 */
  --form-label-margin: var(--spacing-sm);
}

/* ==================== 字体系统 ==================== */
page {
  /* 字体大小 */
  --font-xs: 22rpx;
  --font-sm: 26rpx;
  --font-base: 32rpx;
  /* 增大基础字体确保更好的可读性 */
  --font-lg: 36rpx;
  --font-xl: 40rpx;
  --font-2xl: 48rpx;
  --font-3xl: 60rpx;
  --font-4xl: 72rpx;

  /* 行高 */
  --line-height-tight: 1.2;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.8;

  /* 字重 */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* 字体族 */
  --font-family-base: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  --font-family-mono: "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas, "Courier New", monospace;
}

/* ==================== 圆角系统 ==================== */
page {
  --radius-xs: 8rpx;
  --radius-sm: 12rpx;
  --radius-md: 16rpx;
  --radius-lg: 24rpx;
  --radius-xl: 32rpx;
  --radius-2xl: 48rpx;
  --radius-full: 50%;
  --radius-round: 9999rpx;
}

/* ==================== 阴影系统 ==================== */
page {
  /* 基础阴影 */
  --shadow-xs: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  --shadow-md: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 16rpx 48rpx rgba(0, 0, 0, 0.2);
  --shadow-xl: 0 24rpx 64rpx rgba(0, 0, 0, 0.25);
  --shadow-2xl: 0 32rpx 80rpx rgba(0, 0, 0, 0.3);

  /* 特殊阴影 */
  --shadow-glass: 0 16rpx 64rpx rgba(0, 0, 0, 0.3);
  --shadow-button: 0 8rpx 24rpx rgba(109, 74, 255, 0.3);
  --shadow-card: 0 12rpx 40rpx rgba(0, 0, 0, 0.2);
  --shadow-modal: 0 20rpx 60rpx rgba(0, 0, 0, 0.4);

  /* 内阴影 */
  --shadow-inset: inset 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  --shadow-inset-lg: inset 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
}

/* ==================== 过渡动画 ==================== */
page {
  /* 过渡时长 */
  --transition-fast: 0.15s;
  --transition-normal: 0.2s;
  --transition-slow: 0.3s;
  --transition-slower: 0.5s;

  /* 缓动函数 */
  --ease-linear: linear;
  --ease-in: ease-in;
  --ease-out: ease-out;
  --ease-in-out: ease-in-out;
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --ease-smooth: cubic-bezier(0.4, 0, 0.2, 1);

  /* 组合过渡 */
  --transition-all: all var(--transition-normal) var(--ease-smooth);
  --transition-transform: transform var(--transition-normal) var(--ease-smooth);
  --transition-opacity: opacity var(--transition-normal) var(--ease-smooth);
  --transition-colors: background-color var(--transition-normal) var(--ease-smooth),
    border-color var(--transition-normal) var(--ease-smooth),
    color var(--transition-normal) var(--ease-smooth);
}

/* ==================== Z-Index 层级 ==================== */
page {
  --z-index-base: 0;
  --z-index-dropdown: 10;
  --z-index-sticky: 20;
  --z-index-fixed: 30;
  --z-index-modal-backdrop: 40;
  --z-index-modal: 50;
  --z-index-popover: 60;
  --z-index-tooltip: 70;
  --z-index-toast: 80;
  --z-index-loading: 90;
  --z-index-max: 9999;
}

/* ==================== 断点系统 ==================== */
page {
  /* 小程序屏幕尺寸 */
  --screen-xs: 320rpx;
  /* iPhone SE */
  --screen-sm: 375rpx;
  /* iPhone 6/7/8 */
  --screen-md: 414rpx;
  /* iPhone 6/7/8 Plus */
  --screen-lg: 768rpx;
  /* iPad Mini */
  --screen-xl: 1024rpx;
  /* iPad */

  /* 容器最大宽度 */
  --container-xs: 100%;
  --container-sm: 100%;
  --container-md: 100%;
  --container-lg: 100%;
  --container-xl: 100%;
}

/* ==================== 组件特定变量 ==================== */
page {
  /* 按钮 */
  --button-height-sm: 64rpx;
  --button-height-md: 80rpx;
  --button-height-lg: 96rpx;
  --button-padding-x: 32rpx;
  --button-padding-y: 16rpx;

  /* 输入框 */
  --input-height: 80rpx;
  --input-padding-x: 32rpx;
  --input-padding-y: 24rpx;
  --input-border-width: 1rpx;

  /* 卡片 */
  --card-padding: var(--spacing-lg);
  --card-radius: var(--radius-md);
  --card-border-width: 1rpx;

  /* 导航栏 */
  --navbar-height: 88rpx;
  --tabbar-height: 168rpx;
  --statusbar-height: 44rpx;

  /* 列表项 */
  --list-item-height: 120rpx;
  --list-item-padding: var(--spacing-md);
  --list-divider-height: 1rpx;

  /* 模态框 */
  --modal-max-width: 640rpx;
  --modal-padding: var(--spacing-xl);
  --modal-radius: var(--radius-lg);

  /* 头像 */
  --avatar-size-xs: 48rpx;
  --avatar-size-sm: 64rpx;
  --avatar-size-md: 80rpx;
  --avatar-size-lg: 96rpx;
  --avatar-size-xl: 128rpx;
}

/* ==================== 安全相关变量 ==================== */
page {
  /* 密码强度颜色 */
  --strength-weak: var(--error-color);
  --strength-medium: var(--warning-color);
  --strength-strong: var(--success-color);
  --strength-very-strong: #00d4aa;

  /* 安全等级颜色 */
  --security-low: var(--error-color);
  --security-medium: var(--warning-color);
  --security-high: var(--success-color);
  --security-excellent: #00d4aa;

  /* 密码显示 */
  --password-mask-char: '•';
  --password-font-family: var(--font-family-mono);
  --password-letter-spacing: 2rpx;
}