/**
 * VaultKeeper 密码管理器 - 小程序应用入口
 * 基于 vue-mini 框架开发
 * 
 * 功能：
 * - 初始化 Pinia 状态管理
 * - 配置全局请求拦截器
 * - 设置应用生命周期钩子
 * - 管理全局数据和配置
 */

import { createApp } from '@vue-mini/core'
import { SecureStorage } from '@/utils/storage'

// 防止死循环的标志
let isInitializing = false

/**
 * 检查应用更新
 */
function checkForUpdates() {
  if (wx.canIUse('getUpdateManager')) {
    const updateManager = wx.getUpdateManager()

    updateManager.onCheckForUpdate((res) => {
      if (res.hasUpdate) {
        console.log('📦 发现新版本')
      }
    })

    updateManager.onUpdateReady(() => {
      wx.showModal({
        title: '更新提示',
        content: '新版本已准备好，是否重启应用？',
        success: (res) => {
          if (res.confirm) {
            updateManager.applyUpdate()
          }
        }
      })
    })

    updateManager.onUpdateFailed(() => {
      console.error('❌ 新版本下载失败')
    })
  }
}

/**
 * 初始化安全设置
 */
function initSecuritySettings() {
  // 检查运行环境
  const accountInfo = wx.getAccountInfoSync()
  const isDevTool = accountInfo.miniProgram.envVersion === 'develop'

  if (isDevTool) {
    console.log('🛠️ 开发者工具环境，跳过生物识别检查')
    wx.setStorageSync('biometric_support', [])
    return
  }

  // 检查是否支持生物识别
  wx.checkIsSupportSoterAuthentication({
    success: (res) => {
      console.log('🔐 生物识别支持:', res.supportMode)
      wx.setStorageSync('biometric_support', res.supportMode)
    },
    fail: (error) => {
      console.warn('⚠️ 生物识别检查失败:', error)
      wx.setStorageSync('biometric_support', [])
    }
  })
}

/**
 * 检查会话有效性
 */
function checkSessionValidity() {
  const lastActiveTime = wx.getStorageSync('last_active_time')
  const sessionTimeout = wx.getStorageSync('session_timeout') || 15 // 默认15分钟

  if (lastActiveTime) {
    const now = Date.now()
    const timeDiff = (now - lastActiveTime) / (1000 * 60) // 转换为分钟

    if (timeDiff > sessionTimeout) {
      console.log('⏰ 会话超时，需要重新认证')
      wx.removeStorageSync('auth_token')
      // 注意：在简化架构中，我们不再跳转到登录页
      // wx.reLaunch({ url: '/pages/login/login' })
    }
  }
}

/**
 * 触发自动锁定
 */
function triggerAutoLock() {
  const autoLockEnabled = wx.getStorageSync('auto_lock_enabled') || true

  if (autoLockEnabled) {
    // 记录应用进入后台的时间
    wx.setStorageSync('background_time', Date.now())
  }
}

/**
 * 初始化系统信息
 */
function initSystemInfo() {
  try {
    // 直接使用 getSystemInfoSync，避免 API 兼容性问题
    const systemInfo = wx.getSystemInfoSync()

    // 获取 app 实例
    const app = getApp()
    if (app && app.globalData) {
      app.globalData.systemInfo = systemInfo
    }

    console.log('📱 系统信息:', systemInfo)
  } catch (error) {
    console.error('❌ 获取系统信息失败:', error)
  }
}

/**
 * 错误上报
 */
function reportError(error: string) {
  // 这里可以接入第三方错误监控服务
  // console.log('📊 错误上报:', error)

  // 本地错误日志记录
  const errorLogs = wx.getStorageSync('error_logs') || []
  const app = getApp()
  const version = app?.globalData?.version || '1.0.0'

  errorLogs.push({
    error,
    timestamp: new Date().toISOString(),
    version
  })

  // 只保留最近50条错误日志
  if (errorLogs.length > 50) {
    errorLogs.splice(0, errorLogs.length - 50)
  }

  wx.setStorageSync('error_logs', errorLogs)
}

// 创建小程序应用
createApp({
  /**
   * 全局数据
   */
  globalData: {
    // 应用版本信息
    version: '1.0.0',
    // 用户信息
    userInfo: null,
    // 系统信息
    systemInfo: null,
    // 应用状态
    appState: 'active',
    // 自定义 tabBar 引用
    tabBar: null as any
  },

  /**
   * 应用启动时触发
   */
  async onLaunch(option: WechatMiniprogram.App.LaunchShowOption) {
    console.log('🚀 KeeWord 应用启动', option)
    
    // 防止重复初始化
    if (isInitializing) {
      console.log('⚠️ 存储系统正在初始化中，跳过重复调用')
      return
    }

    isInitializing = true

    // 初始化存储系统
    try {
      await SecureStorage.autoInitialize()
      console.log('✅ 存储系统初始化成功')
      
      // 清理可能导致解密错误的旧数据
      try {
        SecureStorage.clearIncompatibleData()
      } catch (error) {
        console.warn('⚠️ 清理兼容性数据时出现问题:', error)
      }
    } catch (error) {
      console.error('❌ 存储系统初始化失败:', error)
    } finally {
      isInitializing = false
    }
    
    initSystemInfo()
    checkForUpdates()
    initSecuritySettings()
    checkSessionValidity()
  },

  /**
   * 应用显示时触发
   */
  onShow(option: WechatMiniprogram.App.LaunchShowOption) {
    console.log('👁️ 应用显示', option)
    
    // 更新应用状态
    const app = getApp()
    if (app && app.globalData) {
      app.globalData.appState = 'active'
    }
    
    checkSessionValidity()
  },

  /**
   * 应用隐藏时触发
   */
  onHide() {
    console.log('👤 应用隐藏')
    
    // 更新应用状态
    const app = getApp()
    if (app && app.globalData) {
      app.globalData.appState = 'background'
    }
    
    triggerAutoLock()
  },

  /**
   * 应用错误时触发
   */
  onError(error: string) {
    console.error('❌ 应用错误:', error)
    reportError(error)
  },




})
