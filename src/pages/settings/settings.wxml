<!-- Vault<PERSON>eeper 设置页面
  使用 Tailwind CSS 重构，完美还原原型设计 -->
<view class="min-h-screen bg-gradient-to-br from-dark-primary to-dark-secondary">
  <!-- 页面内容 -->
  <scroll-view class="h-screen pb-40" scroll-y="{{true}}" style="padding-top: {{statusBarHeight + 20}}px;">
    <!-- 用户信息卡片 -->
    <view class="px-5 mb-6">
      <glass-card clickable="{{true}}" padding="24rpx" bind:tap="onProfileTap">
        <view class="flex items-center">
          <view class="w-16 h-16 bg-primary-500 rounded-full flex items-center justify-center mr-4">
            <text class="text-white font-bold text-lg">{{userInfo.initials}}</text>
          </view>
          <view class="flex-1">
            <text class="block text-xl font-bold text-white mb-1">{{userInfo.name}}</text>
            <text class="text-gray-400">{{userInfo.email}}</text>
          </view>
          <view class="w-10 h-10 rounded-lg bg-white/10 flex items-center justify-center">
            <text class="fas fa-pen text-gray-300"></text>
          </view>
        </view>
      </glass-card>
    </view>
    <!-- 账户设置 -->
    <view class="px-5 mb-6">
      <text class="block text-lg font-semibold text-white mb-4 px-1">账户</text>
      <glass-card padding="16rpx">
        <!-- 更改主密码 -->
        <view class="flex items-center p-3 transition-colors duration-200 active:bg-white/5 rounded-lg" bind:tap="onChangeMasterPasswordTap">
          <view class="w-10 h-10 bg-purple-500/20 rounded-lg flex items-center justify-center mr-4">
            <text class="fas fa-key text-purple-400"></text>
          </view>
          <view class="flex-1">
            <text class="text-white font-medium">更改主密码</text>
          </view>
          <text class="fas fa-chevron-right text-gray-400"></text>
        </view>
        <!-- 分隔线 -->
        <view class="h-px bg-white/10 my-2"></view>
        <!-- 生物识别 -->
        <view class="flex items-center p-3">
          <view class="w-10 h-10 bg-blue-500/20 rounded-lg flex items-center justify-center mr-4">
            <text class="fas fa-fingerprint text-blue-400"></text>
          </view>
          <view class="flex-1">
            <text class="text-white font-medium">生物识别</text>
          </view>
          <toggle-switch checked="{{settings.biometricEnabled}}" bind:change="onBiometricToggle" />
        </view>
        <!-- 分隔线 -->
        <view class="h-px bg-white/10 my-2"></view>
        <!-- 云同步 -->
        <view class="flex items-center p-3">
          <view class="w-10 h-10 bg-green-500/20 rounded-lg flex items-center justify-center mr-4">
            <text class="fas fa-cloud text-green-400"></text>
          </view>
          <view class="flex-1">
            <text class="block text-white font-medium">云同步</text>
            <text class="text-gray-400 text-sm">上次同步：{{lastSyncTime}}</text>
          </view>
          <toggle-switch checked="{{settings.cloudSyncEnabled}}" bind:change="onCloudSyncToggle" />
        </view>
      </glass-card>
    </view>
    <!-- 导入/导出 -->
    <view class="px-5 mb-6">
      <text class="block text-lg font-semibold text-white mb-4 px-1">导入 / 导出</text>
      <glass-card padding="16rpx">
        <!-- 导入 -->
        <view class="flex items-center p-3 transition-colors duration-200 active:bg-white/5 rounded-lg" bind:tap="onImportTap">
          <view class="w-10 h-10 bg-indigo-500/20 rounded-lg flex items-center justify-center mr-4">
            <text class="fas fa-download text-indigo-400"></text>
          </view>
          <view class="flex-1">
            <text class="block text-white font-medium">导入</text>
            <text class="text-gray-400 text-sm">支持KeePass、CSV、JSON、文本格式</text>
          </view>
          <text class="fas fa-chevron-right text-gray-400"></text>
        </view>
        <!-- 分隔线 -->
        <view class="h-px bg-white/10 my-2"></view>
        <!-- 导出 -->
        <view class="flex items-center p-3 transition-colors duration-200 active:bg-white/5 rounded-lg" bind:tap="onExportTap">
          <view class="w-10 h-10 bg-purple-500/20 rounded-lg flex items-center justify-center mr-4">
            <text class="fas fa-upload text-purple-400"></text>
          </view>
          <view class="flex-1">
            <text class="block text-white font-medium">导出</text>
            <text class="text-gray-400 text-sm">加密备份您的密码库</text>
          </view>
          <text class="fas fa-chevron-right text-gray-400"></text>
        </view>
      </glass-card>
    </view>
    <!-- 安全设置 -->
    <view class="px-5 mb-6">
      <text class="block text-lg font-semibold text-white mb-4 px-1">安全</text>
      <glass-card padding="16rpx">
        <!-- 自动锁定 -->
        <view class="flex items-center p-3 transition-colors duration-200 active:bg-white/5 rounded-lg" bind:tap="onAutoLockTap">
          <view class="w-10 h-10 bg-yellow-500/20 rounded-lg flex items-center justify-center mr-4">
            <text class="fas fa-lock text-yellow-400"></text>
          </view>
          <view class="flex-1">
            <text class="block text-white font-medium">自动锁定</text>
            <text class="text-gray-400 text-sm">{{autoLockTimeText}}</text>
          </view>
          <text class="fas fa-chevron-right text-gray-400"></text>
        </view>
        <!-- 分隔线 -->
        <view class="h-px bg-white/10 my-2"></view>
        <!-- 安全审计 -->
        <view class="flex items-center p-3 transition-colors duration-200 active:bg-white/5 rounded-lg" bind:tap="onSecurityAuditTap">
          <view class="w-10 h-10 bg-red-500/20 rounded-lg flex items-center justify-center mr-4">
            <text class="fas fa-shield-alt text-red-400"></text>
          </view>
          <view class="flex-1">
            <text class="block text-white font-medium">安全审计</text>
            <text class="text-gray-400 text-sm">{{auditFrequencyText}}</text>
          </view>
          <text class="fas fa-chevron-right text-gray-400"></text>
        </view>
        <!-- 分隔线 -->
        <view class="h-px bg-white/10 my-2"></view>
        <!-- 安全通知 -->
        <view class="flex items-center p-3">
          <view class="w-10 h-10 bg-orange-500/20 rounded-lg flex items-center justify-center mr-4">
            <text class="fas fa-bell text-orange-400"></text>
          </view>
          <view class="flex-1">
            <text class="text-white font-medium">安全通知</text>
          </view>
          <toggle-switch checked="{{settings.securityNotificationEnabled}}" bind:change="onSecurityNotificationToggle" />
        </view>
      </glass-card>
    </view>
    <!-- 其他设置 -->
    <view class="px-5 mb-6">
      <text class="block text-lg font-semibold text-white mb-4 px-1">其他</text>
      <glass-card padding="16rpx">
        <!-- 主题设置 -->
        <view class="flex items-center p-3 transition-colors duration-200 active:bg-white/5 rounded-lg" bind:tap="onThemeTap">
          <view class="w-10 h-10 bg-blue-500/20 rounded-lg flex items-center justify-center mr-4">
            <text class="fas fa-palette text-blue-400"></text>
          </view>
          <view class="flex-1">
            <text class="block text-white font-medium">主题设置</text>
            <text class="text-gray-400 text-sm">{{themeText}}</text>
          </view>
          <text class="fas fa-chevron-right text-gray-400"></text>
        </view>
        <!-- 分隔线 -->
        <view class="h-px bg-white/10 my-2"></view>
        <!-- 帮助与支持 -->
        <view class="flex items-center p-3 transition-colors duration-200 active:bg-white/5 rounded-lg" bind:tap="onHelpTap">
          <view class="w-10 h-10 bg-green-500/20 rounded-lg flex items-center justify-center mr-4">
            <text class="fas fa-question-circle text-green-400"></text>
          </view>
          <view class="flex-1">
            <text class="text-white font-medium">帮助与支持</text>
          </view>
          <text class="fas fa-chevron-right text-gray-400"></text>
        </view>
        <!-- 分隔线 -->
        <view class="h-px bg-white/10 my-2"></view>
        <!-- 关于 -->
        <view class="flex items-center p-3 transition-colors duration-200 active:bg-white/5 rounded-lg" bind:tap="onAboutTap">
          <view class="w-10 h-10 bg-gray-500/20 rounded-lg flex items-center justify-center mr-4">
            <text class="fas fa-info-circle text-gray-400"></text>
          </view>
          <view class="flex-1">
            <text class="block text-white font-medium">关于</text>
            <text class="text-gray-400 text-sm">版本 {{appVersion}}</text>
          </view>
          <text class="fas fa-chevron-right text-gray-400"></text>
        </view>
      </glass-card>
    </view>
    <!-- 退出登录 -->
    <view class="px-5 mb-6">
      <custom-button variant="danger" text="退出登录" block="{{true}}" bind:tap="onLogoutTap" />
    </view>
  </scroll-view>
  <!-- 自动锁定时间选择模态框 -->
  <modal show="{{showAutoLockModal}}" title="自动锁定时间" bind:close="onAutoLockModalClose">
    <view class="p-6">
      <view class="space-y-2">
        <view wx:for="{{autoLockOptions}}" wx:key="value" class="flex items-center justify-between p-3 rounded-lg transition-colors duration-200 {{selectedAutoLockValue === item.value ? 'bg-primary-500/20' : 'active:bg-white/5'}}" bind:tap="onAutoLockOptionSelect" data-value="{{item.value}}">
          <text class="text-white">{{item.label}}</text>
          <text wx:if="{{selectedAutoLockValue === item.value}}" class="fas fa-check text-primary-400"></text>
        </view>
      </view>
    </view>
  </modal>
  <!-- 安全审计频率选择模态框 -->
  <modal show="{{showAuditModal}}" title="安全审计频率" bind:close="onAuditModalClose">
    <view class="p-6">
      <view class="space-y-2">
        <view wx:for="{{auditOptions}}" wx:key="value" class="flex items-center justify-between p-3 rounded-lg transition-colors duration-200 {{selectedAuditValue === item.value ? 'bg-primary-500/20' : 'active:bg-white/5'}}" bind:tap="onAuditOptionSelect" data-value="{{item.value}}">
          <text class="text-white">{{item.label}}</text>
          <text wx:if="{{selectedAuditValue === item.value}}" class="fas fa-check text-primary-400"></text>
        </view>
      </view>
    </view>
  </modal>
  <!-- 主题选择模态框 -->
  <modal show="{{showThemeModal}}" title="主题设置" bind:close="onThemeModalClose">
    <view class="p-6">
      <view class="space-y-3">
        <view wx:for="{{themeOptions}}" wx:key="value" class="flex items-center justify-between p-3 rounded-lg transition-colors duration-200 {{selectedThemeValue === item.value ? 'bg-primary-500/20' : 'active:bg-white/5'}}" bind:tap="onThemeOptionSelect" data-value="{{item.value}}">
          <view class="flex-1">
            <text class="block text-white font-medium">{{item.label}}</text>
            <text class="text-gray-400 text-sm">{{item.description}}</text>
          </view>
          <text wx:if="{{selectedThemeValue === item.value}}" class="fas fa-check text-primary-400 ml-3"></text>
        </view>
      </view>
    </view>
  </modal>
  <!-- 确认退出登录模态框 -->
  <modal show="{{showLogoutModal}}" title="确认退出" bind:close="onLogoutModalClose">
    <view class="p-6">
      <view class="text-center mb-6">
        <text class="block text-white mb-2">确定要退出登录吗？</text>
        <text class="text-gray-400 text-sm">退出后需要重新输入主密码才能访问密码库。</text>
      </view>
      <view class="flex space-x-3">
        <custom-button variant="ghost" text="取消" custom-class="flex-1" bind:tap="onLogoutModalClose" />
        <custom-button variant="danger" text="退出登录" custom-class="flex-1" bind:tap="onLogoutConfirm" />
      </view>
    </view>
  </modal>
</view>