/**
 * VaultKeeper 设置页面
 * 完美还原原型设计，支持用户配置和应用管理
 * 
 * 功能特性：
 * - 用户信息管理
 * - 账户安全设置
 * - 应用偏好配置
 * - 数据导入导出
 * - 主题和外观设置
 */

import { definePage } from '@vue-mini/core'
import { useAuthStore } from '../../store/auth'
import { usePasswordStore } from '../../store/password'
import { useSettingsStore } from '../../store/settings'

/**
 * 自动锁定选项接口
 */
interface AutoLockOption {
  label: string
  value: number // 分钟数，0表示从不
}

/**
 * 审计频率选项接口
 */
interface AuditOption {
  label: string
  value: string
}

/**
 * 主题选项接口
 */
interface ThemeOption {
  label: string
  value: string
  description: string
}

definePage({
  /**
   * 页面数据
   */
  data: {
    // 系统信息
    statusBarHeight: 44,

    // 用户信息
    userInfo: {
      name: '<PERSON>',
      email: '<EMAIL>',
      initials: 'KL'
    },

    // 设置状态
    settings: {
      biometricEnabled: true,
      cloudSyncEnabled: true,
      securityNotificationEnabled: true
    },

    // 时间显示
    lastSyncTime: '今天 16:42',
    autoLockTimeText: '5分钟不活动后',
    auditFrequencyText: '每周扫描',
    themeText: '暗黑模式',
    appVersion: '1.2.3',

    // 模态框状态
    showAutoLockModal: false,
    showAuditModal: false,
    showThemeModal: false,
    showLogoutModal: false,

    // 选项数据
    autoLockOptions: [
      { label: '从不', value: 0 },
      { label: '1分钟', value: 1 },
      { label: '5分钟', value: 5 },
      { label: '15分钟', value: 15 },
      { label: '30分钟', value: 30 },
      { label: '1小时', value: 60 }
    ] as AutoLockOption[],

    auditOptions: [
      { label: '从不', value: 'never' },
      { label: '每天', value: 'daily' },
      { label: '每周', value: 'weekly' },
      { label: '每月', value: 'monthly' }
    ] as AuditOption[],

    themeOptions: [
      { label: '跟随系统', value: 'auto', description: '根据系统设置自动切换' },
      { label: '浅色模式', value: 'light', description: '始终使用浅色主题' },
      { label: '暗黑模式', value: 'dark', description: '始终使用暗黑主题' }
    ] as ThemeOption[],

    // 当前选中值
    selectedAutoLockValue: 5,
    selectedAuditValue: 'weekly',
    selectedThemeValue: 'dark'
  },

  /**
   * 页面设置
   */
  setup() {
    const authStore = useAuthStore()
    const passwordStore = usePasswordStore()
    const settingsStore = useSettingsStore()

    console.log('⚙️ 设置页面初始化')

    /**
     * 页面加载时初始化
     */
    const onLoad = async () => {
      try {
        console.log('📱 初始化设置页面')

        // 检查登录状态
        if (!authStore.isLoggedIn) {
          console.log('❌ 用户未登录，跳转到登录页')
          wx.reLaunch({ url: '/pages/login/login' })
          return
        }

        // 加载用户设置
        await this.loadUserSettings()

        // 更新活跃时间
        await authStore.updateActiveTime()

      } catch (error) {
        console.error('❌ 页面初始化失败:', error)
        wx.showToast({
          title: '初始化失败',
          icon: 'error'
        })
      }
    }

    /**
     * 页面显示时更新数据
     */
    const onShow = async () => {
      try {
        // 获取系统信息 - 使用新的 API
        const windowInfo = wx.getWindowInfo()
        this.setData({
          statusBarHeight: windowInfo.statusBarHeight || 44
        })

        // 设置 tabBar 选中状态
        if (typeof getApp().globalData.tabBar !== 'undefined') {
          getApp().globalData.tabBar.setSelected(3)
        }

        // 检查会话有效性
        await authStore.checkSessionTimeout()
        await authStore.checkAutoLock()

        // 更新活跃时间
        await authStore.updateActiveTime()

        // 更新同步时间显示
        this.updateSyncTimeDisplay()

      } catch (error) {
        console.error('❌ 页面显示更新失败:', error)
      }
    }

    /**
     * 加载用户设置
     */
    const loadUserSettings = async () => {
      try {
        // 从store加载设置
        const userSettings = await settingsStore.getUserSettings()
        const userProfile = await authStore.getUserProfile()

        // 更新用户信息
        if (userProfile) {
          const initials = this.generateInitials(userProfile.name || userProfile.email)
          this.setData({
            userInfo: {
              name: userProfile.name || '用户',
              email: userProfile.email || '',
              initials
            }
          })
        }

        // 更新设置状态
        this.setData({
          settings: {
            biometricEnabled: userSettings.biometricEnabled || false,
            cloudSyncEnabled: userSettings.cloudSyncEnabled || false,
            securityNotificationEnabled: userSettings.securityNotificationEnabled || true
          },
          selectedAutoLockValue: userSettings.autoLockTime || 5,
          selectedAuditValue: userSettings.auditFrequency || 'weekly',
          selectedThemeValue: userSettings.theme || 'dark'
        })

        // 更新显示文本
        this.updateDisplayTexts()

      } catch (error) {
        console.error('❌ 加载用户设置失败:', error)
      }
    }

    /**
     * 生成用户名首字母
     */
    const generateInitials = (name: string): string => {
      if (!name) return 'U'

      const words = name.trim().split(/\s+/)
      if (words.length === 1) {
        return words[0].charAt(0).toUpperCase()
      } else {
        return (words[0].charAt(0) + words[words.length - 1].charAt(0)).toUpperCase()
      }
    }

    /**
     * 更新显示文本
     */
    const updateDisplayTexts = () => {
      const { selectedAutoLockValue, selectedAuditValue, selectedThemeValue } = this.data

      // 更新自动锁定时间文本
      const autoLockOption = this.data.autoLockOptions.find(opt => opt.value === selectedAutoLockValue)
      const autoLockTimeText = autoLockOption ?
        (autoLockOption.value === 0 ? '从不' : `${autoLockOption.label}不活动后`) :
        '5分钟不活动后'

      // 更新审计频率文本
      const auditOption = this.data.auditOptions.find(opt => opt.value === selectedAuditValue)
      const auditFrequencyText = auditOption ? auditOption.label : '每周扫描'

      // 更新主题文本
      const themeOption = this.data.themeOptions.find(opt => opt.value === selectedThemeValue)
      const themeText = themeOption ? themeOption.label : '暗黑模式'

      this.setData({
        autoLockTimeText,
        auditFrequencyText,
        themeText
      })
    }

    /**
     * 更新同步时间显示
     */
    const updateSyncTimeDisplay = () => {
      // 这里可以从设置store获取真实的同步时间
      const now = new Date()
      const timeText = `今天 ${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`

      this.setData({
        lastSyncTime: timeText
      })
    }

    return {
      onLoad,
      onShow,
      loadUserSettings,
      generateInitials,
      updateDisplayTexts,
      updateSyncTimeDisplay
    }
  },

  /**
   * 页面方法
   */
  /**
   * 处理用户信息点击
   */
  onProfileTap() {
    console.log('👤 编辑用户信息')
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    })
  },

  /**
   * 处理更改主密码
   */
  onChangeMasterPasswordTap() {
    console.log('🔑 更改主密码')
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    })
  },

  /**
   * 处理生物识别开关
   */
  onBiometricToggle(event: any) {
    const enabled = event.detail.checked

    this.setData({
      'settings.biometricEnabled': enabled
    })

    // 保存设置
    this.saveSettings()

    console.log('👆 生物识别:', enabled ? '开启' : '关闭')

    wx.showToast({
      title: enabled ? '已开启生物识别' : '已关闭生物识别',
      icon: 'success'
    })
  },

  /**
   * 处理云同步开关
   */
  onCloudSyncToggle(event: any) {
    const enabled = event.detail.checked

    this.setData({
      'settings.cloudSyncEnabled': enabled
    })

    // 保存设置
    this.saveSettings()

    console.log('☁️ 云同步:', enabled ? '开启' : '关闭')

    if (enabled) {
      // 触发同步
      this.triggerCloudSync()
    }

    wx.showToast({
      title: enabled ? '已开启云同步' : '已关闭云同步',
      icon: 'success'
    })
  },

  /**
   * 处理自动锁定设置
   */
  onAutoLockTap() {
    console.log('🔒 设置自动锁定时间')
    this.setData({
      showAutoLockModal: true
    })
  },

  /**
   * 处理自动锁定选项选择
   */
  onAutoLockOptionSelect(event: any) {
    const value = event.currentTarget.dataset.value

    this.setData({
      selectedAutoLockValue: value,
      showAutoLockModal: false
    })

    // 更新显示文本
    this.updateDisplayTexts()

    // 保存设置
    this.saveSettings()

    console.log('🔒 自动锁定时间设置为:', value)
  },

  /**
   * 关闭自动锁定模态框
   */
  onAutoLockModalClose() {
    this.setData({
      showAutoLockModal: false
    })
  },

  /**
   * 处理安全审计设置
   */
  onSecurityAuditTap() {
    console.log('🛡️ 设置安全审计频率')
    this.setData({
      showAuditModal: true
    })
  },

  /**
   * 处理安全审计选项选择
   */
  onAuditOptionSelect(event: any) {
    const value = event.currentTarget.dataset.value

    this.setData({
      selectedAuditValue: value,
      showAuditModal: false
    })

    // 更新显示文本
    this.updateDisplayTexts()

    // 保存设置
    this.saveSettings()

    console.log('🛡️ 安全审计频率设置为:', value)
  },

  /**
   * 关闭安全审计模态框
   */
  onAuditModalClose() {
    this.setData({
      showAuditModal: false
    })
  },

  /**
   * 处理安全通知开关
   */
  onSecurityNotificationToggle(event: any) {
    const enabled = event.detail.checked

    this.setData({
      'settings.securityNotificationEnabled': enabled
    })

    // 保存设置
    this.saveSettings()

    console.log('🔔 安全通知:', enabled ? '开启' : '关闭')

    wx.showToast({
      title: enabled ? '已开启安全通知' : '已关闭安全通知',
      icon: 'success'
    })
  },

  /**
   * 处理导入数据
   */
  onImportTap() {
    console.log('📥 导入数据')

    // 显示导入选项
    wx.showActionSheet({
      itemList: ['导入 KeePass 数据库', '导入 CSV 文件', '导入 JSON 文件', '导入文本文件'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            // 导入 KeePass
            this.onImportKeePass()
            break
          case 1:
            // 导入 CSV
            this.onImportCSV()
            break
          case 2:
            // 导入 JSON
            this.onImportJSON()
            break
          case 3:
            // 导入文本
            this.onImportText()
            break
        }
      },
      fail: (error) => {
        console.log('用户取消选择')
      }
    })
  },

  /**
   * 导入 KeePass 数据库
   */
  onImportKeePass() {
    console.log('📥 导入 KeePass 数据库')
    wx.navigateTo({
      url: '/pages/import-keepass/import-keepass',
      success: () => {
        console.log('✅ 跳转到 KeePass 导入页面成功')
      },
      fail: (error) => {
        console.error('❌ 跳转到 KeePass 导入页面失败:', error)
        wx.showToast({
          title: '页面跳转失败',
          icon: 'error'
        })
      }
    })
  },

  /**
   * 导入 CSV 文件
   */
  onImportCSV() {
    console.log('📥 导入 CSV 文件')
    wx.navigateTo({
      url: '/pages/import-keepass/import-keepass?type=csv',
      success: () => {
        console.log('✅ 跳转到 CSV 导入页面成功')
      },
      fail: (error) => {
        console.error('❌ 跳转到 CSV 导入页面失败:', error)
        wx.showToast({
          title: '页面跳转失败',
          icon: 'error'
        })
      }
    })
  },
  /**
   * 导入 JSON 文件
   */
  onImportJSON() {
    console.log('📥 导入 JSON 文件')
    wx.navigateTo({
      url: '/pages/import-keepass/import-keepass?type=json',
      success: () => {
        console.log('✅ 跳转到 JSON 导入页面成功')
      },
      fail: (error) => {
        console.error('❌ 跳转到 JSON 导入页面失败:', error)
        wx.showToast({
          title: '页面跳转失败',
          icon: 'error'
        })
      }
    })
  },
  /**
   * 导入文本文件
   */
  onImportText() {
    console.log('📥 导入文本文件')
    wx.navigateTo({
      url: '/pages/import-keepass/import-keepass?type=text',
      success: () => {
        console.log('✅ 跳转到文本导入页面成功')
      },
      fail: (error) => {
        console.error('❌ 跳转到文本导入页面失败:', error)
        wx.showToast({
          title: '页面跳转失败',
          icon: 'error'
        })
      }
    })
  },

  /**
   * 处理导出数据
   */
  onExportTap() {
    console.log('📤 导出数据')
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    })
  },

  /**
   * 处理主题设置
   */
  onThemeTap() {
    console.log('🎨 设置主题')
    this.setData({
      showThemeModal: true
    })
  },

  /**
   * 处理主题选项选择
   */
  onThemeOptionSelect(event: any) {
    const value = event.currentTarget.dataset.value

    this.setData({
      selectedThemeValue: value,
      showThemeModal: false
    })

    // 更新显示文本
    this.updateDisplayTexts()

    // 保存设置
    this.saveSettings()

    console.log('🎨 主题设置为:', value)

    wx.showToast({
      title: '主题设置已保存',
      icon: 'success'
    })
  },

  /**
   * 关闭主题模态框
   */
  onThemeModalClose() {
    this.setData({
      showThemeModal: false
    })
  },

  /**
   * 处理帮助与支持
   */
  onHelpTap() {
    console.log('❓ 帮助与支持')
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    })
  },

  /**
   * 处理关于页面
   */
  onAboutTap() {
    console.log('ℹ️ 关于应用')
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    })
  },

  /**
   * 处理退出登录
   */
  onLogoutTap() {
    console.log('🚪 退出登录')
    this.setData({
      showLogoutModal: true
    })
  },

  /**
   * 确认退出登录
   */
  async onLogoutConfirm() {
    try {
      this.setData({
        showLogoutModal: false
      })

      // 显示加载提示
      wx.showLoading({
        title: '正在退出...',
        mask: true
      })

      // 执行退出登录
      await useAuthStore().logout()

      wx.hideLoading()

      // 跳转到登录页
      wx.reLaunch({
        url: '/pages/login/login'
      })

      console.log('🚪 退出登录成功')

    } catch (error) {
      wx.hideLoading()
      console.error('❌ 退出登录失败:', error)

      wx.showToast({
        title: '退出失败',
        icon: 'error'
      })
    }
  },

  /**
   * 关闭退出登录模态框
   */
  onLogoutModalClose() {
    this.setData({
      showLogoutModal: false
    })
  },

  /**
   * 保存设置
   */
  async saveSettings() {
    try {
      const { settings, selectedAutoLockValue, selectedAuditValue, selectedThemeValue } = this.data

      const settingsData = {
        ...settings,
        autoLockTime: selectedAutoLockValue,
        auditFrequency: selectedAuditValue,
        theme: selectedThemeValue
      }

      await useSettingsStore().updateUserSettings(settingsData)

      console.log('💾 设置保存成功')

    } catch (error) {
      console.error('❌ 设置保存失败:', error)
    }
  },

  /**
   * 触发云同步
   */
  async triggerCloudSync() {
    try {
      // 这里可以实现真实的云同步逻辑
      console.log('☁️ 开始云同步...')

      // 模拟同步过程
      setTimeout(() => {
        this.updateSyncTimeDisplay()
        console.log('☁️ 云同步完成')
      }, 2000)

    } catch (error) {
      console.error('❌ 云同步失败:', error)
    }
  }
})
