/**
 * VaultKeeper 密码生成器页面
 * 完美还原原型设计，支持密码生成和配置
 */

import { definePage, ref, onLoad, onShow } from '@vue-mini/core'
import { usePasswordStore } from '../../store/password'
import { CryptoUtils } from '../../utils/crypto'

definePage({
  setup() {
    const passwordStore = usePasswordStore()

    console.log('🔑 密码生成器页面初始化')

    // 系统信息
    const statusBarHeight = ref(44) // 默认状态栏高度

    // 响应式数据
    const generatedPassword = ref('')
    const passwordStrength = ref(null)
    const passwordLength = ref(16)
    const minLength = ref(4)
    const maxLength = ref(64)

    const options = ref({
      includeUppercase: true,
      includeLowercase: true,
      includeNumbers: true,
      includeSymbols: true,
      excludeSimilar: false
    })

    const showAdvanced = ref(false)
    const customCharacters = ref('')
    const excludeCharacters = ref('')
    const passwordHistory = ref([])
    const maxHistoryCount = ref(10)
    const isCallback = ref(false)

    // 定时器
    let customCharactersTimer = null
    let excludeCharactersTimer = null
    let isGenerating = ref(false) // 防止重复生成

    /**
     * 生成密码
     */
    const generatePassword = () => {
      // 防止重复生成
      if (isGenerating.value) {
        console.log('🔑 密码正在生成中，跳过重复请求')
        return
      }

      try {
        isGenerating.value = true

        // 构建字符集
        let charset = ''

        if (customCharacters.value) {
          charset = customCharacters.value
        } else {
          if (options.value.includeUppercase) {
            charset += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
          }
          if (options.value.includeLowercase) {
            charset += 'abcdefghijklmnopqrstuvwxyz'
          }
          if (options.value.includeNumbers) {
            charset += '0123456789'
          }
          if (options.value.includeSymbols) {
            charset += '!@#$%^&*()_+-=[]{}|;:,.<>?'
          }
        }

        // 排除相似字符
        if (options.value.excludeSimilar) {
          charset = charset.replace(/[1lI0O]/g, '')
        }

        // 排除自定义字符
        if (excludeCharacters.value) {
          for (const char of excludeCharacters.value) {
            charset = charset.replace(new RegExp(char.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), '')
          }
        }

        if (!charset) {
          wx.showToast({
            title: '请至少选择一种字符类型',
            icon: 'error'
          })
          return
        }

        // 生成密码
        const password = CryptoUtils.generatePassword(passwordLength.value, charset)
        const strength = CryptoUtils.calculateStrength(password)

        generatedPassword.value = password
        passwordStrength.value = strength

        // 添加到历史记录
        addToHistory(password)

        console.log('🔑 密码生成成功:', { password, strength })

      } catch (error) {
        console.error('❌ 密码生成失败:', error)
        wx.showToast({
          title: '生成失败',
          icon: 'error'
        })
      } finally {
        // 延迟重置生成状态，避免过快的连续调用
        setTimeout(() => {
          isGenerating.value = false
        }, 100)
      }
    }

    /**
     * 添加到历史记录
     */
    const addToHistory = (password: string) => {
      const now = new Date()
      const timeText = formatTime(now)

      const historyItem = {
        id: Date.now().toString(),
        password,
        timestamp: now.getTime(),
        timeText
      }

      let history = [...passwordHistory.value]

      // 检查是否已存在相同密码
      const existingIndex = history.findIndex(item => item.password === password)
      if (existingIndex >= 0) {
        history.splice(existingIndex, 1)
      }

      // 添加到开头
      history.unshift(historyItem)

      // 限制历史记录数量
      if (history.length > maxHistoryCount.value) {
        history = history.slice(0, maxHistoryCount.value)
      }

      passwordHistory.value = history
      savePasswordHistory(history)
    }

    /**
     * 格式化时间
     */
    const formatTime = (date: Date): string => {
      const now = new Date()
      const diff = now.getTime() - date.getTime()

      if (diff < 60000) {
        return '刚刚'
      } else if (diff < 3600000) {
        return `${Math.floor(diff / 60000)}分钟前`
      } else if (diff < 86400000) {
        return `${Math.floor(diff / 3600000)}小时前`
      } else {
        return `${date.getMonth() + 1}月${date.getDate()}日`
      }
    }

    /**
     * 加载历史记录
     */
    const loadPasswordHistory = () => {
      try {
        const history = wx.getStorageSync('password_generator_history') || []
        const updatedHistory = history.map((item) => ({
          ...item,
          timeText: formatTime(new Date(item.timestamp))
        }))
        passwordHistory.value = updatedHistory
      } catch (error) {
        console.error('❌ 加载历史记录失败:', error)
      }
    }

    /**
     * 保存历史记录
     */
    const savePasswordHistory = (history: any[]) => {
      try {
        wx.setStorageSync('password_generator_history', history)
      } catch (error) {
        console.error('❌ 保存历史记录失败:', error)
      }
    }

    /**
     * 页面加载时初始化
     */
    onLoad(async (options: any) => {
      try {
        console.log('📱 初始化密码生成器页面', options)

        const callbackMode = options?.callback === 'true'
        isCallback.value = callbackMode

        loadPasswordHistory()
        generatePassword()

        console.log('✅ 密码生成器页面初始化完成')

      } catch (error) {
        console.error('❌ 页面初始化失败:', error)
        wx.showToast({
          title: '初始化失败',
          icon: 'error'
        })
      }
    })

    /**
     * 页面显示时更新数据
     */
    onShow(async () => {
      try {
        // 获取系统信息
        const systemInfo = wx.getSystemInfoSync()
        statusBarHeight.value = systemInfo.statusBarHeight || 44

        // 设置 tabBar 选中状态
        if (typeof getApp().globalData.tabBar !== 'undefined') {
          getApp().globalData.tabBar.setSelected(1)
        }

        console.log('👁️ 密码生成器页面显示')
      } catch (error) {
        console.error('❌ 页面显示更新失败:', error)
      }
    })

    /**
     * 处理重新生成
     */
    const onRegenerateTap = () => {
      console.log('🔄 重新生成密码')
      generatePassword()

      // 触觉反馈
      if (wx.vibrateShort) {
        wx.vibrateShort({ type: 'light' })
      }
    }

    /**
     * 处理复制
     */
    const onCopyTap = () => {
      if (!generatedPassword.value) {
        wx.showToast({
          title: '没有可复制的密码',
          icon: 'error'
        })
        return
      }

      wx.setClipboardData({
        data: generatedPassword.value,
        success: () => {
          wx.showToast({
            title: '已复制到剪贴板',
            icon: 'success'
          })

          console.log('📋 密码复制成功:', generatedPassword.value)

          // 触觉反馈
          if (wx.vibrateShort) {
            wx.vibrateShort({ type: 'medium' })
          }
        },
        fail: (error) => {
          console.error('❌ 复制失败:', error)
          wx.showToast({
            title: '复制失败',
            icon: 'error'
          })
        }
      })
    }

    // 长度变化定时器
    let lengthChangeTimer = null

    /**
     * 处理长度减少
     */
    const onLengthDecrease = () => {
      const newLength = Math.max(passwordLength.value - 1, minLength.value)
      if (newLength === passwordLength.value) {
        return // 已经是最小值，不需要更新
      }

      passwordLength.value = newLength

      // 延迟生成，避免连续点击时频繁触发
      if (lengthChangeTimer) {
        clearTimeout(lengthChangeTimer)
      }
      lengthChangeTimer = setTimeout(() => {
        generatePassword()
      }, 200)
    }

    /**
     * 处理长度增加
     */
    const onLengthIncrease = () => {
      const newLength = Math.min(passwordLength.value + 1, maxLength.value)
      if (newLength === passwordLength.value) {
        return // 已经是最大值，不需要更新
      }

      passwordLength.value = newLength

      // 延迟生成，避免连续点击时频繁触发
      if (lengthChangeTimer) {
        clearTimeout(lengthChangeTimer)
      }
      lengthChangeTimer = setTimeout(() => {
        generatePassword()
      }, 200)
    }

    /**
     * 处理长度滑块变化
     */
    const onLengthSliderChange = (event: any) => {
      const newLength = event.detail.value
      if (newLength === passwordLength.value) {
        return // 值没有变化，不需要更新
      }

      passwordLength.value = newLength

      // 滑块变化时立即生成，但也要防抖
      if (lengthChangeTimer) {
        clearTimeout(lengthChangeTimer)
      }
      lengthChangeTimer = setTimeout(() => {
        generatePassword()
      }, 100)
    }

    /**
     * 处理大写字母开关
     */
    const onUppercaseToggle = (event: any) => {
      const checked = event.detail.checked
      console.log('🔤 大写字母开关切换:', checked)

      options.value.includeUppercase = checked

      // 延迟生成，避免频繁触发
      setTimeout(() => {
        generatePassword()
      }, 50)
    }

    /**
     * 处理小写字母开关
     */
    const onLowercaseToggle = (event: any) => {
      const checked = event.detail.checked
      console.log('🔤 小写字母开关切换:', checked)

      options.value.includeLowercase = checked

      // 延迟生成，避免频繁触发
      setTimeout(() => {
        generatePassword()
      }, 50)
    }

    /**
     * 处理数字开关
     */
    const onNumbersToggle = (event: any) => {
      const checked = event.detail.checked
      console.log('🔢 数字开关切换:', checked)

      options.value.includeNumbers = checked

      // 延迟生成，避免频繁触发
      setTimeout(() => {
        generatePassword()
      }, 50)
    }

    /**
     * 处理特殊符号开关
     */
    const onSymbolsToggle = (event: any) => {
      const checked = event.detail.checked
      console.log('🔣 特殊符号开关切换:', checked)

      options.value.includeSymbols = checked

      // 延迟生成，避免频繁触发
      setTimeout(() => {
        generatePassword()
      }, 50)
    }

    /**
     * 处理排除相似字符开关
     */
    const onExcludeSimilarToggle = (event: any) => {
      const checked = event.detail.checked
      console.log('🚫 排除相似字符开关切换:', checked)

      options.value.excludeSimilar = checked

      // 延迟生成，避免频繁触发
      setTimeout(() => {
        generatePassword()
      }, 50)
    }

    /**
     * 处理高级选项切换
     */
    const onAdvancedToggle = () => {
      showAdvanced.value = !showAdvanced.value
    }

    /**
     * 处理自定义字符输入
     */
    const onCustomCharactersInput = (event) => {
      const value = event.detail.value
      customCharacters.value = value

      // 延迟生成，避免频繁触发
      if (customCharactersTimer) {
        clearTimeout(customCharactersTimer)
      }
      customCharactersTimer = setTimeout(() => {
        generatePassword()
      }, 500)
    }

    /**
     * 处理排除字符输入
     */
    const onExcludeCharactersInput = (event) => {
      const value = event.detail.value
      excludeCharacters.value = value

      // 延迟生成，避免频繁触发
      if (excludeCharactersTimer) {
        clearTimeout(excludeCharactersTimer)
      }
      excludeCharactersTimer = setTimeout(() => {
        generatePassword()
      }, 500)
    }

    /**
     * 处理保存密码
     */
    const onSavePasswordTap = () => {
      if (!generatedPassword.value) {
        wx.showToast({
          title: '没有可保存的密码',
          icon: 'error'
        })
        return
      }

      if (isCallback.value) {
        // 回调模式：返回密码给调用页面
        const pages = getCurrentPages()
        const prevPage = pages[pages.length - 2] as any

        if (prevPage && prevPage.setData) {
          // 设置密码到上一页
          prevPage.setData({
            'formData.password': generatedPassword.value
          })

          // 计算密码强度
          if (prevPage.calculatePasswordStrength) {
            prevPage.calculatePasswordStrength(generatedPassword.value)
          }

          // 验证表单
          if (prevPage.validateForm) {
            prevPage.validateForm()
          }
        }

        wx.navigateBack({
          success: () => {
            wx.showToast({
              title: '密码已应用',
              icon: 'success'
            })
          }
        })
      } else {
        // 普通模式：跳转到添加密码页面
        wx.navigateTo({
          url: `/pages/add-password/add-password?password=${encodeURIComponent(generatedPassword.value)}`
        })
      }
    }

    /**
     * 处理历史记录点击
     */
    const onHistoryItemTap = (event) => {
      const password = event.currentTarget.dataset.password

      generatedPassword.value = password
      passwordStrength.value = CryptoUtils.calculateStrength(password)

      console.log('📋 选择历史密码:', password)
    }

    /**
     * 处理历史记录复制
     */
    const onHistoryCopyTap = (event) => {
      event.stopPropagation() // 阻止冒泡

      const password = event.currentTarget.dataset.password

      wx.setClipboardData({
        data: password,
        success: () => {
          wx.showToast({
            title: '已复制到剪贴板',
            icon: 'success'
          })

          console.log('📋 历史密码复制成功:', password)

          // 触觉反馈
          if (wx.vibrateShort) {
            wx.vibrateShort({ type: 'light' })
          }
        },
        fail: (error) => {
          console.error('❌ 复制失败:', error)
          wx.showToast({
            title: '复制失败',
            icon: 'error'
          })
        }
      })
    }

    /**
     * 处理清空历史记录
     */
    const onClearHistoryTap = () => {
      wx.showModal({
        title: '确认清空',
        content: '确定要清空所有生成历史吗？',
        success: (res) => {
          if (res.confirm) {
            passwordHistory.value = []
            savePasswordHistory([])

            wx.showToast({
              title: '已清空历史记录',
              icon: 'success'
            })

            console.log('🗑️ 清空历史记录')
          }
        }
      })
    }

    return {
      // 响应式数据
      statusBarHeight,
      generatedPassword,
      passwordStrength,
      passwordLength,
      minLength,
      maxLength,
      options,
      showAdvanced,
      customCharacters,
      excludeCharacters,
      passwordHistory,
      maxHistoryCount,
      isCallback,
      isGenerating,

      // 方法
      generatePassword,
      addToHistory,
      formatTime,
      loadPasswordHistory,
      savePasswordHistory,
      onRegenerateTap,
      onCopyTap,
      onLengthDecrease,
      onLengthIncrease,
      onLengthSliderChange,
      onUppercaseToggle,
      onLowercaseToggle,
      onNumbersToggle,
      onSymbolsToggle,
      onExcludeSimilarToggle,
      onAdvancedToggle,
      onCustomCharactersInput,
      onExcludeCharactersInput,
      onSavePasswordTap,
      onHistoryItemTap,
      onHistoryCopyTap,
      onClearHistoryTap
    }
  }
})