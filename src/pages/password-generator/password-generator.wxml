<!-- VaultKeeper 密码生成器页面
  使用 Tailwind CSS 重构，完美还原原型设计 -->
<view class="min-h-screen bg-gradient-to-br from-dark-primary to-dark-secondary">
  <!-- 页面内容 -->
  <scroll-view class="h-screen pb-40" scroll-y="{{true}}" style="padding-top: {{statusBarHeight + 20}}px;">
    <!-- 页面头部 -->
    <view class="text-center pt-8 pb-8 px-5">
      <text class="text-2xl font-bold text-white mb-2">密码生成器</text>
      <text class="text-gray-400">创建安全且强大的密码</text>
    </view>
    <!-- 生成的密码显示区域 -->
    <view class="px-5 mb-6">
      <glass-card padding="32rpx">
        <!-- 密码显示 - 居中布局 -->
        <view class="text-center mb-6">
          <view class="text-xl font-mono tracking-wide break-all text-white mb-4" selectable="{{true}}">
            {{generatedPassword || '点击生成密码'}}
          </view>
          <!-- 密码强度指示器 -->
          <view wx:if="{{generatedPassword}}" class="mb-4">
            <strength-meter strength="{{passwordStrength}}" show-feedback="{{true}}" />
          </view>
        </view>
        <!-- 操作按钮 -->
        <view class="flex space-x-3">
          <custom-button variant="secondary" text="重新生成" left-icon="redo" custom-class="flex-1" bind:tap="onRegenerateTap" />
          <custom-button variant="primary" text="复制" left-icon="copy" disabled="{{!generatedPassword}}" custom-class="flex-1" bind:tap="onCopyTap" />
        </view>
      </glass-card>
    </view>
    <!-- 密码设置 -->
    <view class="px-5 mb-6">
      <text class="block text-lg font-semibold text-white mb-4 px-1">密码设置</text>
      <glass-card padding="24rpx">
        <!-- 密码长度设置 -->
        <view class="mb-6">
          <view class="flex items-center justify-between mb-4">
            <view>
              <text class="block text-white font-medium mb-1">密码长度</text>
              <text class="text-gray-400 text-sm">{{passwordLength}}个字符</text>
            </view>
            <view class="flex items-center space-x-3">
              <view class="w-8 h-8 rounded-full bg-white/10 flex items-center justify-center transition-colors duration-200 {{passwordLength <= minLength ? 'opacity-50' : 'active:bg-white/20'}}" bind:tap="onLengthDecrease">
                <text class="fas fa-minus text-white text-sm"></text>
              </view>
              <text class="text-white font-bold text-lg min-w-[48rpx] text-center">
                {{passwordLength}}
              </text>
              <view class="w-8 h-8 rounded-full bg-white/10 flex items-center justify-center transition-colors duration-200 {{passwordLength >= maxLength ? 'opacity-50' : 'active:bg-white/20'}}" bind:tap="onLengthIncrease">
                <text class="fas fa-plus text-white text-sm"></text>
              </view>
            </view>
          </view>
          <!-- 长度滑块 -->
          <view class="mb-6">
            <slider min="{{minLength}}" max="{{maxLength}}" value="{{passwordLength}}" activeColor="#8b5cf6" backgroundColor="rgba(255, 255, 255, 0.1)" block-color="#8b5cf6" block-size="20" bind:change="onLengthSliderChange" />
          </view>
        </view>
        <!-- 字符类型选项 -->
        <view class="space-y-4">
          <!-- 大写字母 -->
          <view class="flex items-center justify-between">
            <text class="text-white">大写字母 (A-Z)</text>
            <toggle-switch checked="{{options.includeUppercase}}" bind:change="onUppercaseToggle" />
          </view>
          <!-- 小写字母 -->
          <view class="flex items-center justify-between">
            <text class="text-white">小写字母 (a-z)</text>
            <toggle-switch checked="{{options.includeLowercase}}" bind:change="onLowercaseToggle" />
          </view>
          <!-- 数字 -->
          <view class="flex items-center justify-between">
            <text class="text-white">数字 (0-9)</text>
            <toggle-switch checked="{{options.includeNumbers}}" bind:change="onNumbersToggle" />
          </view>
          <!-- 特殊符号 -->
          <view class="flex items-center justify-between">
            <text class="text-white">特殊符号 (!@#$%)</text>
            <toggle-switch checked="{{options.includeSymbols}}" bind:change="onSymbolsToggle" />
          </view>
          <!-- 避免类似字符 -->
          <view class="flex items-center justify-between">
            <text class="text-white">避免类似字符 (1, l, I)</text>
            <toggle-switch checked="{{options.excludeSimilar}}" bind:change="onExcludeSimilarToggle" />
          </view>
        </view>
      </glass-card>
    </view>
    <!-- 高级选项 -->
    <view class="px-5 mb-6">
      <glass-card clickable="{{true}}" padding="24rpx" bind:tap="onAdvancedToggle">
        <view class="flex items-center justify-between mb-4">
          <text class="text-white font-medium">高级选项</text>
          <text class="fas fa-chevron-{{showAdvanced ? 'up' : 'down'}} text-gray-400 transition-transform duration-200"></text>
        </view>
        <!-- 高级选项内容 -->
        <view wx:if="{{showAdvanced}}" class="space-y-4">
          <!-- 自定义字符集 -->
          <view>
            <text class="block text-white mb-2">自定义字符集</text>
            <textarea class="w-full p-3 bg-dark-input/70 border border-white/10 rounded-xl text-white resize-none" placeholder="输入自定义字符..." value="{{customCharacters}}" maxlength="100" bind:input="onCustomCharactersInput" />
          </view>
          <!-- 排除字符 -->
          <view>
            <text class="block text-white mb-2">排除字符</text>
            <textarea class="w-full p-3 bg-dark-input/70 border border-white/10 rounded-xl text-white resize-none" placeholder="输入要排除的字符..." value="{{excludeCharacters}}" maxlength="50" bind:input="onExcludeCharactersInput" />
          </view>
        </view>
      </glass-card>
    </view>
    <!-- 保存按钮 -->
    <view class="px-5 mb-6">
      <custom-button variant="primary" text="保存为新密码" left-icon="save" block="{{true}}" disabled="{{!generatedPassword}}" bind:tap="onSavePasswordTap" />
    </view>
    <!-- 生成历史 -->
    <view wx:if="{{passwordHistory.length > 0}}" class="px-5 mb-6">
      <view class="flex items-center justify-between mb-4 px-1">
        <text class="text-lg font-semibold text-white">生成历史</text>
        <custom-button variant="text" size="sm" text="清空" bind:tap="onClearHistoryTap" />
      </view>
      <view class="space-y-3">
        <glass-card wx:for="{{passwordHistory}}" wx:key="id" clickable="{{true}}" padding="20rpx" bind:tap="onHistoryItemTap" data-password="{{item.password}}">
          <view class="flex items-center justify-between">
            <view class="flex-1 mr-3">
              <text class="block text-white font-mono text-sm mb-2 break-all" selectable="{{true}}">
                {{item.password}}
              </text>
              <text class="text-gray-400 text-xs">{{item.timeText}}</text>
            </view>
            <view class="w-10 h-10 rounded-lg bg-white/10 flex items-center justify-center transition-colors duration-200 active:bg-white/20" bind:tap="onHistoryCopyTap" data-password="{{item.password}}" catch:tap="true">
              <text class="far fa-copy text-gray-300"></text>
            </view>
          </view>
        </glass-card>
      </view>
    </view>
    <!-- 空状态 -->
    <view wx:else class="px-5 mb-6">
      <view class="text-center py-12">
        <text class="block text-gray-400 mb-2">暂无生成历史</text>
        <text class="text-gray-500 text-sm">生成的密码将显示在这里</text>
      </view>
    </view>
  </scroll-view>
</view>