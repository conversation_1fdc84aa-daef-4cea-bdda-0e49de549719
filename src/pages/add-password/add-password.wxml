<!-- VaultKeeper 添加/编辑密码页面
  使用 Tailwind CSS 重构，完美还原原型设计 -->
<view class="min-h-screen bg-gradient-to-br from-dark-primary to-dark-secondary">
  <!-- 自定义导航栏 -->
  <glass-card custom-class="fixed top-0 left-0 right-0 z-50 border-b border-white/10" custom-style="padding-top: {{statusBarHeight}}px;">
    <view class="flex items-center justify-between px-4 py-3">
      <!-- 返回按钮 -->
      <view class="w-10 h-10 rounded-lg bg-white/10 flex items-center justify-center transition-colors duration-200 active:bg-white/20" bind:tap="onBackTap">
        <text class="fas fa-arrow-left text-white"></text>
      </view>
      <!-- 标题 -->
      <text class="text-lg font-semibold text-white">{{isEditMode ? '编辑密码' : '添加密码'}}</text>
      <!-- 保存按钮 -->
      <view class="px-2">
        <custom-button variant="success" size="sm" text="保存" loading="{{saveLoading}}" disabled="{{!canSave}}" bind:tap="onSaveTap" custom-class="min-w-20" />
      </view>
    </view>
  </glass-card>
  <!-- 页面内容 -->
  <scroll-view class="h-screen pb-40" scroll-y="{{true}}" style="padding-top: {{statusBarHeight + 88}}px;">
    <!-- 图标选择区域 -->
    <view class="px-5 mb-6">
      <glass-card padding="24rpx">
        <view class="text-center" bind:tap="onIconTap">
          <view class="w-20 h-20 rounded-2xl mx-auto mb-3 flex items-center justify-center text-3xl bg-gray-600/20">
            <text class="fas fa-{{iconName || 'globe'}} text-gray-400"></text>
          </view>
        </view>
      </glass-card>
    </view>
    <!-- 基本信息 -->
    <view class="px-5 mb-6">
      <glass-card padding="24rpx">
        <!-- 类型选择 -->
        <view class="mb-6">
          <text class="block text-white font-medium mb-3">类型</text>
          <picker mode="selector" range="{{categoryOptions}}" range-key="name" value="{{selectedCategoryIndex}}" bind:change="onCategoryChange">
            <view class="flex items-center justify-between p-3 bg-dark-input/70 border border-white/10 rounded-xl">
              <text class="text-white">{{selectedCategory.name || '网站'}}</text>
              <text class="fas fa-chevron-down text-gray-400"></text>
            </view>
          </picker>
        </view>
        <!-- 名称输入 -->
        <view class="mb-6">
          <form-input label="名称" placeholder="例如：Gmail, 淘宝..." value="{{formData.title}}" error="{{errors.title}}" required="{{true}}" bind:input="onTitleInput" bind:blur="onTitleBlur" />
        </view>
      </glass-card>
    </view>
    <!-- 账户信息 -->
    <view class="px-5 mb-6">
      <glass-card padding="24rpx">
        <!-- 用户名/邮箱 -->
        <view class="mb-6">
          <form-input label="用户名/邮箱" placeholder="输入账号用户名或邮箱..." value="{{formData.username}}" error="{{errors.username}}" bind:input="onUsernameInput" bind:blur="onUsernameBlur" />
        </view>
        <!-- 密码输入 -->
        <view class="mb-8">
          <view class="flex items-center justify-between mb-4">
            <text class="text-white font-medium text-lg">密码</text>
            <view class="px-4 py-2 bg-purple-500/20 rounded-lg transition-all duration-200 active:bg-purple-500/30 active:scale-95" bind:tap="onGeneratePasswordTap">
              <text class="fas fa-magic text-purple-400 mr-2"></text>
              <text class="text-purple-400 text-sm font-medium">生成密码</text>
            </view>
          </view>
          <view class="relative mb-4">
            <input type="{{passwordVisible ? 'text' : 'password'}}" class="w-full px-5 py-4 bg-dark-input/80 border-2 border-white/10 focus:border-purple-400/50 rounded-xl text-white pr-20 text-base font-mono tracking-wider transition-all duration-200" placeholder="输入密码..." value="{{formData.password}}" bind:input="onPasswordInput" bind:blur="onPasswordBlur" bind:focus="onPasswordFocus" />
            <view class="absolute right-4 top-1/2 transform -translate-y-1/2 w-10 h-10 rounded-lg bg-white/10 flex items-center justify-center transition-all duration-200 active:bg-white/20 active:scale-95" bind:tap="onPasswordVisibilityToggle">
              <text class="far fa-{{passwordVisible ? 'eye-slash' : 'eye'}} text-gray-400 text-lg"></text>
            </view>
          </view>
          <!-- 密码强度指示器 -->
          <view wx:if="{{formData.password}}" class="mt-4">
            <strength-meter strength="{{passwordStrength}}" show-feedback="{{true}}" />
          </view>
        </view>
        <!-- 网址输入 -->
        <view class="mb-6">
          <form-input label="网址" placeholder="https://..." value="{{formData.website}}" error="{{errors.website}}" bind:input="onWebsiteInput" bind:blur="onWebsiteBlur" />
        </view>
      </glass-card>
    </view>
    <!-- 高级选项 -->
    <view class="px-5 mb-6">
      <glass-card padding="24rpx">
        <!-- 双因素认证 -->
        <view class="flex items-center justify-between mb-4">
          <text class="text-white font-medium">双因素认证 (TOTP)</text>
          <toggle-switch checked="{{formData.totpEnabled}}" bind:change="onTotpToggle" />
        </view>
        <!-- 添加到收藏 -->
        <view class="flex items-center justify-between mb-4">
          <text class="text-white font-medium">添加到收藏</text>
          <toggle-switch checked="{{formData.isFavorite}}" bind:change="onFavoriteToggle" />
        </view>
        <!-- 备注输入 -->
        <view>
          <text class="block text-white font-medium mb-3">备注</text>
          <textarea class="w-full p-3 bg-dark-input/70 border border-white/10 rounded-xl text-white resize-none" rows="3" placeholder="添加备注信息..." value="{{formData.notes}}" maxlength="500" bind:input="onNotesInput" bind:blur="onNotesBlur" />
        </view>
      </glass-card>
    </view>
    <!-- 自定义字段 -->
    <view class="px-5 mb-6">
      <glass-card padding="24rpx">
        <view class="flex items-center justify-between mb-6">
          <text class="text-white font-medium">自定义字段</text>
          <custom-button variant="ghost" size="sm" left-icon="plus" bind:tap="onAddCustomFieldTap" />
        </view>
        <!-- 自定义字段列表 -->
        <view wx:if="{{customFields.length > 0}}" class="space-y-4">
          <view wx:for="{{customFields}}" wx:key="id" class="p-4 bg-white/5 rounded-xl">
            <view class="flex space-x-3 mb-3">
              <view class="flex-1">
                <form-input placeholder="字段名称" value="{{item.name}}" bind:input="onCustomFieldNameInput" data-index="{{index}}" />
              </view>
              <view class="flex-1">
                <form-input placeholder="字段值" value="{{item.value}}" type="{{item.isSecret ? 'password' : 'text'}}" bind:input="onCustomFieldValueInput" data-index="{{index}}" />
              </view>
            </view>
            <view class="flex items-center justify-end space-x-2">
              <custom-button variant="ghost" size="sm" left-icon="{{item.isSecret ? 'eye-off' : 'eye'}}" bind:tap="onCustomFieldToggleSecret" data-index="{{index}}" />
              <custom-button variant="ghost" size="sm" left-icon="trash" bind:tap="onCustomFieldDelete" data-index="{{index}}" />
            </view>
          </view>
        </view>
        <!-- 空状态 -->
        <view wx:else class="text-center py-8">
          <text class="block text-gray-400 mb-2">暂无自定义字段</text>
          <text class="text-gray-500 text-sm">点击上方按钮添加字段</text>
        </view>
      </glass-card>
    </view>
  </scroll-view>
  <!-- 图标选择模态框 -->
  <modal show="{{showIconModal}}" title="选择图标" bind:close="onIconModalClose">
    <view class="p-6">
      <view class="flex flex-wrap gap-3">
        <view wx:for="{{iconOptions}}" wx:key="name" class="w-12 h-12 rounded-lg flex items-center justify-center text-white transition-all duration-200 {{selectedIconName === item.name ? 'border-2 border-primary-500 scale-110' : 'active:scale-95'}}" style="background-color: {{item.color}};" bind:tap="onIconSelect" data-icon="{{item}}">
          <text class="fas fa-{{item.name}}"></text>
        </view>
      </view>
    </view>
  </modal>
  <!-- 自定义字段模态框 -->
  <modal show="{{showCustomFieldModal}}" title="添加自定义字段" bind:close="onCustomFieldModalClose">
    <view class="p-6">
      <view class="mb-6">
        <form-input label="字段名称" placeholder="例如：安全问题、PIN码..." value="{{newCustomField.name}}" bind:input="onNewCustomFieldNameInput" />
      </view>
      <view class="mb-6">
        <form-input label="字段值" placeholder="输入字段值..." value="{{newCustomField.value}}" type="{{newCustomField.isSecret ? 'password' : 'text'}}" bind:input="onNewCustomFieldValueInput" />
      </view>
      <view class="flex items-center justify-between mb-6">
        <text class="text-white font-medium">敏感信息</text>
        <toggle-switch checked="{{newCustomField.isSecret}}" bind:change="onNewCustomFieldSecretToggle" />
      </view>
      <view class="flex space-x-3">
        <custom-button variant="ghost" text="取消" custom-class="flex-1" bind:tap="onCustomFieldModalClose" />
        <custom-button variant="primary" text="添加" custom-class="flex-1" disabled="{{!newCustomField.name || !newCustomField.value}}" bind:tap="onCustomFieldAdd" />
      </view>
    </view>
  </modal>
</view>