/**
 * VaultKeeper 添加/编辑密码页面
 * 严格按照原型设计实现
 */

import { definePage, ref, computed, onLoad, onShow } from '@vue-mini/core'

definePage({
    setup() {
        // 系统信息
        const statusBarHeight = ref(44)

        // 页面状态
        const isEditMode = ref(false)
        const passwordId = ref('')
        const saveLoading = ref(false)

        // 表单数据
        const formData = ref({
            title: '',
            username: '',
            password: '',
            website: '',
            notes: '',
            category: 'website',
            totpEnabled: false,
            isFavorite: false
        })

        // 表单错误
        const errors = ref({
            title: '',
            username: '',
            password: '',
            website: ''
        })

        // UI 状态
        const passwordVisible = ref(false)
        const showIconModal = ref(false)
        const showCustomFieldModal = ref(false)

        // 图标相关
        const iconName = ref('globe')
        const iconColor = ref('#6d4aff')
        const selectedIconName = ref('')

        // 分类选项
        const categoryOptions = ref([
            { id: 'website', name: '网站' },
            { id: 'app', name: '应用' },
            { id: 'bank', name: '银行卡' },
            { id: 'identity', name: '身份证' },
            { id: 'note', name: '安全笔记' },
            { id: 'other', name: '其他' }])  
             const selectedCategoryIndex = ref(0)  
                // 图标选项 
                  const iconOptions = ref([\n      { name: 'globe', color: '#6d4aff' },\n      { name: 'google', color: '#4285f4' },\n      { name: 'github', color: '#333' },\n      { name: 'facebook', color: '#4267b2' },\n      { name: 'twitter', color: '#1da1f2' },\n      { name: 'instagram', color: '#e4405f' },\n      { name: 'linkedin', color: '#0077b5' },\n      { name: 'youtube', color: '#ff0000' },\n      { name: 'amazon', color: '#ff9900' },\n      { name: 'apple', color: '#000' },\n      { name: 'microsoft', color: '#0078d4' },\n      { name: 'spotify', color: '#1db954' },\n      { name: 'netflix', color: '#e50914' },\n      { name: 'dropbox', color: '#0061ff' },\n      { name: 'paypal', color: '#003087' },\n      { name: 'university', color: '#10b981' },\n      { name: 'shopping-cart', color: '#8b5cf6' },\n      { name: 'envelope', color: '#f59e0b' },\n      { name: 'shield-alt', color: '#ef4444' },\n      { name: 'key', color: '#6366f1' }\n    ])\n    \n    // 自定义字段\n    const customFields = ref([])\n    const newCustomField = ref({\n      name: '',\n      value: '',\n      isSecret: false\n    })\n    \n    // 计算属性\n    const selectedCategory = computed(() => {\n      return categoryOptions.value[selectedCategoryIndex.value] || categoryOptions.value[0]\n    })\n    \n    const passwordStrength = computed(() => {\n      if (!formData.value.password) {\n        return { score: 0, level: 'weak', feedback: [] }\n      }\n      \n      const password = formData.value.password\n      let score = 0\n      const feedback = []\n      \n      // 长度评分\n      if (password.length >= 8) score += 25\n      if (password.length >= 12) score += 25\n      if (password.length >= 16) score += 25\n      else if (password.length < 8) {\n        feedback.push('密码长度建议至少8位')\n      }\n      \n      // 字符类型评分\n      if (/[a-z]/.test(password)) score += 5\n      else feedback.push('建议包含小写字母')\n      \n      if (/[A-Z]/.test(password)) score += 5\n      else feedback.push('建议包含大写字母')\n      \n      if (/[0-9]/.test(password)) score += 5\n      else feedback.push('建议包含数字')\n      \n      if (/[!@#$%^&*(),.?\":{}|<>]/.test(password)) score += 10\n      else feedback.push('建议包含特殊字符')\n      \n      // 确定强度等级\n      let level = 'weak'\n      if (score >= 80) level = 'strong'\n      else if (score >= 60) level = 'medium'\n      \n      return { score, level, feedback }\n    })\n    \n    const canSave = computed(() => {\n      return formData.value.title && formData.value.password && !saveLoading.value\n    })\n    \n    /**\n     * 页面加载\n     */\n    onLoad(async (options) => {\n      try {\n        console.log('➕ 添加/编辑密码页面加载', options)\n        \n        // 获取系统信息\n        const systemInfo = wx.getSystemInfoSync()\n        statusBarHeight.value = systemInfo.statusBarHeight || 44\n        \n        // 检查是否是编辑模式\n        if (options.id) {\n          isEditMode.value = true\n          passwordId.value = options.id\n          await loadPasswordData(options.id)\n        }\n        \n        // 检查是否有预设密码（来自密码生成器）\n        if (options.password) {\n          formData.value.password = decodeURIComponent(options.password)\n        }\n        \n      } catch (error) {\n        console.error('❌ 页面加载失败:', error)\n        wx.showToast({\n          title: '加载失败',\n          icon: 'error'\n        })\n      }\n    })\n    \n    /**\n     * 页面显示\n     */\n    onShow(() => {\n      console.log('➕ 添加/编辑密码页面显示')\n    })\n    \n    /**\n     * 加载密码数据（编辑模式）\n     */\n    const loadPasswordData = async (id) => {\n      try {\n        // 模拟数据 - 实际项目中从存储获取\n        const mockPassword = {\n          id,\n          title: 'Google',\n          username: '<EMAIL>',\n          password: 'MySecurePassword123!',\n          website: 'https://accounts.google.com',\n          notes: '这是我的主要Google账号',\n          category: 'website',\n          totpEnabled: false,\n          isFavorite: true,\n          icon: 'google',\n          iconColor: '#4285f4'\n        }\n        \n        formData.value = {\n          title: mockPassword.title,\n          username: mockPassword.username,\n          password: mockPassword.password,\n          website: mockPassword.website,\n          notes: mockPassword.notes,\n          category: mockPassword.category,\n          totpEnabled: mockPassword.totpEnabled,\n          isFavorite: mockPassword.isFavorite\n        }\n        \n        iconName.value = mockPassword.icon || 'globe'\n        iconColor.value = mockPassword.iconColor || '#6d4aff'\n        \n        // 设置分类选择器\n        const categoryIndex = categoryOptions.value.findIndex(cat => cat.id === mockPassword.category)\n        selectedCategoryIndex.value = categoryIndex >= 0 ? categoryIndex : 0\n        \n        console.log('✅ 密码数据加载完成')\n        \n      } catch (error) {\n        console.error('❌ 加载密码数据失败:', error)\n        throw error\n      }\n    }\n    \n    /**\n     * 返回上一页\n     */\n    const onBackTap = () => {\n      if (hasUnsavedChanges()) {\n        wx.showModal({\n          title: '确认离开',\n          content: '您有未保存的更改，确定要离开吗？',\n          success: (res) => {\n            if (res.confirm) {\n              wx.navigateBack()\n            }\n          }\n        })\n      } else {\n        wx.navigateBack()\n      }\n    }\n    \n    /**\n     * 检查是否有未保存的更改\n     */\n    const hasUnsavedChanges = () => {\n      // 简化检查 - 实际项目中需要比较原始数据\n      return formData.value.title || formData.value.username || formData.value.password\n    }\n    \n    /**\n     * 保存密码\n     */\n    const onSaveTap = async () => {\n      try {\n        console.log('💾 保存密码')\n        \n        // 验证表单\n        if (!validateForm()) {\n          return\n        }\n        \n        saveLoading.value = true\n        \n        // 模拟保存延迟\n        await new Promise(resolve => setTimeout(resolve, 1000))\n        \n        const passwordData = {\n          ...formData.value,\n          icon: iconName.value,\n          iconColor: iconColor.value,\n          customFields: customFields.value,\n          strength: passwordStrength.value\n        }\n        \n        if (isEditMode.value) {\n          console.log('📝 更新密码:', passwordData)\n        } else {\n          console.log('➕ 添加新密码:', passwordData)\n        }\n        \n        wx.showToast({\n          title: isEditMode.value ? '密码已更新' : '密码已保存',\n          icon: 'success',\n          duration: 1500\n        })\n        \n        // 返回上一页\n        setTimeout(() => {\n          wx.navigateBack()\n        }, 1500)\n        \n      } catch (error) {\n        console.error('❌ 保存密码失败:', error)\n        wx.showToast({\n          title: '保存失败',\n          icon: 'error'\n        })\n      } finally {\n        saveLoading.value = false\n      }\n    }\n    \n    /**\n     * 表单验证\n     */\n    const validateForm = () => {\n      const newErrors = {\n        title: '',\n        username: '',\n        password: '',\n        website: ''\n      }\n      \n      // 标题验证\n      if (!formData.value.title.trim()) {\n        newErrors.title = '请输入密码名称'\n      }\n      \n      // 密码验证\n      if (!formData.value.password) {\n        newErrors.password = '请输入密码'\n      } else if (formData.value.password.length < 6) {\n        newErrors.password = '密码长度至少6位'\n      }\n      \n      // 网址验证\n      if (formData.value.website && !isValidUrl(formData.value.website)) {\n        newErrors.website = '请输入有效的网址'\n      }\n      \n      errors.value = newErrors\n      \n      // 检查是否有错误\n      return !Object.values(newErrors).some(error => error)\n    }\n    \n    /**\n     * 验证URL格式\n     */\n    const isValidUrl = (url) => {\n      try {\n        new URL(url)\n        return true\n      } catch {\n        return false\n      }\n    }\n    \n    /**\n     * 标题输入\n     */\n    const onTitleInput = (event) => {\n      formData.value.title = event.detail.value\n      if (errors.value.title) {\n        errors.value.title = ''\n      }\n    }\n    \n    const onTitleBlur = () => {\n      if (!formData.value.title.trim()) {\n        errors.value.title = '请输入密码名称'\n      }\n    }\n    \n    /**\n     * 用户名输入\n     */\n    const onUsernameInput = (event) => {\n      formData.value.username = event.detail.value\n    }\n    \n    const onUsernameBlur = () => {\n      // 用户名验证逻辑\n    }\n    \n    /**\n     * 密码输入\n     */\n    const onPasswordInput = (event) => {\n      formData.value.password = event.detail.value\n      if (errors.value.password) {\n        errors.value.password = ''\n      }\n    }\n    \n    const onPasswordBlur = () => {\n      if (!formData.value.password) {\n        errors.value.password = '请输入密码'\n      }\n    }\n    \n    /**\n     * 网址输入\n     */\n    const onWebsiteInput = (event) => {\n      formData.value.website = event.detail.value\n      if (errors.value.website) {\n        errors.value.website = ''\n      }\n    }\n    \n    const onWebsiteBlur = () => {\n      if (formData.value.website && !isValidUrl(formData.value.website)) {\n        errors.value.website = '请输入有效的网址'\n      }\n    }\n    \n    /**\n     * 备注输入\n     */\n    const onNotesInput = (event) => {\n      formData.value.notes = event.detail.value\n    }\n    \n    const onNotesBlur = () => {\n      // 备注验证逻辑\n    }\n    \n    /**\n     * 分类选择\n     */\n    const onCategoryChange = (event) => {\n      const index = event.detail.value\n      selectedCategoryIndex.value = index\n      formData.value.category = categoryOptions.value[index].id\n    }\n    \n    /**\n     * 密码可见性切换\n     */\n    const onPasswordVisibilityToggle = () => {\n      passwordVisible.value = !passwordVisible.value\n    }\n    \n    /**\n     * 生成密码\n     */\n    const onGeneratePasswordTap = () => {\n      wx.navigateTo({\n        url: '/pages/password-generator/password-generator?callback=true'\n      })\n    }\n    \n    /**\n     * TOTP开关\n     */\n    const onTotpToggle = (event) => {\n      formData.value.totpEnabled = event.detail.value\n    }\n    \n    /**\n     * 收藏开关\n     */\n    const onFavoriteToggle = (event) => {\n      formData.value.isFavorite = event.detail.value\n    }\n    \n    /**\n     * 图标选择\n     */\n    const onIconTap = () => {\n      showIconModal.value = true\n      selectedIconName.value = iconName.value\n    }\n    \n    const onIconModalClose = () => {\n      showIconModal.value = false\n    }\n    \n    const onIconSelect = (event) => {\n      const icon = event.currentTarget.dataset.icon\n      iconName.value = icon.name\n      iconColor.value = icon.color\n      showIconModal.value = false\n    }\n    \n    /**\n     * 自定义字段\n     */\n    const onAddCustomFieldTap = () => {\n      newCustomField.value = {\n        name: '',\n        value: '',\n        isSecret: false\n      }\n      showCustomFieldModal.value = true\n    }\n    \n    const onCustomFieldModalClose = () => {\n      showCustomFieldModal.value = false\n    }\n    \n    const onNewCustomFieldNameInput = (event) => {\n      newCustomField.value.name = event.detail.value\n    }\n    \n    const onNewCustomFieldValueInput = (event) => {\n      newCustomField.value.value = event.detail.value\n    }\n    \n    const onNewCustomFieldSecretToggle = (event) => {\n      newCustomField.value.isSecret = event.detail.value\n    }\n    \n    const onCustomFieldAdd = () => {\n      const field = {\n        id: Date.now().toString(),\n        ...newCustomField.value\n      }\n      \n      customFields.value.push(field)\n      showCustomFieldModal.value = false\n      \n      wx.showToast({\n        title: '字段已添加',\n        icon: 'success'\n      })\n    }\n    \n    const onCustomFieldNameInput = (event) => {\n      const index = event.currentTarget.dataset.index\n      customFields.value[index].name = event.detail.value\n    }\n    \n    const onCustomFieldValueInput = (event) => {\n      const index = event.currentTarget.dataset.index\n      customFields.value[index].value = event.detail.value\n    }\n    \n    const onCustomFieldToggleSecret = (event) => {\n      const index = event.currentTarget.dataset.index\n      customFields.value[index].isSecret = !customFields.value[index].isSecret\n    }\n    \n    const onCustomFieldDelete = (event) => {\n      const index = event.currentTarget.dataset.index\n      \n      wx.showModal({\n        title: '删除字段',\n        content: '确定要删除这个自定义字段吗？',\n        success: (res) => {\n          if (res.confirm) {\n            customFields.value.splice(index, 1)\n          }\n        }\n      })\n    }\n    \n    return {\n      // 响应式数据\n      statusBarHeight,\n      isEditMode,\n      passwordId,\n      saveLoading,\n      formData,\n      errors,\n      passwordVisible,\n      showIconModal,\n      showCustomFieldModal,\n      iconName,\n      iconColor,\n      selectedIconName,\n      categoryOptions,\n      selectedCategoryIndex,\n      selectedCategory,\n      iconOptions,\n      customFields,\n      newCustomField,\n      passwordStrength,\n      canSave,\n      \n      // 方法\n      onBackTap,\n      onSaveTap,\n      onTitleInput,\n      onTitleBlur,\n      onUsernameInput,\n      onUsernameBlur,\n      onPasswordInput,\n      onPasswordBlur,\n      onWebsiteInput,\n      onWebsiteBlur,\n      onNotesInput,\n      onNotesBlur,\n      onCategoryChange,\n      onPasswordVisibilityToggle,\n      onGeneratePasswordTap,\n      onTotpToggle,\n      onFavoriteToggle,\n      onIconTap,\n      onIconModalClose,\n      onIconSelect,\n      onAddCustomFieldTap,\n      onCustomFieldModalClose,\n      onNewCustomFieldNameInput,\n      onNewCustomFieldValueInput,\n      onNewCustomFieldSecretToggle,\n      onCustomFieldAdd,\n      onCustomFieldNameInput,\n      onCustomFieldValueInput,\n      onCustomFieldToggleSecret,\n      onCustomFieldDelete\n    }\n  }\n})
