/**
 * VaultKeeper 添加/编辑密码页面 - 简化版本
 * 严格按照原型设计实现
 */

import { definePage, ref, computed, onLoad, onShow } from '@vue-mini/core'
import { usePasswordStore } from '../../store/password'

definePage({
    setup() {
        // 初始化密码 store
        const passwordStore = usePasswordStore()

        // 系统信息
        const statusBarHeight = ref(44)

        // 页面状态
        const isEditMode = ref(false)
        const passwordId = ref('')
        const saveLoading = ref(false)

        // 表单数据
        const formData = ref({
            title: '',
            username: '',
            password: '',
            website: '',
            notes: '',
            category: 'website',
            totpEnabled: false,
            isFavorite: false
        })

        // 表单错误
        const errors = ref({
            title: '',
            username: '',
            password: '',
            website: ''
        })

        // UI 状态
        const passwordVisible = ref(false)
        const showIconModal = ref(false)
        const showCustomFieldModal = ref(false)

        // 图标相关
        const iconName = ref('globe')
        const iconColor = ref('#6d4aff')
        const selectedIconName = ref('')

        // 分类选项
        const categoryOptions = ref([
            { id: 'website', name: '网站' },
            { id: 'app', name: '应用' },
            { id: 'bank', name: '银行卡' },
            { id: 'identity', name: '身份证' },
            { id: 'note', name: '安全笔记' },
            { id: 'other', name: '其他' }
        ])

        const selectedCategoryIndex = ref(0)

        // 图标选项
        const iconOptions = ref([
            { name: 'globe', color: '#6d4aff' },
            { name: 'google', color: '#4285f4' },
            { name: 'github', color: '#333' },
            { name: 'facebook', color: '#4267b2' },
            { name: 'twitter', color: '#1da1f2' },
            { name: 'instagram', color: '#e4405f' },
            { name: 'linkedin', color: '#0077b5' },
            { name: 'youtube', color: '#ff0000' },
            { name: 'amazon', color: '#ff9900' },
            { name: 'apple', color: '#000' },
            { name: 'microsoft', color: '#0078d4' },
            { name: 'spotify', color: '#1db954' },
            { name: 'netflix', color: '#e50914' },
            { name: 'dropbox', color: '#0061ff' },
            { name: 'paypal', color: '#003087' },
            { name: 'university', color: '#10b981' },
            { name: 'shopping-cart', color: '#8b5cf6' },
            { name: 'envelope', color: '#f59e0b' },
            { name: 'shield-alt', color: '#ef4444' },
            { name: 'key', color: '#6366f1' }
        ])

        // 自定义字段
        const customFields = ref([])
        const newCustomField = ref({
            name: '',
            value: '',
            isSecret: false
        })

        // 计算属性
        const selectedCategory = computed(() => {
            return categoryOptions.value[selectedCategoryIndex.value] || categoryOptions.value[0]
        })

        const passwordStrength = computed(() => {
            if (!formData.value.password) {
                return { score: 0, level: 'weak', feedback: [] }
            }

            const password = formData.value.password
            let score = 0
            const feedback = []

            // 长度评分
            if (password.length >= 8) score += 25
            if (password.length >= 12) score += 25
            if (password.length >= 16) score += 25
            else if (password.length < 8) {
                feedback.push('密码长度建议至少8位')
            }

            // 字符类型评分
            if (/[a-z]/.test(password)) score += 5
            else feedback.push('建议包含小写字母')

            if (/[A-Z]/.test(password)) score += 5
            else feedback.push('建议包含大写字母')

            if (/[0-9]/.test(password)) score += 5
            else feedback.push('建议包含数字')

            if (/[!@#$%^&*(),.?\":{}|<>]/.test(password)) score += 10
            else feedback.push('建议包含特殊字符')

            // 确定强度等级
            let level = 'weak'
            if (score >= 80) level = 'strong'
            else if (score >= 60) level = 'medium'

            return { score, level, feedback }
        })

        const canSave = computed(() => {
            return formData.value.title && formData.value.password && !saveLoading.value
        })

        /**
         * 页面加载
         */
        onLoad(async (options) => {
            try {
                console.log('➕ 添加/编辑密码页面加载', options)

                // 获取系统信息 - 使用新的 API
                const windowInfo = wx.getWindowInfo()
                statusBarHeight.value = windowInfo.statusBarHeight || 44

                // 初始化密码 store（如果还没有初始化）
                if (passwordStore.passwords.length === 0) {
                    await passwordStore.initialize()
                }

                // 检查是否是编辑模式
                if (options.id) {
                    isEditMode.value = true
                    passwordId.value = options.id
                    await loadPasswordData(options.id)
                }

                // 检查是否有预设密码（来自密码生成器）
                if (options.password) {
                    formData.value.password = decodeURIComponent(options.password)
                }

            } catch (error) {
                console.error('❌ 页面加载失败:', error)
                wx.showToast({
                    title: '加载失败',
                    icon: 'error'
                })
            }
        })

        /**
         * 页面显示
         */
        onShow(() => {
            console.log('➕ 添加/编辑密码页面显示')
        })

        /**
         * 加载密码数据（编辑模式）
         */
        const loadPasswordData = async (id) => {
            try {
                console.log('🔍 加载密码数据:', id)

                // 从 store 获取密码数据
                const password = passwordStore.getPassword(id)

                if (!password) {
                    console.error('❌ 密码不存在')
                    wx.showToast({
                        title: '密码不存在',
                        icon: 'error'
                    })
                    wx.navigateBack()
                    return
                }

                // 映射 store 分类到页面分类
                const categoryMap = {
                    'work': 'website',
                    'entertainment': 'app',
                    'finance': 'bank',
                    'other': 'other'
                }
                const pageCategory = categoryMap[password.category] || 'other'

                formData.value = {
                    title: password.title,
                    username: password.username,
                    password: password.password,
                    website: password.website || '',
                    notes: password.notes || '',
                    category: pageCategory,
                    totpEnabled: false, // TOTP 功能暂未实现
                    isFavorite: password.isFavorite
                }

                iconName.value = password.icon || 'globe'
                iconColor.value = password.iconColor || '#6d4aff'

                // 设置分类选择器
                const categoryIndex = categoryOptions.value.findIndex(cat => cat.id === pageCategory)
                selectedCategoryIndex.value = categoryIndex >= 0 ? categoryIndex : 0

                console.log('✅ 密码数据加载完成')

            } catch (error) {
                console.error('❌ 加载密码数据失败:', error)
                throw error
            }
        }

        /**
         * 返回上一页
         */
        const onBackTap = () => {
            if (hasUnsavedChanges()) {
                wx.showModal({
                    title: '确认离开',
                    content: '您有未保存的更改，确定要离开吗？',
                    success: (res) => {
                        if (res.confirm) {
                            wx.navigateBack()
                        }
                    }
                })
            } else {
                wx.navigateBack()
            }
        }

        /**
         * 检查是否有未保存的更改
         */
        const hasUnsavedChanges = () => {
            // 简化检查 - 实际项目中需要比较原始数据
            return formData.value.title || formData.value.username || formData.value.password
        }

        /**
         * 保存密码
         */
        const onSaveTap = async () => {
            try {
                console.log('💾 保存密码')

                // 验证表单
                if (!validateForm()) {
                    return
                }

                saveLoading.value = true

                // 映射页面分类到 store 分类
                const categoryMap = {
                    'website': 'work',
                    'app': 'entertainment',
                    'bank': 'finance',
                    'identity': 'other',
                    'note': 'other',
                    'other': 'other'
                }
                const storeCategory = categoryMap[formData.value.category] || 'other'

                const passwordData = {
                    title: formData.value.title,
                    username: formData.value.username,
                    password: formData.value.password,
                    website: formData.value.website,
                    notes: formData.value.notes,
                    category: storeCategory,
                    tags: [],
                    isFavorite: formData.value.isFavorite,
                    icon: iconName.value,
                    iconType: 'fas' as const,
                    iconColor: iconColor.value
                }

                if (isEditMode.value) {
                    console.log('📝 更新密码:', passwordData)
                    await passwordStore.updatePassword(passwordId.value, passwordData)
                } else {
                    console.log('➕ 添加新密码:', passwordData)
                    await passwordStore.addPassword(passwordData)
                }

                wx.showToast({
                    title: isEditMode.value ? '密码已更新' : '密码已保存',
                    icon: 'success',
                    duration: 1500
                })

                // 返回上一页
                setTimeout(() => {
                    wx.navigateBack()
                }, 1500)

            } catch (error) {
                console.error('❌ 保存密码失败:', error)
                wx.showToast({
                    title: '保存失败',
                    icon: 'error'
                })
            } finally {
                saveLoading.value = false
            }
        }

        /**
         * 表单验证
         */
        const validateForm = () => {
            const newErrors = {
                title: '',
                username: '',
                password: '',
                website: ''
            }

            // 标题验证
            if (!formData.value.title.trim()) {
                newErrors.title = '请输入密码名称'
            }

            // 密码验证
            if (!formData.value.password) {
                newErrors.password = '请输入密码'
            } else if (formData.value.password.length < 6) {
                newErrors.password = '密码长度至少6位'
            }

            // 网址验证
            if (formData.value.website && !isValidUrl(formData.value.website)) {
                newErrors.website = '请输入有效的网址'
            }

            errors.value = newErrors

            // 检查是否有错误
            return !Object.values(newErrors).some(error => error)
        }

        /**
         * 验证URL格式
         */
        const isValidUrl = (url) => {
            try {
                new URL(url)
                return true
            } catch {
                return false
            }
        }

        /**
         * 表单输入处理
         */
        const onTitleInput = (event) => {
            formData.value.title = event.detail.value
            if (errors.value.title) {
                errors.value.title = ''
            }
        }

        const onUsernameInput = (event) => {
            formData.value.username = event.detail.value
        }

        const onPasswordInput = (event) => {
            formData.value.password = event.detail.value
            if (errors.value.password) {
                errors.value.password = ''
            }
        }

        const onWebsiteInput = (event) => {
            formData.value.website = event.detail.value
            if (errors.value.website) {
                errors.value.website = ''
            }
        }

        const onNotesInput = (event) => {
            formData.value.notes = event.detail.value
        }

        /**
         * 其他操作
         */
        const onCategoryChange = (event) => {
            const index = event.detail.value
            selectedCategoryIndex.value = index
            formData.value.category = categoryOptions.value[index].id
        }

        const onPasswordVisibilityToggle = () => {
            passwordVisible.value = !passwordVisible.value
        }

        const onGeneratePasswordTap = () => {
            wx.navigateTo({
                url: '/pages/password-generator/password-generator?callback=true'
            })
        }

        const onTotpToggle = (event) => {
            formData.value.totpEnabled = event.detail.value
        }

        const onFavoriteToggle = (event) => {
            formData.value.isFavorite = event.detail.value
        }

        const onIconTap = () => {
            showIconModal.value = true
            selectedIconName.value = iconName.value
        }

        const onIconModalClose = () => {
            showIconModal.value = false
        }

        const onIconSelect = (event) => {
            const icon = event.currentTarget.dataset.icon
            iconName.value = icon.name
            iconColor.value = icon.color
            showIconModal.value = false
        }

        return {
            // 响应式数据
            statusBarHeight,
            isEditMode,
            passwordId,
            saveLoading,
            formData,
            errors,
            passwordVisible,
            showIconModal,
            showCustomFieldModal,
            iconName,
            iconColor,
            selectedIconName,
            categoryOptions,
            selectedCategoryIndex,
            selectedCategory,
            iconOptions,
            customFields,
            newCustomField,
            passwordStrength,
            canSave,

            // 方法
            onBackTap,
            onSaveTap,
            onTitleInput,
            onUsernameInput,
            onPasswordInput,
            onWebsiteInput,
            onNotesInput,
            onCategoryChange,
            onPasswordVisibilityToggle,
            onGeneratePasswordTap,
            onTotpToggle,
            onFavoriteToggle,
            onIconTap,
            onIconModalClose,
            onIconSelect
        }
    }
})
