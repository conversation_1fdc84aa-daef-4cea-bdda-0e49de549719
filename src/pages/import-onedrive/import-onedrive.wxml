<!--
  VaultKeeper OneDrive 导入页面
  支持从 OneDrive 云端导入 KeePass 数据库文件
-->

<view class="onedrive-import">
  <!-- 页面头部 -->
  <view class="import-header">
    <view class="import-icon">
      <text>☁️</text>
    </view>
    <text class="import-title">OneDrive 导入</text>
    <text class="import-subtitle">从云端导入您的 KeePass 数据库</text>
  </view>

  <!-- 导入步骤 -->
  <view class="import-steps">
    <view class="step-item {{currentStep >= 1 ? 'active' : ''}} {{currentStep > 1 ? 'completed' : ''}}">
      <view class="step-number">1</view>
      <text class="step-text">授权登录</text>
    </view>
    <view class="step-line {{currentStep > 1 ? 'completed' : ''}}"></view>
    <view class="step-item {{currentStep >= 2 ? 'active' : ''}} {{currentStep > 2 ? 'completed' : ''}}">
      <view class="step-number">2</view>
      <text class="step-text">选择文件</text>
    </view>
    <view class="step-line {{currentStep > 2 ? 'completed' : ''}}"></view>
    <view class="step-item {{currentStep >= 3 ? 'active' : ''}} {{currentStep > 3 ? 'completed' : ''}}">
      <view class="step-number">3</view>
      <text class="step-text">导入数据</text>
    </view>
  </view>

  <!-- 步骤1: 授权登录 -->
  <glass-card wx:if="{{currentStep === 1}}" class="import-card">
    <view class="card-header">
      <text class="card-title">连接 OneDrive</text>
      <text class="card-subtitle">需要授权访问您的 OneDrive 文件</text>
    </view>

    <view class="auth-section">
      <view wx:if="{{!isAuthorized}}" class="auth-login">
        <view class="auth-icon">
          <text>🔐</text>
        </view>
        <text class="auth-title">安全授权</text>
        <text class="auth-desc">我们将通过 Microsoft 官方授权页面安全访问您的 OneDrive</text>
        
        <view class="auth-features">
          <view class="feature-item">
            <text class="feature-icon">✅</text>
            <text class="feature-text">只读权限，无法修改您的文件</text>
          </view>
          <view class="feature-item">
            <text class="feature-icon">🔒</text>
            <text class="feature-text">授权信息本地加密存储</text>
          </view>
          <view class="feature-item">
            <text class="feature-icon">⏰</text>
            <text class="feature-text">授权令牌自动过期保护</text>
          </view>
        </view>
      </view>

      <view wx:else class="auth-success">
        <view class="success-icon">
          <text>✅</text>
        </view>
        <text class="success-title">授权成功</text>
        <text class="success-desc">已连接到您的 OneDrive 账户</text>
        <view class="user-info">
          <text class="user-email">{{userEmail}}</text>
        </view>
      </view>
    </view>

    <view class="card-actions">
      <custom-button
        wx:if="{{!isAuthorized}}"
        variant="primary"
        size="lg"
        block="{{true}}"
        text="授权登录 OneDrive"
        loading="{{authorizing}}"
        loading-text="授权中..."
        bind:tap="onAuthorize"
      />
      <custom-button
        wx:else
        variant="primary"
        size="lg"
        block="{{true}}"
        text="下一步"
        bind:tap="onNextStep"
      />
    </view>
  </glass-card>

  <!-- 步骤2: 选择文件 -->
  <glass-card wx:if="{{currentStep === 2}}" class="import-card">
    <view class="card-header">
      <text class="card-title">选择 KeePass 文件</text>
      <text class="card-subtitle">从您的 OneDrive 中选择 .kdbx 文件</text>
    </view>

    <view class="file-browser">
      <!-- 当前路径 -->
      <view class="current-path">
        <text class="path-icon">📁</text>
        <text class="path-text">{{currentPath || '/'}}</text>
      </view>

      <!-- 文件列表 -->
      <view wx:if="{{loading}}" class="loading-state">
        <text class="loading-icon">⏳</text>
        <text class="loading-text">加载文件列表...</text>
      </view>

      <view wx:elif="{{files.length === 0}}" class="empty-state">
        <text class="empty-icon">📂</text>
        <text class="empty-text">此文件夹为空</text>
        <text class="empty-desc">请将 .kdbx 文件上传到 OneDrive</text>
      </view>

      <view wx:else class="file-list">
        <!-- 返回上级目录 -->
        <view wx:if="{{currentPath}}" class="file-item folder-item" bind:tap="onGoBack">
          <text class="file-icon">⬆️</text>
          <text class="file-name">返回上级目录</text>
          <text class="file-action">📁</text>
        </view>

        <!-- 文件和文件夹列表 -->
        <view
          wx:for="{{files}}"
          wx:key="id"
          class="file-item {{item.isFolder ? 'folder-item' : 'file-item'}} {{selectedFile && selectedFile.id === item.id ? 'selected' : ''}}"
          bind:tap="onFileSelect"
          data-file="{{item}}"
        >
          <text class="file-icon">{{item.isFolder ? '📁' : (item.name.endsWith('.kdbx') ? '🔐' : '📄')}}</text>
          <view class="file-info">
            <text class="file-name">{{item.name}}</text>
            <text wx:if="{{!item.isFolder}}" class="file-size">{{item.sizeText}}</text>
            <text wx:if="{{!item.isFolder}}" class="file-date">{{item.dateText}}</text>
          </view>
          <text class="file-action">{{item.isFolder ? '📁' : (item.name.endsWith('.kdbx') ? '✅' : '📄')}}</text>
        </view>
      </view>
    </view>

    <view class="card-actions">
      <custom-button
        variant="ghost"
        size="lg"
        text="上一步"
        bind:tap="onPrevStep"
        style="margin-right: 16rpx;"
      />
      <custom-button
        variant="primary"
        size="lg"
        text="下一步"
        disabled="{{!selectedFile || selectedFile.isFolder}}"
        bind:tap="onNextStep"
        style="flex: 1;"
      />
    </view>
  </glass-card>

  <!-- 步骤3: 输入密码和导入 -->
  <glass-card wx:if="{{currentStep === 3}}" class="import-card">
    <view class="card-header">
      <text class="card-title">{{importing ? '正在导入...' : '输入数据库密码'}}</text>
      <text class="card-subtitle">{{importing ? '请稍候，正在处理您的数据' : '请输入 KeePass 数据库的主密码'}}</text>
    </view>

    <view wx:if="{{!importing && !importResult.success}}" class="password-form">
      <view class="selected-file-info">
        <text class="file-icon">🔐</text>
        <view class="file-details">
          <text class="file-name">{{selectedFile.name}}</text>
          <text class="file-path">{{selectedFile.path}}</text>
        </view>
      </view>

      <form-input
        label="数据库密码"
        type="password"
        value="{{databasePassword}}"
        placeholder="请输入 KeePass 数据库密码"
        required="{{true}}"
        clearable="{{false}}"
        bind:input="onPasswordInput"
        error-text="{{passwordError}}"
        left-icon="lock"
      />
    </view>

    <!-- 导入进度 -->
    <view wx:if="{{importing}}" class="import-progress">
      <view class="progress-steps">
        <view class="progress-step {{downloadProgress > 0 ? 'active' : ''}}">
          <text class="step-icon">📥</text>
          <text class="step-text">下载文件</text>
          <text wx:if="{{downloadProgress > 0}}" class="step-progress">{{downloadProgress}}%</text>
        </view>
        <view class="progress-step {{parseProgress > 0 ? 'active' : ''}}">
          <text class="step-icon">🔓</text>
          <text class="step-text">解析数据库</text>
        </view>
        <view class="progress-step {{importProgress > 0 ? 'active' : ''}}">
          <text class="step-icon">💾</text>
          <text class="step-text">导入密码</text>
          <text wx:if="{{importProgress > 0}}" class="step-progress">{{importProgress}}%</text>
        </view>
      </view>
    </view>

    <!-- 导入结果 -->
    <view wx:if="{{importResult.success || importResult.message}}" class="import-result">
      <view class="result-icon {{importResult.success ? 'success' : 'error'}}">
        <text>{{importResult.success ? '✅' : '❌'}}</text>
      </view>
      <text class="result-message">{{importResult.message}}</text>

      <view wx:if="{{importResult.success}}" class="result-stats">
        <view class="stat-item">
          <text class="stat-number">{{importResult.totalCount}}</text>
          <text class="stat-label">总条目</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{importResult.importedCount}}</text>
          <text class="stat-label">已导入</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{importResult.skippedCount}}</text>
          <text class="stat-label">已跳过</text>
        </view>
      </view>
    </view>

    <view class="card-actions">
      <custom-button
        wx:if="{{!importing && !importResult.success && !importResult.message}}"
        variant="ghost"
        size="lg"
        text="上一步"
        bind:tap="onPrevStep"
        style="margin-right: 16rpx;"
      />
      <custom-button
        wx:if="{{!importing && !importResult.success && !importResult.message}}"
        variant="primary"
        size="lg"
        text="开始导入"
        disabled="{{!databasePassword}}"
        bind:tap="onStartImport"
        style="flex: 1;"
      />
      <custom-button
        wx:if="{{!importing && (importResult.success || importResult.message)}}"
        variant="primary"
        size="lg"
        text="{{importResult.success ? '完成' : '重试'}}"
        bind:tap="{{importResult.success ? 'onFinish' : 'onRetry'}}"
        block="{{true}}"
      />
    </view>
  </glass-card>

  <!-- 帮助信息 -->
  <view class="import-help">
    <text class="help-title">💡 OneDrive 导入说明</text>
    <text class="help-item">• 支持从 OneDrive 直接导入 .kdbx 文件</text>
    <text class="help-item">• 需要 Microsoft 账户授权访问</text>
    <text class="help-item">• 文件下载后会自动删除临时文件</text>
    <text class="help-item">• 授权信息本地加密存储，保护隐私</text>
  </view>
</view>
