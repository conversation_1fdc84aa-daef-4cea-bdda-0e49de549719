/**
 * VaultKeeper OneDrive 导入页面
 * 支持从 OneDrive 云端导入 KeePass 数据库文件
 */

import { definePage, ref, onLoad, onShow } from '@vue-mini/core'
import { useAuthStore } from '../../store/auth'
import { usePasswordStore } from '../../store/password'
import { KeePassImporter, KeePassImportResult } from '../../utils/keepass'

// OneDrive 文件信息接口
interface OneDriveFile {
  id: string
  name: string
  path: string
  isFolder: boolean
  size?: number
  sizeText?: string
  dateText?: string
  downloadUrl?: string
}

// OneDrive 授权信息接口
interface OneDriveAuth {
  accessToken: string
  refreshToken: string
  expiresAt: number
  userEmail: string
}

definePage({
  setup() {
    const authStore = useAuthStore()
    const passwordStore = usePasswordStore()
    
    console.log('☁️ OneDrive 导入页面初始化')

    // 响应式数据
    const currentStep = ref(1) // 当前步骤：1-授权，2-选择文件，3-导入
    const isAuthorized = ref(false)
    const authorizing = ref(false)
    const userEmail = ref('')
    
    // 文件浏览相关
    const loading = ref(false)
    const files = ref<OneDriveFile[]>([])
    const currentPath = ref('')
    const selectedFile = ref<OneDriveFile | null>(null)
    
    // 导入相关
    const databasePassword = ref('')
    const passwordError = ref('')
    const importing = ref(false)
    const downloadProgress = ref(0)
    const parseProgress = ref(0)
    const importProgress = ref(0)
    
    // 导入结果
    const importResult = ref<KeePassImportResult>({
      success: false,
      message: '',
      passwords: [],
      totalCount: 0,
      importedCount: 0,
      skippedCount: 0,
      errors: []
    })

    /**
     * 页面加载时初始化
     */
    onLoad(async (options: any) => {
      try {
        console.log('📱 初始化 OneDrive 导入页面', options)
        
        if (!authStore.isLoggedIn) {
          console.log('❌ 用户未登录，跳转到登录页')
          wx.reLaunch({ url: '/pages/login/login' })
          return
        }
        
        // 检查是否已有授权信息
        await checkExistingAuth()
        
        await authStore.updateActiveTime()
        
      } catch (error) {
        console.error('❌ 页面初始化失败:', error)
        wx.showToast({
          title: '初始化失败',
          icon: 'error'
        })
      }
    })

    /**
     * 页面显示时更新数据
     */
    onShow(async () => {
      try {
        await authStore.checkSessionTimeout()
        await authStore.checkAutoLock()
        await authStore.updateActiveTime()
      } catch (error) {
        console.error('❌ 页面显示更新失败:', error)
      }
    })

    /**
     * 检查现有授权信息
     */
    const checkExistingAuth = async () => {
      try {
        const authInfo = wx.getStorageSync('onedrive_auth')
        if (authInfo) {
          const auth: OneDriveAuth = JSON.parse(authInfo)
          
          // 检查令牌是否过期
          if (auth.expiresAt > Date.now()) {
            isAuthorized.value = true
            userEmail.value = auth.userEmail
            console.log('✅ 发现有效的 OneDrive 授权')
          } else {
            // 尝试刷新令牌
            await refreshAccessToken(auth.refreshToken)
          }
        }
      } catch (error) {
        console.error('❌ 检查授权信息失败:', error)
        // 清除无效的授权信息
        wx.removeStorageSync('onedrive_auth')
      }
    }

    /**
     * 刷新访问令牌
     */
    const refreshAccessToken = async (refreshToken: string) => {
      try {
        console.log('🔄 刷新 OneDrive 访问令牌')
        
        // 这里应该调用 Microsoft Graph API 刷新令牌
        // 由于小程序环境限制，这里使用模拟实现
        wx.showToast({
          title: '授权已过期，请重新授权',
          icon: 'none'
        })
        
        wx.removeStorageSync('onedrive_auth')
        
      } catch (error) {
        console.error('❌ 刷新令牌失败:', error)
        wx.removeStorageSync('onedrive_auth')
      }
    }

    /**
     * 授权 OneDrive
     */
    const onAuthorize = async () => {
      try {
        authorizing.value = true
        console.log('🔐 开始 OneDrive 授权')
        
        // 由于小程序环境限制，这里使用模拟授权
        // 实际实现需要集成 Microsoft Graph API
        
        wx.showModal({
          title: '授权说明',
          content: '由于小程序环境限制，当前版本使用模拟授权。实际部署时需要集成 Microsoft Graph API。',
          confirmText: '模拟授权',
          cancelText: '取消',
          success: (res) => {
            if (res.confirm) {
              // 模拟授权成功
              simulateAuthorization()
            }
          }
        })
        
      } catch (error) {
        console.error('❌ OneDrive 授权失败:', error)
        wx.showToast({
          title: '授权失败',
          icon: 'error'
        })
      } finally {
        authorizing.value = false
      }
    }

    /**
     * 模拟授权（用于演示）
     */
    const simulateAuthorization = () => {
      setTimeout(() => {
        const mockAuth: OneDriveAuth = {
          accessToken: 'mock_access_token_' + Date.now(),
          refreshToken: 'mock_refresh_token_' + Date.now(),
          expiresAt: Date.now() + 3600000, // 1小时后过期
          userEmail: '<EMAIL>'
        }
        
        // 保存授权信息
        wx.setStorageSync('onedrive_auth', JSON.stringify(mockAuth))
        
        isAuthorized.value = true
        userEmail.value = mockAuth.userEmail
        authorizing.value = false
        
        wx.showToast({
          title: '授权成功',
          icon: 'success'
        })
        
        console.log('✅ OneDrive 授权成功（模拟）')
      }, 2000)
    }

    /**
     * 下一步
     */
    const onNextStep = async () => {
      if (currentStep.value === 1 && isAuthorized.value) {
        currentStep.value = 2
        await loadFiles()
      } else if (currentStep.value === 2 && selectedFile.value) {
        currentStep.value = 3
      }
      
      console.log('➡️ 进入步骤:', currentStep.value)
    }

    /**
     * 上一步
     */
    const onPrevStep = () => {
      if (currentStep.value > 1) {
        currentStep.value--
        console.log('⬅️ 返回步骤:', currentStep.value)
      }
    }

    /**
     * 加载文件列表
     */
    const loadFiles = async (path: string = '') => {
      try {
        loading.value = true
        console.log('📁 加载文件列表:', path)
        
        // 模拟文件列表（实际实现需要调用 Microsoft Graph API）
        const mockFiles: OneDriveFile[] = [
          {
            id: 'folder1',
            name: 'Documents',
            path: path + '/Documents',
            isFolder: true
          },
          {
            id: 'folder2',
            name: 'KeePass',
            path: path + '/KeePass',
            isFolder: true
          },
          {
            id: 'file1',
            name: 'passwords.kdbx',
            path: path + '/passwords.kdbx',
            isFolder: false,
            size: 1024 * 50, // 50KB
            sizeText: '50 KB',
            dateText: '2024-01-15',
            downloadUrl: 'mock://download/passwords.kdbx'
          },
          {
            id: 'file2',
            name: 'backup.kdbx',
            path: path + '/backup.kdbx',
            isFolder: false,
            size: 1024 * 75, // 75KB
            sizeText: '75 KB',
            dateText: '2024-01-10',
            downloadUrl: 'mock://download/backup.kdbx'
          }
        ]
        
        // 模拟网络延迟
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        files.value = mockFiles
        currentPath.value = path
        
        console.log('✅ 文件列表加载完成:', mockFiles.length, '个项目')
        
      } catch (error) {
        console.error('❌ 加载文件列表失败:', error)
        wx.showToast({
          title: '加载失败',
          icon: 'error'
        })
      } finally {
        loading.value = false
      }
    }

    /**
     * 返回上级目录
     */
    const onGoBack = () => {
      const parentPath = currentPath.value.split('/').slice(0, -1).join('/')
      loadFiles(parentPath)
    }

    /**
     * 选择文件或文件夹
     */
    const onFileSelect = (event: any) => {
      const file: OneDriveFile = event.currentTarget.dataset.file

      if (file.isFolder) {
        // 进入文件夹
        loadFiles(file.path)
      } else if (file.name.endsWith('.kdbx')) {
        // 选择 .kdbx 文件
        selectedFile.value = file
        console.log('📄 选择文件:', file.name)
      } else {
        wx.showToast({
          title: '请选择 .kdbx 文件',
          icon: 'none'
        })
      }
    }

    /**
     * 处理密码输入
     */
    const onPasswordInput = (event: any) => {
      const value = event.detail.value
      databasePassword.value = value

      // 清除错误信息
      if (passwordError.value) {
        passwordError.value = ''
      }
    }

    /**
     * 开始导入
     */
    const onStartImport = async () => {
      if (!selectedFile.value || !databasePassword.value) {
        wx.showToast({
          title: '请完善导入信息',
          icon: 'error'
        })
        return
      }

      try {
        importing.value = true
        passwordError.value = ''
        downloadProgress.value = 0
        parseProgress.value = 0
        importProgress.value = 0

        console.log('🚀 开始从 OneDrive 导入...')

        // 步骤1: 下载文件
        const fileBuffer = await downloadFile(selectedFile.value)

        // 步骤2: 解析数据库
        parseProgress.value = 1
        const result = await KeePassImporter.importDatabase(
          fileBuffer,
          databasePassword.value,
          undefined,
          { skipDuplicates: true, mergeCategories: true }
        )

        // 步骤3: 导入密码
        if (result.success && result.passwords.length > 0) {
          importProgress.value = 1

          for (let i = 0; i < result.passwords.length; i++) {
            await passwordStore.addPassword(result.passwords[i])
            importProgress.value = Math.round(((i + 1) / result.passwords.length) * 100)
          }
        }

        importResult.value = result

        if (result.success) {
          console.log('✅ OneDrive 导入成功，共导入', result.importedCount, '个密码')

          // 触觉反馈
          if (wx.vibrateShort) {
            wx.vibrateShort({ type: 'success' })
          }
        } else {
          console.error('❌ 导入失败:', result.message)

          // 如果是密码错误，显示错误信息
          if (result.message.includes('密码') || result.message.includes('密钥')) {
            passwordError.value = result.message
          }
        }

      } catch (error: any) {
        console.error('❌ OneDrive 导入过程出错:', error)

        const errorMessage = error?.message || '未知错误'

        importResult.value = {
          success: false,
          message: `导入失败: ${errorMessage}`,
          passwords: [],
          totalCount: 0,
          importedCount: 0,
          skippedCount: 0,
          errors: [errorMessage]
        }

        // 如果是认证错误，显示错误信息
        if (errorMessage.includes('密码') || errorMessage.includes('认证')) {
          passwordError.value = errorMessage
        }

      } finally {
        importing.value = false
      }
    }

    /**
     * 下载文件
     */
    const downloadFile = async (file: OneDriveFile): Promise<ArrayBuffer> => {
      return new Promise((resolve, reject) => {
        console.log('📥 开始下载文件:', file.name)

        // 模拟下载进度
        let progress = 0
        const progressInterval = setInterval(() => {
          progress += 10
          downloadProgress.value = progress

          if (progress >= 100) {
            clearInterval(progressInterval)

            // 模拟文件内容（实际实现需要从 OneDrive 下载）
            const mockFileContent = new ArrayBuffer(1024 * 50) // 50KB 模拟数据

            console.log('✅ 文件下载完成:', file.name)
            resolve(mockFileContent)
          }
        }, 200)

        // 模拟下载失败的情况
        setTimeout(() => {
          if (progress < 100) {
            clearInterval(progressInterval)
            reject(new Error('下载超时'))
          }
        }, 10000)
      })
    }

    /**
     * 重试导入
     */
    const onRetry = () => {
      // 重置状态
      importResult.value = {
        success: false,
        message: '',
        passwords: [],
        totalCount: 0,
        importedCount: 0,
        skippedCount: 0,
        errors: []
      }

      passwordError.value = ''
      downloadProgress.value = 0
      parseProgress.value = 0
      importProgress.value = 0

      console.log('🔄 重试导入')
    }

    /**
     * 完成导入
     */
    const onFinish = () => {
      if (importResult.value.success) {
        // 导入成功，返回主页
        wx.switchTab({
          url: '/pages/home/<USER>',
          success: () => {
            wx.showToast({
              title: `成功从 OneDrive 导入 ${importResult.value.importedCount} 个密码`,
              icon: 'success',
              duration: 2000
            })
          }
        })
      } else {
        // 导入失败，返回上一页
        wx.navigateBack({
          fail: () => {
            // 如果无法返回，跳转到主页
            wx.switchTab({
              url: '/pages/home/<USER>'
            })
          }
        })
      }
    }

    return {
      // 响应式数据
      currentStep,
      isAuthorized,
      authorizing,
      userEmail,
      loading,
      files,
      currentPath,
      selectedFile,
      databasePassword,
      passwordError,
      importing,
      downloadProgress,
      parseProgress,
      importProgress,
      importResult,

      // 方法
      checkExistingAuth,
      refreshAccessToken,
      onAuthorize,
      simulateAuthorization,
      onNextStep,
      onPrevStep,
      loadFiles,
      onGoBack,
      onFileSelect,
      onPasswordInput,
      onStartImport,
      downloadFile,
      onRetry,
      onFinish
    }
  }
})
