/**
 * VaultKeeper OneDrive 导入页面样式
 * 云端导入的现代化界面设计
 */

.onedrive-import {
  min-height: 100vh;
  background: linear-gradient(135deg, #1a1c25 0%, #0f1118 100%);
  padding: 40rpx 32rpx;
}

/* ==================== 页面头部 ==================== */
.import-header {
  text-align: center;
  margin-bottom: 60rpx;
}

.import-icon {
  font-size: 120rpx;
  margin-bottom: 24rpx;
}

.import-title {
  display: block;
  font-size: 48rpx;
  font-weight: 600;
  color: #e0e0e0;
  margin-bottom: 16rpx;
}

.import-subtitle {
  display: block;
  font-size: 28rpx;
  color: #888888;
}

/* ==================== 导入步骤 ==================== */
.import-steps {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 60rpx;
  padding: 0 40rpx;
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.step-number {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  color: #888888;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
  transition: all 0.3s ease;
}

.step-item.active .step-number {
  background: #6d4aff;
  color: white;
}

.step-item.completed .step-number {
  background: #4ade80;
  color: white;
}

.step-text {
  font-size: 24rpx;
  color: #888888;
  transition: all 0.3s ease;
}

.step-item.active .step-text {
  color: #e0e0e0;
}

.step-line {
  width: 80rpx;
  height: 4rpx;
  background: rgba(255, 255, 255, 0.1);
  margin: 0 20rpx;
  margin-top: -46rpx;
  transition: all 0.3s ease;
}

.step-line.completed {
  background: #4ade80;
}

/* ==================== 导入卡片 ==================== */
.import-card {
  margin-bottom: 40rpx;
}

.card-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.card-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #e0e0e0;
  margin-bottom: 12rpx;
}

.card-subtitle {
  display: block;
  font-size: 26rpx;
  color: #888888;
}

.card-actions {
  display: flex;
  gap: 16rpx;
  margin-top: 40rpx;
}

/* ==================== 授权部分 ==================== */
.auth-section {
  margin-bottom: 40rpx;
}

.auth-login {
  text-align: center;
  padding: 40rpx 20rpx;
}

.auth-icon {
  font-size: 100rpx;
  margin-bottom: 32rpx;
}

.auth-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #e0e0e0;
  margin-bottom: 16rpx;
}

.auth-desc {
  display: block;
  font-size: 26rpx;
  color: #888888;
  line-height: 1.5;
  margin-bottom: 40rpx;
}

.auth-features {
  text-align: left;
}

.feature-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.feature-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.feature-text {
  font-size: 26rpx;
  color: #e0e0e0;
}

.auth-success {
  text-align: center;
  padding: 40rpx 20rpx;
}

.success-icon {
  font-size: 100rpx;
  margin-bottom: 32rpx;
}

.success-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #4ade80;
  margin-bottom: 16rpx;
}

.success-desc {
  display: block;
  font-size: 26rpx;
  color: #888888;
  margin-bottom: 24rpx;
}

.user-info {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16rpx;
  padding: 20rpx;
}

.user-email {
  font-size: 24rpx;
  color: #e0e0e0;
}

/* ==================== 文件浏览器 ==================== */
.file-browser {
  margin-bottom: 40rpx;
}

.current-path {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16rpx;
  margin-bottom: 24rpx;
}

.path-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.path-text {
  font-size: 26rpx;
  color: #e0e0e0;
  font-family: monospace;
}

.loading-state,
.empty-state {
  text-align: center;
  padding: 80rpx 40rpx;
}

.loading-icon,
.empty-icon {
  display: block;
  font-size: 80rpx;
  margin-bottom: 24rpx;
}

.loading-text,
.empty-text {
  display: block;
  font-size: 28rpx;
  color: #e0e0e0;
  margin-bottom: 12rpx;
}

.empty-desc {
  display: block;
  font-size: 24rpx;
  color: #888888;
}

.file-list {
  max-height: 600rpx;
  overflow-y: auto;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  border-radius: 16rpx;
  margin-bottom: 12rpx;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.02);
}

.file-item:active {
  background: rgba(255, 255, 255, 0.08);
}

.file-item.selected {
  background: rgba(109, 74, 255, 0.2);
  border: 2rpx solid #6d4aff;
}

.file-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
}

.file-info {
  flex: 1;
}

.file-name {
  display: block;
  font-size: 28rpx;
  color: #e0e0e0;
  margin-bottom: 8rpx;
}

.file-size,
.file-date {
  display: block;
  font-size: 22rpx;
  color: #888888;
  margin-bottom: 4rpx;
}

.file-action {
  font-size: 32rpx;
  color: #888888;
}

/* ==================== 密码表单 ==================== */
.password-form {
  margin-bottom: 40rpx;
}

.selected-file-info {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16rpx;
  margin-bottom: 32rpx;
}

.selected-file-info .file-icon {
  font-size: 48rpx;
  margin-right: 20rpx;
}

.file-details {
  flex: 1;
}

.file-details .file-name {
  font-size: 28rpx;
  color: #e0e0e0;
  margin-bottom: 8rpx;
}

.file-path {
  font-size: 22rpx;
  color: #888888;
  font-family: monospace;
}

/* ==================== 导入进度 ==================== */
.import-progress {
  padding: 40rpx 20rpx;
}

.progress-steps {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  opacity: 0.5;
  transition: all 0.3s ease;
}

.progress-step.active {
  opacity: 1;
}

.step-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
}

.step-text {
  font-size: 24rpx;
  color: #e0e0e0;
  margin-bottom: 8rpx;
}

.step-progress {
  font-size: 20rpx;
  color: #6d4aff;
  font-weight: 600;
}

/* ==================== 导入结果 ==================== */
.import-result {
  text-align: center;
  padding: 40rpx;
}

.result-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
}

.result-icon.success {
  color: #4ade80;
}

.result-icon.error {
  color: #ff6b6b;
}

.result-message {
  display: block;
  font-size: 28rpx;
  color: #e0e0e0;
  margin-bottom: 32rpx;
}

.result-stats {
  display: flex;
  justify-content: space-around;
  margin: 40rpx 0;
  padding: 32rpx;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20rpx;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 48rpx;
  font-weight: 600;
  color: #6d4aff;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #888888;
}

/* ==================== 帮助信息 ==================== */
.import-help {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20rpx;
  padding: 32rpx;
  margin-top: 40rpx;
}

.help-title {
  display: block;
  font-size: 28rpx;
  color: #e0e0e0;
  font-weight: 600;
  margin-bottom: 20rpx;
}

.help-item {
  display: block;
  font-size: 24rpx;
  color: #888888;
  margin-bottom: 12rpx;
  line-height: 1.5;
}
