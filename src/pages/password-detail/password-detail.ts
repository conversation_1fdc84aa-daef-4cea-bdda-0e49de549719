/**
 * VaultKeeper 密码详情页面 - Vue 3 Composition API 版本
 * 完美还原原型设计，查看和编辑密码信息
 */

import { definePage, ref, onLoad, onShow } from '@vue-mini/core'
import { useAuthStore } from '../../store/auth'
import { usePasswordStore } from '../../store/password'
import { useSettingsStore } from '../../store/settings'

definePage({
  setup() {
    // 获取 store 实例
    const authStore = useAuthStore()
    const passwordStore = usePasswordStore()
    const settingsStore = useSettingsStore()

    // 系统信息
    const statusBarHeight = ref(44)
    const safeAreaBottom = ref(34)

    // 密码信息
    const password = ref(null)
    const passwordId = ref('')

    // 显示控制
    const passwordVisible = ref(false)
    const passwordMask = ref('')

    // 图标信息
    const iconText = ref('')
    const iconColor = ref('#6d4aff')

    // 分类信息
    const categoryName = ref('')
    const categoryIcon = ref('folder')

    // 强度信息
    const strengthLevel = ref('medium')
    const strengthText = ref('中等')
    const strengthFeedback = ref([])

    // 时间信息
    const createdAtText = ref('')
    const updatedAtText = ref('')
    const lastUsedAtText = ref('')
    const expiresAtText = ref('')
    const isExpired = ref(false)
    const isExpiringSoon = ref(false)

    // 自定义字段和附件
    const customFieldsVisible = ref<Record<string, boolean>>({})

    // 安全分析
    const securityWarnings = ref([])

    // 模态框
    const showDeleteModal = ref(false)

    // 加载状态
    const loading = ref(true)

    /**
     * 页面加载时初始化
     */
    onLoad(async (options: any) => {
      try {
        console.log('📱 初始化密码详情页面')

        // 初始化密码 store（如果还没有初始化）
        if (passwordStore.passwords.length === 0) {
          await passwordStore.initialize()
        }

        // 获取密码ID
        const id = options.id
        if (!id) {
          console.error('❌ 缺少密码ID参数')
          wx.showToast({
            title: '参数错误',
            icon: 'error'
          })
          wx.navigateBack()
          return
        }

        passwordId.value = id

        // 获取系统信息
        const systemInfo = wx.getSystemInfoSync()
        statusBarHeight.value = systemInfo.statusBarHeight || 44
        safeAreaBottom.value = systemInfo.safeArea?.bottom ?
          systemInfo.screenHeight - systemInfo.safeArea.bottom : 34

        // 加载密码详情
        await loadPasswordDetail(id)

        // 更新活跃时间
        await authStore.updateActiveTime()

      } catch (error) {
        console.error('❌ 密码详情页面初始化失败:', error)
        wx.showToast({
          title: '初始化失败',
          icon: 'error'
        })
      }
    })

    /**
     * 页面显示时更新数据
     */
    onShow(async () => {
      try {
        // 检查会话有效性
        await authStore.checkSessionTimeout()
        await authStore.checkAutoLock()

        // 重新加载密码详情（可能被编辑过）
        if (passwordId.value) {
          await loadPasswordDetail(passwordId.value)
        }

        // 更新活跃时间
        await authStore.updateActiveTime()

      } catch (error) {
        console.error('❌ 页面显示更新失败:', error)
      }
    })

    /**
     * 加载密码详情
     */
    const loadPasswordDetail = async (id: string) => {
      try {
        console.log('🔍 加载密码详情:', id)

        // 从 store 获取密码信息
        const passwordData = passwordStore.getPassword(id)

        if (!passwordData) {
          console.error('❌ 密码不存在')
          wx.showToast({
            title: '密码不存在',
            icon: 'error'
          })
          wx.navigateBack()
          return
        }

        password.value = passwordData

        // 生成密码遮罩
        const maskLength = passwordData.password.length
        passwordMask.value = '•'.repeat(maskLength)

        // 更新显示数据
        updateDisplayData(passwordData)

        loading.value = false
        console.log('✅ 密码详情加载完成')

      } catch (error) {
        console.error('❌ 加载密码详情失败:', error)
        loading.value = false
        throw error
      }
    }

    /**
     * 更新显示数据
     */
    const updateDisplayData = (passwordData: any) => {
      // 生成图标信息
      const iconData = generateIconData(passwordData)
      iconText.value = iconData.text
      iconColor.value = iconData.color

      // 获取分类信息
      const categoryData = getCategoryData(passwordData.category)
      categoryName.value = categoryData.name
      categoryIcon.value = categoryData.icon

      // 格式化密码强度
      if (passwordData.strength) {
        const strengthData = formatPasswordStrength(passwordData.strength)
        strengthLevel.value = strengthData.level
        strengthText.value = strengthData.text
        strengthFeedback.value = strengthData.feedback
      }

      // 格式化时间
      createdAtText.value = formatDateTime(passwordData.createdAt)
      updatedAtText.value = formatDateTime(passwordData.updatedAt)
      lastUsedAtText.value = passwordData.lastUsedAt ? formatDateTime(passwordData.lastUsedAt) : '从未使用'

      // 处理过期时间
      if (passwordData.expiresAt) {
        expiresAtText.value = formatDateTime(passwordData.expiresAt)
        const expiryDate = new Date(passwordData.expiresAt)
        const now = new Date()
        const diffMs = expiryDate.getTime() - now.getTime()
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

        isExpired.value = diffMs < 0
        isExpiringSoon.value = diffDays >= 0 && diffDays <= 30 // 30天内过期
      }

      // 检查安全警告
      const warnings = checkSecurityWarnings(passwordData)
      securityWarnings.value = warnings
    }

    /**
     * 生成图标数据
     */
    const generateIconData = (passwordData: any) => {
      if (passwordData.icon) {
        return { text: '', color: passwordData.iconColor || '#6d4aff' }
      }

      const title = passwordData.title || 'P'
      const firstChar = title.charAt(0).toUpperCase()

      const colors = [
        '#6d4aff', '#3742fa', '#2ed573', '#ffa502',
        '#ff4757', '#8c66ff', '#00d4aa', '#ff6b7a'
      ]
      const colorIndex = firstChar.charCodeAt(0) % colors.length

      return {
        text: firstChar,
        color: colors[colorIndex]
      }
    }

    /**
     * 获取分类数据
     */
    const getCategoryData = (categoryId: string) => {
      const categoryMap: Record<string, { name: string; icon: string }> = {
        common: { name: '常用', icon: 'star' },
        work: { name: '工作', icon: 'briefcase' },
        social: { name: '社交', icon: 'users' },
        finance: { name: '金融', icon: 'credit-card' },
        shopping: { name: '购物', icon: 'shopping-cart' },
        entertainment: { name: '娱乐', icon: 'play' },
        other: { name: '其他', icon: 'folder' }
      }

      return categoryMap[categoryId] || { name: '其他', icon: 'folder' }
    }

    /**
     * 格式化密码强度
     */
    const formatPasswordStrength = (strength: any) => {
      const levelMap: Record<string, { level: string; text: string }> = {
        'weak': { level: 'weak', text: '弱' },
        'medium': { level: 'medium', text: '中等' },
        'strong': { level: 'strong', text: '强' },
        'very-strong': { level: 'very-strong', text: '很强' }
      }

      const result = levelMap[strength.level] || { level: 'medium', text: '中等' }

      return {
        ...result,
        feedback: strength.feedback || []
      }
    }

    /**
     * 格式化日期时间
     */
    const formatDateTime = (dateString: string) => {
      const date = new Date(dateString)
      const now = new Date()
      const diffMs = now.getTime() - date.getTime()
      const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

      if (diffDays === 0) {
        return '今天 ' + date.toLocaleTimeString('zh-CN', {
          hour: '2-digit',
          minute: '2-digit'
        })
      } else if (diffDays === 1) {
        return '昨天 ' + date.toLocaleTimeString('zh-CN', {
          hour: '2-digit',
          minute: '2-digit'
        })
      } else if (diffDays < 7) {
        return `${diffDays}天前`
      } else {
        return date.toLocaleDateString('zh-CN')
      }
    }

    /**
     * 检查安全警告
     */
    const checkSecurityWarnings = (passwordData: any) => {
      const warnings = []

      // 检查弱密码
      if (passwordData.strength && passwordData.strength.score < 60) {
        warnings.push({
          type: 'weak',
          icon: 'alert-triangle',
          title: '弱密码警告',
          description: '此密码强度较低，建议使用更复杂的密码'
        })
      }

      return warnings
    }

    /**
     * 处理返回
     */
    const onBackTap = () => {
      console.log('⬅️ 返回上一页')
      wx.navigateBack()
    }

    /**
     * 处理编辑
     */
    const onEditTap = () => {
      console.log('✏️ 编辑密码')
      wx.navigateTo({
        url: `/pages/add-password/add-password?id=${passwordId.value}&mode=edit`
      })
    }

    /**
     * 处理收藏切换
     */
    const onFavoriteTap = async () => {
      try {
        console.log('⭐ 切换收藏状态')

        await passwordStore.toggleFavorite(passwordId.value)

        // 重新加载密码详情
        await loadPasswordDetail(passwordId.value)

        wx.showToast({
          title: password.value.isFavorite ? '已取消收藏' : '已添加收藏',
          icon: 'success',
          duration: 1500
        })

      } catch (error) {
        console.error('❌ 切换收藏失败:', error)
        wx.showToast({
          title: '操作失败',
          icon: 'error'
        })
      }
    }

    /**
     * 切换密码显示
     */
    const onTogglePassword = () => {
      console.log('👁️ 切换密码显示')
      passwordVisible.value = !passwordVisible.value

      // 触觉反馈
      if (wx.vibrateShort) {
        wx.vibrateShort({ type: 'light' })
      }
    }

    /**
     * 复制用户名
     */
    const onCopyUsername = async () => {
      try {
        console.log('📋 复制用户名')

        await wx.setClipboardData({
          data: password.value.username
        })

        wx.showToast({
          title: '用户名已复制',
          icon: 'success',
          duration: 1500
        })

      } catch (error) {
        console.error('❌ 复制用户名失败:', error)
        wx.showToast({
          title: '复制失败',
          icon: 'error'
        })
      }
    }

    /**
     * 复制密码
     */
    const onCopyPassword = async () => {
      try {
        console.log('📋 复制密码')

        await wx.setClipboardData({
          data: password.value.password
        })

        // 标记为已使用
        await passwordStore.markAsUsed(passwordId.value)

        wx.showToast({
          title: '密码已复制',
          icon: 'success',
          duration: 1500
        })

        // 设置剪贴板清除定时器
        const clearTimeout = settingsStore.securitySettings.clearClipboardTimeout
        if (clearTimeout > 0) {
          setTimeout(() => {
            wx.setClipboardData({ data: '' })
          }, clearTimeout * 1000)
        }

      } catch (error) {
        console.error('❌ 复制密码失败:', error)
        wx.showToast({
          title: '复制失败',
          icon: 'error'
        })
      }
    }

    /**
     * 处理删除
     */
    const onDeleteTap = () => {
      console.log('🗑️ 删除密码')
      showDeleteModal.value = true
    }

    /**
     * 关闭删除模态框
     */
    const onDeleteModalClose = () => {
      showDeleteModal.value = false
    }

    /**
     * 确认删除
     */
    const onConfirmDelete = async () => {
      try {
        console.log('🗑️ 确认删除密码')

        await passwordStore.deletePassword(passwordId.value)

        showDeleteModal.value = false

        wx.showToast({
          title: '密码已删除',
          icon: 'success'
        })

        // 返回上一页
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)

      } catch (error) {
        console.error('❌ 删除密码失败:', error)
        wx.showToast({
          title: '删除失败',
          icon: 'error'
        })
      }
    }

    /**
     * 切换自定义字段显示
     */
    const onToggleCustomField = (event: any) => {
      const fieldName = event.currentTarget.dataset.field
      customFieldsVisible.value[fieldName] = !customFieldsVisible.value[fieldName]
    }

    /**
     * 复制自定义字段
     */
    const onCopyCustomField = async (event: any) => {
      try {
        const value = event.currentTarget.dataset.value
        await wx.setClipboardData({ data: value })
        wx.showToast({
          title: '已复制',
          icon: 'success',
          duration: 1500
        })
      } catch (error) {
        console.error('❌ 复制自定义字段失败:', error)
        wx.showToast({
          title: '复制失败',
          icon: 'error'
        })
      }
    }

    /**
     * 复制网站地址
     */
    const onCopyWebsite = async () => {
      try {
        await wx.setClipboardData({ data: password.value.website })
        wx.showToast({
          title: '网站地址已复制',
          icon: 'success',
          duration: 1500
        })
      } catch (error) {
        console.error('❌ 复制网站地址失败:', error)
        wx.showToast({
          title: '复制失败',
          icon: 'error'
        })
      }
    }

    /**
     * 打开网站
     */
    const onOpenWebsite = () => {
      if (password.value.website) {
        wx.setClipboardData({
          data: password.value.website,
          success: () => {
            wx.showToast({
              title: '网址已复制，请在浏览器中打开',
              icon: 'success',
              duration: 2000
            })
          }
        })
      }
    }

    /**
     * 查看附件
     */
    const onViewAttachment = (event: any) => {
      const attachment = event.currentTarget.dataset.attachment
      console.log('👁️ 查看附件:', attachment.name)

      wx.showModal({
        title: '查看附件',
        content: `附件名称：${attachment.name}\n文件大小：${formatFileSize(attachment.size)}\n文件类型：${attachment.mimeType || '未知'}`,
        showCancel: false,
        confirmText: '确定'
      })
    }

    /**
     * 保存附件
     */
    const onSaveAttachment = (event: any) => {
      const attachment = event.currentTarget.dataset.attachment
      console.log('💾 保存附件:', attachment.name)

      wx.showToast({
        title: '小程序暂不支持保存附件',
        icon: 'none',
        duration: 2000
      })
    }

    /**
     * 格式化文件大小
     */
    const formatFileSize = (bytes: number): string => {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }

    /**
     * 获取附件图标
     */
    const getAttachmentIcon = (mimeType?: string): string => {
      if (!mimeType) return 'file'

      if (mimeType.startsWith('image/')) return 'image'
      if (mimeType.startsWith('video/')) return 'video'
      if (mimeType.startsWith('audio/')) return 'music'
      if (mimeType.includes('pdf')) return 'file-pdf'
      if (mimeType.includes('word')) return 'file-word'
      if (mimeType.includes('excel') || mimeType.includes('spreadsheet')) return 'file-excel'
      if (mimeType.includes('powerpoint') || mimeType.includes('presentation')) return 'file-powerpoint'
      if (mimeType.includes('zip') || mimeType.includes('rar')) return 'file-archive'
      if (mimeType.includes('text')) return 'file-alt'

      return 'file'
    }

    return {
      // 响应式数据
      statusBarHeight,
      safeAreaBottom,
      password,
      passwordId,
      passwordVisible,
      passwordMask,
      iconText,
      iconColor,
      categoryName,
      categoryIcon,
      strengthLevel,
      strengthText,
      strengthFeedback,
      createdAtText,
      updatedAtText,
      lastUsedAtText,
      expiresAtText,
      isExpired,
      isExpiringSoon,
      customFieldsVisible,
      securityWarnings,
      showDeleteModal,
      loading,

      // 方法
      onBackTap,
      onEditTap,
      onFavoriteTap,
      onTogglePassword,
      onCopyUsername,
      onCopyPassword,
      onCopyWebsite,
      onOpenWebsite,
      onToggleCustomField,
      onCopyCustomField,
      onViewAttachment,
      onSaveAttachment,
      onDeleteTap,
      onDeleteModalClose,
      onConfirmDelete,
      formatFileSize,
      getAttachmentIcon
    }
  }
})
