<!-- VaultKeeper 密码详情页面
  使用 Tailwind CSS 重构，完美还原原型设计 -->
<view class="min-h-screen bg-gradient-to-br from-dark-primary to-dark-secondary">
  <!-- 自定义导航栏 -->
  <glass-card custom-class="fixed top-0 left-0 right-0 z-50 border-b border-white/10" custom-style="padding-top: {{statusBarHeight}}px;">
    <view class="flex items-center justify-between px-4 py-3">
      <view class="flex items-center">
        <view class="w-10 h-10 rounded-lg bg-white/10 flex items-center justify-center mr-3 transition-colors duration-200 active:bg-white/20" bind:tap="onBackTap">
          <text class="fas fa-arrow-left text-white"></text>
        </view>
        <text class="text-lg font-semibold text-white">密码详情</text>
      </view>
      <view class="flex items-center space-x-2">
        <view class="w-10 h-10 rounded-lg bg-white/10 flex items-center justify-center transition-colors duration-200 active:bg-white/20" bind:tap="onEditTap">
          <text class="fas fa-pen text-white"></text>
        </view>
        <view class="w-10 h-10 rounded-lg bg-white/10 flex items-center justify-center transition-colors duration-200 active:bg-white/20" bind:tap="onMoreTap">
          <text class="fas fa-ellipsis-h text-white"></text>
        </view>
      </view>
    </view>
  </glass-card>
  <!-- 主要内容 -->
  <scroll-view class="h-screen pt-20 pb-40" scroll-y="{{true}}">
    <view wx:if="{{password}}" class="p-5">
      <!-- 密码头部信息 -->
      <glass-card padding="24rpx" custom-class="mb-6">
        <view class="flex items-start">
          <!-- 网站图标 -->
          <view class="w-16 h-16 rounded-xl flex items-center justify-center mr-4 text-2xl" style="background-color: {{iconColor}};">
            <text wx:if="{{password.icon}}" class="{{password.iconType || 'fab'}} fa-{{password.icon}}"></text>
            <text wx:else class="text-white font-bold">{{iconText}}</text>
          </view>
          <!-- 基本信息 -->
          <view class="flex-1">
            <text class="text-xl font-bold text-white mb-1">{{password.title}}</text>
            <text wx:if="{{password.website}}" class="block text-gray-400 text-sm mb-3">
              {{password.website}}
            </text>
            <!-- 分类和收藏 -->
            <view class="flex items-center justify-between">
              <view class="flex items-center">
                <text class="fas fa-{{categoryIcon}} text-gray-400 mr-1"></text>
                <text class="text-gray-400 text-sm">{{categoryName}}</text>
              </view>
              <view class="flex items-center px-3 py-1.5 rounded-lg transition-colors duration-200 {{password.isFavorite ? 'bg-red-500/20 text-red-400' : 'bg-white/10 text-gray-400'}} active:scale-95" bind:tap="onFavoriteTap">
                <text class="{{password.isFavorite ? 'fas' : 'far'}} fa-heart mr-1"></text>
                <text class="text-sm">{{password.isFavorite ? '已收藏' : '收藏'}}</text>
              </view>
            </view>
          </view>
        </view>
      </glass-card>
      <!-- 密码信息 -->
      <view class="mb-6">
        <text class="block text-lg font-semibold text-white mb-4 px-1">登录信息</text>
        <!-- 用户名 -->
        <glass-card padding="16rpx" custom-class="mb-3">
          <view class="flex items-center justify-between">
            <view class="flex-1">
              <text class="block text-sm text-gray-400 mb-1">用户名</text>
              <text class="text-white font-medium">{{password.username || '无'}}</text>
            </view>
            <view wx:if="{{password.username}}" class="ml-3">
              <view class="w-10 h-10 rounded-lg bg-white/10 flex items-center justify-center transition-colors duration-200 active:bg-white/20" bind:tap="onCopyUsername">
                <text class="far fa-copy text-gray-300"></text>
              </view>
            </view>
          </view>
        </glass-card>
        <!-- 密码 -->
        <glass-card padding="16rpx" custom-class="mb-3">
          <view class="flex items-start justify-between">
            <view class="flex-1">
              <text class="block text-sm text-gray-400 mb-1">密码</text>
              <text class="text-white font-medium font-mono {{passwordVisible ? '' : 'password-hidden'}} mb-2">
                {{passwordVisible ? password.password : passwordMask}}
              </text>
              <!-- 密码强度 -->
              <view wx:if="{{password.strength}}" class="flex items-center">
                <view class="w-16 h-1 bg-gray-600 rounded-full mr-2 overflow-hidden">
                  <view class="strength-meter-fill strength-{{strengthLevel}}"></view>
                </view>
                <text class="text-xs text-gray-400">强度：{{strengthText}}</text>
              </view>
            </view>
            <view class="flex items-center space-x-2 ml-3">
              <view class="w-10 h-10 rounded-lg bg-white/10 flex items-center justify-center transition-colors duration-200 active:bg-white/20" bind:tap="onTogglePassword">
                <text class="far fa-{{passwordVisible ? 'eye-slash' : 'eye'}} text-gray-300"></text>
              </view>
              <view class="w-10 h-10 rounded-lg bg-white/10 flex items-center justify-center transition-colors duration-200 active:bg-white/20" bind:tap="onCopyPassword">
                <text class="far fa-copy text-gray-300"></text>
              </view>
            </view>
          </view>
        </glass-card>
        <!-- 网站地址 -->
        <glass-card wx:if="{{password.website}}" padding="16rpx">
          <view class="flex items-center justify-between">
            <view class="flex-1">
              <text class="block text-sm text-gray-400 mb-1">网站地址</text>
              <text class="text-primary-400 font-medium">{{password.website}}</text>
            </view>
            <view class="flex items-center space-x-2 ml-3">
              <view class="w-10 h-10 rounded-lg bg-white/10 flex items-center justify-center transition-colors duration-200 active:bg-white/20" bind:tap="onOpenWebsite">
                <text class="fas fa-external-link-alt text-gray-300"></text>
              </view>
              <view class="w-10 h-10 rounded-lg bg-white/10 flex items-center justify-center transition-colors duration-200 active:bg-white/20" bind:tap="onCopyWebsite">
                <text class="far fa-copy text-gray-300"></text>
              </view>
            </view>
          </view>
        </glass-card>
      </view>
      <!-- 备注信息 -->
      <view wx:if="{{password.notes}}" class="mb-6">
        <text class="block text-lg font-semibold text-white mb-4 px-1">备注</text>
        <glass-card padding="16rpx">
          <text class="text-gray-300 leading-relaxed">{{password.notes}}</text>
        </glass-card>
      </view>
      <!-- 安全分析 -->
      <view class="mb-6">
        <text class="block text-lg font-semibold text-white mb-4 px-1">安全分析</text>
        <glass-card padding="16rpx">
          <!-- 密码强度详情 -->
          <view wx:if="{{password.strength}}" class="mb-4">
            <view class="flex items-center justify-between mb-3">
              <text class="text-white font-medium">密码强度</text>
              <text class="text-sm px-2 py-1 rounded-lg {{strengthLevel === 'strong' ? 'bg-green-500/20 text-green-400' : strengthLevel === 'medium' ? 'bg-yellow-500/20 text-yellow-400' : 'bg-red-500/20 text-red-400'}}">
                {{strengthText}}
              </text>
            </view>
            <view class="space-y-1">
              <text wx:for="{{strengthFeedback}}" wx:key="*this" class="block text-sm text-gray-400">
                • {{item}}
              </text>
            </view>
          </view>
          <!-- 安全警告 -->
          <view wx:if="{{securityWarnings.length > 0}}" class="space-y-3">
            <view wx:for="{{securityWarnings}}" wx:key="type" class="flex items-start p-3 rounded-lg {{item.type === 'danger' ? 'bg-red-500/10' : item.type === 'warning' ? 'bg-yellow-500/10' : 'bg-blue-500/10'}}">
              <view class="w-8 h-8 rounded-lg flex items-center justify-center mr-3 {{item.type === 'danger' ? 'bg-red-500/20 text-red-400' : item.type === 'warning' ? 'bg-yellow-500/20 text-yellow-400' : 'bg-blue-500/20 text-blue-400'}}">
                <text class="fas fa-{{item.icon}} text-sm"></text>
              </view>
              <view class="flex-1">
                <text class="block text-white font-medium mb-1">{{item.title}}</text>
                <text class="text-gray-400 text-sm">{{item.description}}</text>
              </view>
            </view>
          </view>
        </glass-card>
      </view>
      <!-- 自定义字段 -->
      <view wx:if="{{password.customFields && password.customFields.length > 0}}" class="mb-6">
        <text class="block text-lg font-semibold text-white mb-4 px-1">自定义字段</text>
        <view class="space-y-3">
          <glass-card wx:for="{{password.customFields}}" wx:key="name" padding="16rpx">
            <view class="flex items-center justify-between">
              <view class="flex-1">
                <text class="block text-sm text-gray-400 mb-1">{{item.name}}</text>
                <text class="text-white font-medium {{item.protected ? 'font-mono' : ''}}">
                  {{item.protected && !customFieldsVisible[item.name] ? '••••••••' : item.value}}
                </text>
              </view>
              <view class="flex items-center space-x-2 ml-3">
                <view wx:if="{{item.protected}}" class="w-10 h-10 rounded-lg bg-white/10 flex items-center justify-center transition-colors duration-200 active:bg-white/20" bind:tap="onToggleCustomField" data-field="{{item.name}}">
                  <text class="far fa-{{customFieldsVisible[item.name] ? 'eye-slash' : 'eye'}} text-gray-300"></text>
                </view>
                <view class="w-10 h-10 rounded-lg bg-white/10 flex items-center justify-center transition-colors duration-200 active:bg-white/20" bind:tap="onCopyCustomField" data-value="{{item.value}}">
                  <text class="far fa-copy text-gray-300"></text>
                </view>
              </view>
            </view>
          </glass-card>
        </view>
      </view>
      <!-- 附件 -->
      <view wx:if="{{password.attachments && password.attachments.length > 0}}" class="mb-6">
        <text class="block text-lg font-semibold text-white mb-4 px-1">附件</text>
        <view class="space-y-3">
          <glass-card wx:for="{{password.attachments}}" wx:key="name" padding="16rpx">
            <view class="flex items-center justify-between">
              <view class="flex items-center flex-1">
                <view class="w-10 h-10 rounded-lg bg-white/10 flex items-center justify-center mr-3">
                  <text class="fas fa-{{getAttachmentIcon(item.mimeType)}} text-gray-300"></text>
                </view>
                <view class="flex-1">
                  <text class="block text-white font-medium">{{item.name}}</text>
                  <text class="text-gray-400 text-sm">{{formatFileSize(item.size)}}</text>
                </view>
              </view>
              <view class="flex items-center space-x-2 ml-3">
                <view class="w-10 h-10 rounded-lg bg-white/10 flex items-center justify-center transition-colors duration-200 active:bg-white/20" bind:tap="onViewAttachment" data-attachment="{{item}}">
                  <text class="far fa-eye text-gray-300"></text>
                </view>
                <view class="w-10 h-10 rounded-lg bg-white/10 flex items-center justify-center transition-colors duration-200 active:bg-white/20" bind:tap="onSaveAttachment" data-attachment="{{item}}">
                  <text class="fas fa-download text-gray-300"></text>
                </view>
              </view>
            </view>
          </glass-card>
        </view>
      </view>
      <!-- 使用记录 -->
      <view class="mb-6">
        <text class="block text-lg font-semibold text-white mb-4 px-1">使用记录</text>
        <glass-card padding="16rpx">
          <view class="space-y-3">
            <view class="flex items-center justify-between">
              <text class="text-gray-400">创建时间</text>
              <text class="text-white">{{createdAtText}}</text>
            </view>
            <view class="flex items-center justify-between">
              <text class="text-gray-400">最后修改</text>
              <text class="text-white">{{updatedAtText}}</text>
            </view>
            <view wx:if="{{password.lastUsedAt}}" class="flex items-center justify-between">
              <text class="text-gray-400">最后使用</text>
              <text class="text-white">{{lastUsedAtText}}</text>
            </view>
            <view wx:if="{{password.expiresAt}}" class="flex items-center justify-between">
              <text class="text-gray-400">过期时间</text>
              <text class="text-white {{isExpired ? 'text-red-400' : isExpiringSoon ? 'text-yellow-400' : ''}}">
                {{expiresAtText}}
                <text wx:if="{{isExpired}}" class="ml-2 text-xs bg-red-500/20 text-red-400 px-2 py-1 rounded">
                  已过期
                </text>
                <text wx:elif="{{isExpiringSoon}}" class="ml-2 text-xs bg-yellow-500/20 text-yellow-400 px-2 py-1 rounded">
                  即将过期
                </text>
              </text>
            </view>
          </view>
        </glass-card>
      </view>
      <!-- 操作按钮 -->
      <view class="space-y-3 mb-6">
        <custom-button variant="secondary" size="lg" block="{{true}}" text="编辑密码" left-icon="edit" bind:tap="onEditTap" />
        <custom-button variant="danger" size="lg" block="{{true}}" text="删除密码" left-icon="trash" bind:tap="onDeleteTap" />
      </view>
    </view>
    <!-- 加载状态 -->
    <view wx:else class="flex flex-col items-center justify-center h-64">
      <view class="w-8 h-8 border-2 border-primary-500 border-t-transparent rounded-full animate-spin mb-4"></view>
      <text class="text-gray-400">加载中...</text>
    </view>
  </scroll-view>
  <!-- 删除确认模态框 -->
  <modal visible="{{showDeleteModal}}" title="删除密码" subtitle="此操作无法撤销" bind:close="onDeleteModalClose" bind:confirm="onConfirmDelete" confirm-text="删除" cancel-text="取消">
    <view class="p-6">
      <view class="flex flex-col items-center text-center">
        <view class="w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mb-4">
          <text class="fas fa-exclamation-triangle text-red-400 text-2xl"></text>
        </view>
        <text class="text-white">确定要删除密码 "{{password.title}}" 吗？</text>
      </view>
    </view>
  </modal>
</view>