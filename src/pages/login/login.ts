/**
 * VaultKeeper 登录页面
 * 完美还原原型设计，支持主密码和生物识别登录
 * 
 * 功能特性：
 * - 主密码验证登录
 * - 生物识别登录
 * - 首次设置向导
 * - 账户锁定保护
 * - 忘记密码处理
 */

import { definePage, ref, onLoad } from '@vue-mini/core'
import { useAuthStore } from '../../store/auth'
import { useSettingsStore } from '../../store/settings'
import { Validator } from '../../utils/validator'

definePage({
  /**
   * 页面设置
   */
  setup () {
    const authStore = useAuthStore()
    const settingsStore = useSettingsStore()

    console.log('🔐 登录页面初始化')

    // 响应式数据
    const masterPassword = ref('')
    const passwordError = ref('')
    const loginLoading = ref(false)

    // 生物识别
    const biometricSupported = ref(false)
    const biometricEnabled = ref(false)

    // 登录状态
    const loginAttempts = ref(0)
    const maxAttempts = ref(5)
    const isLocked = ref(false)
    const lockCountdown = ref('')

    // 首次设置
    const showSetupModal = ref(false)
    const newMasterPassword = ref('')
    const confirmMasterPassword = ref('')
    const newPasswordError = ref('')
    const confirmPasswordError = ref('')
    const setupLoading = ref(false)
    const canCreateVault = ref(false)

    // 忘记密码
    const showForgotModal = ref(false)

    // 应用信息
    const appVersion = ref('1.0.0')

    /**
     * 页面加载时初始化
     */
    onLoad(async () => {
      try {
        console.log('📱 初始化登录页面')

        // 初始化认证状态
        await authStore.initialize()

        // 检查是否已登录
        if (authStore.isLoggedIn) {
          console.log('✅ 用户已登录，跳转到主页')
          wx.reLaunch({ url: '/pages/home/<USER>' })
          return
        }

        // 检查是否首次使用
        const hasSetup = wx.getStorageSync('has_setup')
        if (!hasSetup) {
          console.log('🆕 首次使用，显示设置向导')
          showSetupModal.value = true
          return
        }

        // 更新页面数据
        biometricSupported.value = authStore.biometricSettings.supportedMethods.length > 0
        biometricEnabled.value = authStore.biometricSettings.enabled
        loginAttempts.value = authStore.loginAttempts
        maxAttempts.value = authStore.maxLoginAttempts
        isLocked.value = authStore.isLockedOut
        appVersion.value = settingsStore.appInfo.version

        // 如果账户被锁定，启动倒计时
        if (authStore.isLockedOut) {
          startLockCountdown()
        }

      } catch (error) {
        console.error('❌ 登录页面初始化失败:', error)
        wx.showToast({
          title: '初始化失败',
          icon: 'error'
        })
      }
    })

    /**
     * 处理密码输入
     */
    const onPasswordInput = (event) => {
      const value = event.detail.value
      masterPassword.value = value
      passwordError.value = ''
    }

    /**
     * 处理密码确认（回车登录）
     */
    const onPasswordConfirm = () => {
      if (masterPassword.value && !loginLoading.value) {
        onLogin()
      }
    }

    /**
     * 处理登录
     */
    const onLogin = async () => {
      try {
        const password = masterPassword.value.trim()

        // 验证密码
        if (!password) {
          passwordError.value = '请输入主密码'
          return
        }

        const validation = Validator.validatePasswordStrength(password)
        if (!validation.isValid) {
          passwordError.value = validation.errors[0]
          return
        }

        loginLoading.value = true
        passwordError.value = ''

        console.log('🔐 开始登录验证')

        // 调用认证 store 登录
        const success = await authStore.login(password)

        if (success) {
          console.log('✅ 登录成功')

          // 记录使用时间
          await settingsStore.recordUsageTime(1)

          // 跳转到主页
          wx.reLaunch({ url: '/pages/home/<USER>' })
        }

      } catch (error) {
        console.error('❌ 登录失败:', error)

        passwordError.value = error.message || '登录失败，请重试'
        loginAttempts.value = authStore.loginAttempts

        // 检查是否被锁定
        if (authStore.isLockedOut) {
          isLocked.value = true
          startLockCountdown()
        }

        // 清空密码
        masterPassword.value = ''

      } finally {
        loginLoading.value = false
      }
    }

    /**
     * 处理生物识别登录
     */
    const onBiometricLogin = async () => {
      try {
        console.log('👆 开始生物识别登录')

        const success = await authStore.biometricLogin()

        if (success) {
          console.log('✅ 生物识别登录成功')
          wx.reLaunch({ url: '/pages/home/<USER>' })
        }

      } catch (error) {
        console.error('❌ 生物识别登录失败:', error)

        wx.showToast({
          title: error.message || '生物识别失败',
          icon: 'error'
        })
      }
    }

    /**
     * 处理首次设置
     */
    const onFirstTimeSetup = () => {
      showSetupModal.value = true
    }

    /**
     * 处理新密码输入
     */
    const onNewPasswordInput = (event) => {
      const value = event.detail.value
      newMasterPassword.value = value
      newPasswordError.value = ''
      validateSetupForm()
    }

    /**
     * 处理确认密码输入
     */
    const onConfirmPasswordInput = (event) => {
      const value = event.detail.value
      confirmMasterPassword.value = value
      confirmPasswordError.value = ''
      validateSetupForm()
    }

    /**
     * 验证设置表单
     */
    const validateSetupForm = () => {
      let canCreate = false

      if (newMasterPassword.value && confirmMasterPassword.value) {
        // 验证密码强度
        const validation = Validator.validatePasswordStrength(newMasterPassword.value, 8)
        if (validation.isValid && newMasterPassword.value === confirmMasterPassword.value) {
          canCreate = true
        }
      }

      canCreateVault.value = canCreate
    }

    /**
     * 创建密码库
     */
    const onCreateVault = async () => {
      try {
        // 验证密码
        const validation = Validator.validatePasswordStrength(newMasterPassword.value, 8)
        if (!validation.isValid) {
          newPasswordError.value = validation.errors[0]
          return
        }

        if (newMasterPassword.value !== confirmMasterPassword.value) {
          confirmPasswordError.value = '两次输入的密码不一致'
          return
        }

        setupLoading.value = true

        console.log('🔐 创建新的密码库')

        // 初始化安全存储
        await authStore.login(newMasterPassword.value)

        // 标记已设置
        wx.setStorageSync('has_setup', true)

        console.log('✅ 密码库创建成功')

        // 关闭模态框并跳转
        showSetupModal.value = false
        wx.reLaunch({ url: '/pages/home/<USER>' })

      } catch (error) {
        console.error('❌ 创建密码库失败:', error)

        newPasswordError.value = error.message || '创建失败，请重试'

      } finally {
        setupLoading.value = false
      }
    }

    /**
     * 处理忘记密码
     */
    const onForgotPassword = () => {
      showForgotModal.value = true
    }

    /**
     * 重置密码库
     */
    const onResetVault = async () => {
      try {
        console.log('🗑️ 重置密码库')

        // 清除所有数据
        await authStore.logout()
        wx.clearStorageSync()

        // 显示设置向导
        showForgotModal.value = false
        showSetupModal.value = true
        newMasterPassword.value = ''
        confirmMasterPassword.value = ''
        newPasswordError.value = ''
        confirmPasswordError.value = ''

        wx.showToast({
          title: '密码库已重置',
          icon: 'success'
        })

      } catch (error) {
        console.error('❌ 重置密码库失败:', error)
        wx.showToast({
          title: '重置失败',
          icon: 'error'
        })
      }
    }

    /**
     * 启动锁定倒计时
     */
    const startLockCountdown = () => {
      const updateCountdown = () => {
        if (!authStore.lockoutEndTime) return

        const now = Date.now()
        const remaining = authStore.lockoutEndTime - now

        if (remaining <= 0) {
          isLocked.value = false
          lockCountdown.value = ''
          return
        }

        const minutes = Math.floor(remaining / (60 * 1000))
        const seconds = Math.floor((remaining % (60 * 1000)) / 1000)

        lockCountdown.value = `${minutes}分${seconds}秒`

        setTimeout(updateCountdown, 1000)
      }

      updateCountdown()
    }

    /**
     * 关闭模态框
     */
    const onSetupModalClose = () => {
      // 首次设置不允许关闭
    }

    const onForgotModalClose = () => {
      showForgotModal.value = false
    }

    // 返回响应式数据和方法
    return {
      // 响应式数据
      masterPassword,
      passwordError,
      loginLoading,
      biometricSupported,
      biometricEnabled,
      loginAttempts,
      maxAttempts,
      isLocked,
      lockCountdown,
      showSetupModal,
      newMasterPassword,
      confirmMasterPassword,
      newPasswordError,
      confirmPasswordError,
      setupLoading,
      canCreateVault,
      showForgotModal,
      appVersion,

      // 方法
      onPasswordInput,
      onPasswordConfirm,
      onLogin,
      onBiometricLogin,
      onFirstTimeSetup,
      onNewPasswordInput,
      onConfirmPasswordInput,
      onCreateVault,
      onForgotPassword,
      onResetVault,
      onSetupModalClose,
      onForgotModalClose,
      validateSetupForm,
      startLockCountdown
    }
  }
})
