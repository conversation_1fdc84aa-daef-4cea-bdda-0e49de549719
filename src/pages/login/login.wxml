<!-- VaultKeeper 登录页面
  使用 Tailwind CSS 重构，完美还原原型设计 -->
<view class="min-h-screen flex flex-col justify-center items-center p-6">
  <!-- 应用Logo和标题 - 与原型保持一致 -->
  <view class="text-center mb-12">
    <view class="mb-4">
      <view class="w-24 h-24 rounded-xl bg-white/10 mx-auto flex items-center justify-center">
        <text class="fas fa-shield-alt text-5xl text-primary-500"></text>
      </view>
    </view>
    <text class="text-2xl font-bold mb-2 text-white">VaultKeeper</text>
    <text class="text-gray-400 text-sm">安全管理您的所有密码</text>
  </view>
  <!-- 登录表单 - 与原型保持一致 -->
  <glass-card custom-class="w-full mb-6" padding="24rpx">
    <!-- 主密码输入 -->
    <view class="mb-5">
      <text class="block mb-2 text-sm text-gray-400">主密码</text>
      <input type="password" placeholder="输入您的主密码" value="{{masterPassword}}" bind:input="onPasswordInput" bind:confirm="onPasswordConfirm" class="form-input-dark w-full px-4 py-3.5 rounded-xl text-white text-base transition-colors duration-200" />
    </view>
    <!-- 错误提示 -->
    <view wx:if="{{passwordError}}" class="flex items-center text-red-400 text-sm mb-4">
      <text class="fas fa-exclamation-triangle mr-2"></text>
      <text>{{passwordError}}</text>
    </view>
    <!-- 登录按钮 - 与原型保持一致 -->
    <view class="mb-6">
      <button class="w-full bg-primary-500 text-white px-6 py-3.5 rounded-xl font-semibold text-base transition-all duration-200 {{!masterPassword || loginLoading ? 'opacity-50' : 'active:scale-98 shadow-primary'}}" bind:tap="onLogin" disabled="{{!masterPassword || loginLoading}}">
        <text wx:if="{{loginLoading}}" class="fas fa-spinner animate-spin mr-2"></text>
        <text>{{loginLoading ? '验证中...' : '解锁'}}</text>
      </button>
    </view>
    <!-- 生物识别选项 - 与原型保持一致 -->
    <view wx:if="{{biometricSupported}}" class="flex justify-center">
      <button class="text-sm text-gray-400 hover:text-white transition-colors duration-200 {{!biometricEnabled ? 'opacity-50' : ''}}" bind:tap="onBiometricLogin" disabled="{{!biometricEnabled}}">
        <text>使用生物识别</text>
        <text class="fas fa-fingerprint ml-2"></text>
      </button>
    </view>
    <text wx:if="{{biometricSupported && !biometricEnabled}}" class="block text-center text-xs text-gray-500 mt-2">
      请先使用主密码登录以启用生物识别
    </text>
    <!-- 登录失败提示 -->
    <view wx:if="{{loginAttempts > 0}}" class="text-center text-sm text-yellow-400 mt-4">
      <text>登录失败 {{loginAttempts}} 次，还可尝试 {{maxAttempts - loginAttempts}} 次</text>
    </view>
    <!-- 账户锁定提示 -->
    <view wx:if="{{isLocked}}" class="text-center mt-4">
      <view class="flex justify-center mb-2">
        <text class="fas fa-lock text-red-400 text-lg"></text>
      </view>
      <text class="block text-red-400 font-medium mb-1">账户已锁定</text>
      <text class="block text-gray-400 text-sm mb-2">由于多次登录失败，账户已被锁定</text>
      <text class="block text-yellow-400 text-sm">请等待 {{lockCountdown}} 后重试</text>
    </view>
  </glass-card>
  <!-- 底部链接 -->
  <view class="mt-8 text-center">
    <view class="flex justify-center items-center space-x-4 mb-4">
      <text class="text-sm text-gray-400 hover:text-white transition-colors duration-200" bind:tap="onForgotPassword">
        忘记主密码？
      </text>
      <text class="text-gray-600">|</text>
      <text class="text-sm text-gray-400 hover:text-white transition-colors duration-200" bind:tap="onFirstTimeSetup">
        首次使用
      </text>
    </view>
    <text class="text-xs text-gray-500">版本 {{appVersion}}</text>
  </view>
</view>
<!-- 首次设置模态框 -->
<modal visible="{{showSetupModal}}" title="欢迎使用 VaultKeeper" subtitle="请设置您的主密码" closable="{{false}}" mask-closable="{{false}}" show-footer="{{false}}" bind:close="onSetupModalClose">
  <view class="p-6">
    <view class="mb-6">
      <text class="block text-lg font-semibold text-white mb-2">创建主密码</text>
      <text class="block text-sm text-gray-400 mb-6">主密码是保护您所有密码的关键，请务必牢记</text>
      <form-input label="主密码" type="password" value="{{newMasterPassword}}" placeholder="至少8位，包含字母和数字" required="{{true}}" bind:input="onNewPasswordInput" error-text="{{newPasswordError}}" help-text="建议使用12位以上的强密码" />
      <form-input label="确认主密码" type="password" value="{{confirmMasterPassword}}" placeholder="请再次输入主密码" required="{{true}}" bind:input="onConfirmPasswordInput" error-text="{{confirmPasswordError}}" />
    </view>
    <view>
      <custom-button variant="primary" size="lg" block="{{true}}" text="创建密码库" loading="{{setupLoading}}" disabled="{{!canCreateVault}}" bind:tap="onCreateVault" />
    </view>
  </view>
</modal>
<!-- 忘记密码模态框 -->
<modal visible="{{showForgotModal}}" title="忘记主密码" bind:close="onForgotModalClose" bind:confirm="onResetVault" confirm-text="重置密码库" cancel-text="取消">
  <view class="p-6">
    <view class="mb-6">
      <view class="flex items-center justify-center w-16 h-16 bg-red-500/20 rounded-full mx-auto mb-4">
        <text class="fas fa-exclamation-triangle text-red-400 text-2xl"></text>
      </view>
      <text class="block text-lg font-semibold text-white text-center mb-2">重要提醒</text>
      <text class="block text-sm text-gray-400 text-center">重置密码库将删除所有已保存的密码数据，此操作无法撤销。</text>
    </view>
    <text class="block text-sm text-gray-400 text-center">建议您先尝试回忆可能的密码组合，或查看是否有备份文件。</text>
  </view>
</modal>