<!-- VaultKeeper 安全分析页面
  使用 Tailwind CSS 重构，完美还原原型设计 -->
<view class="min-h-screen bg-gradient-to-br from-dark-primary to-dark-secondary">
  <!-- 页面内容 -->
  <scroll-view class="h-screen pb-40" scroll-y="{{true}}" refresher-enabled="{{true}}" refresher-triggered="{{refreshing}}" bind:refresherrefresh="onRefresh" style="padding-top: {{statusBarHeight + 20}}px;">
    <!-- 页面头部 -->
    <view class="text-center pt-8 pb-8 px-5">
      <text class="text-2xl font-bold text-white mb-2">安全评分</text>
      <text class="text-gray-400">分析您的密码健康状况</text>
    </view>
    <!-- 安全评分卡片 -->
    <view class="px-5 mb-6">
      <glass-card padding="24rpx">
        <view class="flex items-center mb-6">
          <!-- 安全评分圆环 -->
          <view class="relative w-24 h-24 rounded-full flex items-center justify-center mr-6 {{scoreLevel === 'excellent' ? 'bg-green-500/20 border-4 border-green-500' : scoreLevel === 'good' ? 'bg-blue-500/20 border-4 border-blue-500' : scoreLevel === 'fair' ? 'bg-yellow-500/20 border-4 border-yellow-500' : 'bg-red-500/20 border-4 border-red-500'}}">
            <text class="text-2xl font-bold {{scoreLevel === 'excellent' ? 'text-green-400' : scoreLevel === 'good' ? 'text-blue-400' : scoreLevel === 'fair' ? 'text-yellow-400' : 'text-red-400'}}">
              {{securityScore}}
            </text>
          </view>
          <!-- 评分描述 -->
          <view class="flex-1">
            <text class="block text-xl font-bold text-white mb-1">{{scoreLevelText}}</text>
            <text class="text-gray-400 text-sm">您的密码安全状况优于{{scorePercentage}}%的用户</text>
          </view>
        </view>
        <!-- 安全统计 -->
        <view class="space-y-3">
          <!-- 弱密码 -->
          <view class="flex items-center p-4 bg-red-500/10 rounded-xl transition-colors duration-200 active:bg-red-500/20" bind:tap="onWeakPasswordsTap">
            <view class="w-12 h-12 bg-red-500/20 rounded-xl flex items-center justify-center mr-4">
              <text class="fas fa-exclamation-circle text-red-400"></text>
            </view>
            <view class="flex-1">
              <text class="block text-white font-medium">{{weakPasswords.length}}个弱密码</text>
              <text class="text-gray-400 text-sm">这些密码容易被破解</text>
            </view>
            <text class="fas fa-chevron-right text-gray-400"></text>
          </view>
          <!-- 重复密码 -->
          <view class="flex items-center p-4 bg-yellow-500/10 rounded-xl transition-colors duration-200 active:bg-yellow-500/20" bind:tap="onDuplicatePasswordsTap">
            <view class="w-12 h-12 bg-yellow-500/20 rounded-xl flex items-center justify-center mr-4">
              <text class="fas fa-clone text-yellow-400"></text>
            </view>
            <view class="flex-1">
              <text class="block text-white font-medium">
                {{duplicatePasswords.length}}个重复使用的密码
              </text>
              <text class="text-gray-400 text-sm">在多个网站使用相同密码</text>
            </view>
            <text class="fas fa-chevron-right text-gray-400"></text>
          </view>
          <!-- 强密码 -->
          <view class="flex items-center p-4 bg-green-500/10 rounded-xl transition-colors duration-200 active:bg-green-500/20" bind:tap="onStrongPasswordsTap">
            <view class="w-12 h-12 bg-green-500/20 rounded-xl flex items-center justify-center mr-4">
              <text class="fas fa-shield-alt text-green-400"></text>
            </view>
            <view class="flex-1">
              <text class="block text-white font-medium">{{strongPasswords.length}}个强密码</text>
              <text class="text-gray-400 text-sm">这些密码足够安全</text>
            </view>
            <text class="fas fa-chevron-right text-gray-400"></text>
          </view>
        </view>
      </glass-card>
    </view>
    <!-- 需要注意的密码 -->
    <view wx:if="{{riskPasswords.length > 0}}" class="px-5 mb-6">
      <text class="block text-lg font-semibold text-white mb-4 px-1">需要注意</text>
      <view class="space-y-3">
        <glass-card wx:for="{{riskPasswords}}" wx:key="id" clickable="{{true}}" padding="16rpx" bind:tap="onRiskPasswordTap" data-password="{{item}}">
          <view class="flex items-center">
            <view class="w-12 h-12 rounded-xl flex items-center justify-center mr-4 text-lg" style="background-color: {{item.iconColor}}20; color: {{item.iconColor}};">
              <text class="fab fa-{{item.icon}}"></text>
            </view>
            <view class="flex-1 mr-3">
              <text class="block text-white font-medium mb-1">{{item.title}}</text>
              <text class="text-sm {{item.riskLevel === 'high' ? 'text-red-400' : item.riskLevel === 'medium' ? 'text-yellow-400' : 'text-gray-400'}}">
                {{item.riskText}}
              </text>
            </view>
            <custom-button variant="secondary" size="sm" text="修改" bind:tap="onFixPasswordTap" data-password="{{item}}" />
          </view>
        </glass-card>
      </view>
    </view>
    <!-- 安全提示 -->
    <view class="px-5 mb-6">
      <text class="block text-lg font-semibold text-white mb-4 px-1">安全提示</text>
      <view class="space-y-3">
        <!-- 启用两步验证 -->
        <glass-card clickable="{{true}}" padding="16rpx" bind:tap="onTwoFactorTip">
          <view class="flex items-center">
            <view class="w-12 h-12 bg-purple-500/20 rounded-xl flex items-center justify-center mr-4">
              <text class="fas fa-fingerprint text-purple-400"></text>
            </view>
            <view class="flex-1">
              <text class="block text-white font-medium">启用两步验证</text>
              <text class="text-gray-400 text-sm">为重要账号增加安全层级</text>
            </view>
            <text class="fas fa-chevron-right text-gray-400"></text>
          </view>
        </glass-card>
        <!-- 定期更换密码 -->
        <glass-card clickable="{{true}}" padding="16rpx" bind:tap="onPasswordRotationTip">
          <view class="flex items-center">
            <view class="w-12 h-12 bg-blue-500/20 rounded-xl flex items-center justify-center mr-4">
              <text class="fas fa-sync-alt text-blue-400"></text>
            </view>
            <view class="flex-1">
              <text class="block text-white font-medium">定期更换密码</text>
              <text class="text-gray-400 text-sm">建议每90天更新一次重要密码</text>
            </view>
            <text class="fas fa-chevron-right text-gray-400"></text>
          </view>
        </glass-card>
        <!-- 使用密码生成器 -->
        <glass-card clickable="{{true}}" padding="16rpx" bind:tap="onPasswordGeneratorTip">
          <view class="flex items-center">
            <view class="w-12 h-12 bg-green-500/20 rounded-xl flex items-center justify-center mr-4">
              <text class="fas fa-key text-green-400"></text>
            </view>
            <view class="flex-1">
              <text class="block text-white font-medium">使用密码生成器</text>
              <text class="text-gray-400 text-sm">生成更安全的随机密码</text>
            </view>
            <text class="fas fa-chevron-right text-gray-400"></text>
          </view>
        </glass-card>
      </view>
    </view>
    <!-- 空状态 -->
    <view wx:if="{{isEmpty}}" class="px-5 mb-6">
      <view class="text-center py-16">
        <view class="w-20 h-20 bg-primary-500/20 rounded-full flex items-center justify-center mx-auto mb-6">
          <text class="fas fa-shield-alt text-primary-400 text-3xl"></text>
        </view>
        <text class="block text-xl font-bold text-white mb-2">暂无密码数据</text>
        <text class="block text-gray-400 mb-8">添加密码后即可查看安全分析</text>
        <custom-button variant="primary" text="添加密码" bind:tap="onAddPasswordTap" />
      </view>
    </view>
  </scroll-view>
</view>