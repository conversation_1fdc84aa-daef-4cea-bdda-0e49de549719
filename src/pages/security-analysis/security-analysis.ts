/**
 * V<PERSON><PERSON>eeper 安全分析页面
 * 严格按照原型设计实现
 */

import { definePage, ref, computed, onShow, onLoad } from '@vue-mini/core'

definePage({
  setup() {
    // 系统信息
    const statusBarHeight = ref(44)

    // 状态管理
    const refreshing = ref(false)
    const loading = ref(true)

    // 安全数据
    const securityScore = ref(78) // 模拟数据
    const passwordAnalysis = ref({
      total: 28,
      weak: [
        {
          id: '1',
          title: 'Twitter',
          username: '@johndoe',
          icon: 'twitter',
          iconColor: '#1da1f2',
          strengthScore: 30
        },
        {
          id: '2',
          title: '某购物网站',
          username: 'user123',
          icon: 'shopping-cart',
          iconColor: '#ff6b35',
          strengthScore: 25
        }
      ],
      duplicate: [
        {
          id: '3',
          title: 'Facebook',
          username: 'john.doe',
          icon: 'facebook',
          iconColor: '#4267b2',
          duplicateCount: 3
        },
        {
          id: '4',
          title: 'Instagram',
          username: 'johndo<PERSON>',
          icon: 'instagram',
          iconColor: '#e4405f',
          duplicateCount: 2
        }
      ],
      strong: [
        {
          id: '5',
          title: 'Google',
          username: '<EMAIL>',
          icon: 'google',
          iconColor: '#4285f4',
          strengthScore: 95
        },
        {
          id: '6',
          title: 'GitHub',
          username: 'johndoe',
          icon: 'github',
          iconColor: '#333',
          strengthScore: 90
        }
      ],
      old: [
        {
          id: '7',
          title: 'Amazon',
          username: '<EMAIL>',
          icon: 'amazon',
          iconColor: '#ff9900',
          daysSinceUpdate: 120
        }
      ],
      compromised: []
    })

    // 计算属性
    const scoreLevel = computed(() => {
      if (securityScore.value >= 90) return 'excellent'
      if (securityScore.value >= 75) return 'good'
      if (securityScore.value >= 50) return 'fair'
      return 'poor'
    })

    const scoreLevelText = computed(() => {
      const level = scoreLevel.value
      return level === 'excellent' ? '优秀' :
        level === 'good' ? '良好' :
          level === 'fair' ? '一般' : '较差'
    })

    const scorePercentage = computed(() => {
      // 根据安全评分计算百分比
      return Math.min(95, Math.max(5, securityScore.value - 5))
    })

    const weakPasswords = computed(() => passwordAnalysis.value.weak)
    const duplicatePasswords = computed(() => passwordAnalysis.value.duplicate)
    const strongPasswords = computed(() => passwordAnalysis.value.strong)

    const riskPasswords = computed(() => {
      const risks = []

      // 添加弱密码
      passwordAnalysis.value.weak.forEach(password => {
        risks.push({
          ...password,
          riskLevel: 'high',
          riskText: '弱密码'
        })
      })

      // 添加重复密码
      passwordAnalysis.value.duplicate.forEach(password => {
        risks.push({
          ...password,
          riskLevel: 'medium',
          riskText: '重复使用的密码'
        })
      })

      // 添加过期密码
      passwordAnalysis.value.old.forEach(password => {
        risks.push({
          ...password,
          riskLevel: 'low',
          riskText: '密码较旧'
        })
      })

      // 按风险等级排序
      return risks.sort((a, b) => {
        const riskOrder = { high: 3, medium: 2, low: 1 }
        return riskOrder[b.riskLevel] - riskOrder[a.riskLevel]
      }).slice(0, 5) // 最多显示5个
    })

    const isEmpty = computed(() => {
      return passwordAnalysis.value.total === 0
    })

    /**
     * 页面加载
     */
    onLoad(async () => {
      try {
        console.log('🛡️ 安全分析页面加载')

        // 获取系统信息 - 使用新的 API
        const windowInfo = wx.getWindowInfo()
        statusBarHeight.value = windowInfo.statusBarHeight || 44

        // 模拟加载数据
        setTimeout(() => {
          loading.value = false
        }, 1000)

      } catch (error) {
        console.error('❌ 页面加载失败:', error)
        wx.showToast({
          title: '加载失败',
          icon: 'error'
        })
      }
    })

    /**
     * 页面显示
     */
    onShow(async () => {
      try {
        // 设置 tabBar 选中状态
        if (typeof getApp().globalData.tabBar !== 'undefined') {
          getApp().globalData.tabBar.setSelected(2)
        }

        console.log('🛡️ 安全分析页面显示')
      } catch (error) {
        console.error('❌ 页面显示更新失败:', error)
      }
    })

    /**
     * 刷新数据
     */
    const onRefresh = async () => {
      try {
        refreshing.value = true

        // 模拟刷新
        await new Promise(resolve => setTimeout(resolve, 1500))

        wx.showToast({
          title: '刷新成功',
          icon: 'success',
          duration: 1500
        })

      } catch (error) {
        console.error('❌ 刷新失败:', error)
        wx.showToast({
          title: '刷新失败',
          icon: 'error'
        })
      } finally {
        refreshing.value = false
      }
    }

    /**
     * 查看弱密码
     */
    const onWeakPasswordsTap = () => {
      if (weakPasswords.value.length === 0) {
        wx.showToast({
          title: '没有弱密码',
          icon: 'none'
        })
        return
      }

      console.log('🔍 查看弱密码列表')
      wx.showModal({
        title: '弱密码列表',
        content: `发现 ${weakPasswords.value.length} 个弱密码，建议立即更换以保证安全。`,
        confirmText: '立即修复',
        cancelText: '稍后处理',
        success: (res) => {
          if (res.confirm) {
            // 跳转到第一个弱密码的编辑页面
            const firstWeak = weakPasswords.value[0]
            if (firstWeak) {
              wx.navigateTo({
                url: `/pages/password-detail/password-detail?id=${firstWeak.id}`
              })
            }
          }
        }
      })
    }

    /**
     * 查看重复密码
     */
    const onDuplicatePasswordsTap = () => {
      if (duplicatePasswords.value.length === 0) {
        wx.showToast({
          title: '没有重复密码',
          icon: 'none'
        })
        return
      }

      console.log('🔍 查看重复密码列表')
      wx.showModal({
        title: '重复密码列表',
        content: `发现 ${duplicatePasswords.value.length} 个重复使用的密码，建议为每个账号设置独特的密码。`,
        confirmText: '立即修复',
        cancelText: '稍后处理',
        success: (res) => {
          if (res.confirm) {
            const firstDuplicate = duplicatePasswords.value[0]
            if (firstDuplicate) {
              wx.navigateTo({
                url: `/pages/password-detail/password-detail?id=${firstDuplicate.id}`
              })
            }
          }
        }
      })
    }

    /**
     * 查看强密码
     */
    const onStrongPasswordsTap = () => {
      if (strongPasswords.value.length === 0) {
        wx.showToast({
          title: '没有强密码',
          icon: 'none'
        })
        return
      }

      wx.showToast({
        title: `您有 ${strongPasswords.value.length} 个强密码！`,
        icon: 'success'
      })
    }

    /**
     * 点击风险密码
     */
    const onRiskPasswordTap = (event) => {
      const password = event.currentTarget.dataset.password
      console.log('🔍 查看风险密码详情:', password.title)

      wx.navigateTo({
        url: `/pages/password-detail/password-detail?id=${password.id}`
      })
    }

    /**
     * 修复密码
     */
    const onFixPasswordTap = (event) => {
      event.stopPropagation() // 阻止事件冒泡

      const password = event.currentTarget.dataset.password
      console.log('🔧 修复密码:', password.title)

      wx.navigateTo({
        url: `/pages/add-password/add-password?id=${password.id}&mode=edit`
      })
    }

    /**
     * 两步验证提示
     */
    const onTwoFactorTip = () => {
      wx.showModal({
        title: '启用两步验证',
        content: '两步验证可以大大提高账号安全性，建议为所有重要账号启用此功能。',
        confirmText: '了解更多',
        cancelText: '知道了',
        success: (res) => {
          if (res.confirm) {
            wx.showToast({
              title: '功能开发中',
              icon: 'none'
            })
          }
        }
      })
    }

    /**
     * 密码轮换提示
     */
    const onPasswordRotationTip = () => {
      wx.showModal({
        title: '定期更换密码',
        content: '定期更换密码可以降低密码泄露风险，建议每90天更换一次重要账号的密码。',
        confirmText: '设置提醒',
        cancelText: '知道了',
        success: (res) => {
          if (res.confirm) {
            wx.showToast({
              title: '功能开发中',
              icon: 'none'
            })
          }
        }
      })
    }

    /**
     * 密码生成器提示
     */
    const onPasswordGeneratorTip = () => {
      wx.switchTab({
        url: '/pages/password-generator/password-generator'
      })
    }

    /**
     * 添加密码
     */
    const onAddPasswordTap = () => {
      wx.navigateTo({
        url: '/pages/add-password/add-password'
      })
    }

    return {
      // 响应式数据
      statusBarHeight,
      refreshing,
      loading,
      securityScore,
      passwordAnalysis,
      scoreLevel,
      scoreLevelText,
      scorePercentage,
      weakPasswords,
      duplicatePasswords,
      strongPasswords,
      riskPasswords,
      isEmpty,

      // 方法
      onRefresh,
      onWeakPasswordsTap,
      onDuplicatePasswordsTap,
      onStrongPasswordsTap,
      onRiskPasswordTap,
      onFixPasswordTap,
      onTwoFactorTip,
      onPasswordRotationTip,
      onPasswordGeneratorTip,
      onAddPasswordTap
    }
  }
})