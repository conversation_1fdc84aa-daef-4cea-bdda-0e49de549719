/**
 * VaultKeeper 主页/密码列表页面
 * 严格按照原型设计实现
 */

import { definePage, ref, computed, onShow } from '@vue-mini/core'
import { usePasswordStore } from '../../store/password'
import faBell from '@fortawesome/free-regular-svg-icons/faBell'
import faHeart from '@fortawesome/free-regular-svg-icons/faHeart'
import faEye from '@fortawesome/free-regular-svg-icons/faEye'
import faBarChart from '@fortawesome/free-regular-svg-icons/faBarChart'

definePage({
  /**
   * 页面设置
   */
  setup() {
    // 初始化密码 store
    const passwordStore = usePasswordStore()

    // 系统信息
    const statusBarHeight = ref(44) // 默认状态栏高度

    // 搜索
    const searchQuery = ref('')

    // 分类 - 与原型保持一致
    const categories = ref([
      { id: 'all', name: '所有' },
      { id: 'website', name: '网站' },
      { id: 'app', name: '应用' },
      { id: 'bank', name: '银行卡' },
      { id: 'identity', name: '身份证' },
      { id: 'note', name: '安全笔记' },
      { id: 'other', name: '其他' }
    ])
    const selectedCategory = ref('all')

    // 密码数据现在从 store 中获取
    // 计算属性：根据分类和搜索筛选密码
    const filteredPasswords = computed(() => {
      try {
        let filtered = passwordStore.passwords || []

        if (!Array.isArray(filtered)) {
          return []
        }

        // 根据搜索查询筛选
        if (searchQuery.value) {
          const query = searchQuery.value.toLowerCase()
          filtered = filtered.filter(password =>
            password.title.toLowerCase().includes(query) ||
            password.username.toLowerCase().includes(query)
          )
        }

        // 根据分类筛选
        if (selectedCategory.value !== 'all') {
          // 映射分类ID到store中的分类
          const categoryMap = {
            'website': 'work',
            'app': 'entertainment',
            'bank': 'finance',
            'identity': 'other',
            'note': 'other',
            'other': 'other'
          }
          const storeCategory = categoryMap[selectedCategory.value] || selectedCategory.value
          filtered = filtered.filter(password => password.category === storeCategory)
        }

        return filtered
      } catch (error) {
        console.error('❌ 筛选密码失败:', error)
        return []
      }
    })

    // 计算属性：常用密码（使用store中的收藏功能）
    const favoritePasswords = computed(() => {
      try {
        const favorites = passwordStore.favoritePasswords
        if (!favorites || !Array.isArray(favorites)) {
          return []
        }
        return favorites
      } catch (error) {
        console.error('❌ 获取收藏密码失败:', error)
        return []
      }
    })

    // 计算属性：最近添加密码（使用store中的最近使用功能）
    const recentPasswords = computed(() => {
      try {
        const recent = passwordStore.recentPasswords
        if (!recent || !Array.isArray(recent)) {
          return []
        }
        return recent.filter(password => !password.isFavorite).slice(0, 3)
      } catch (error) {
        console.error('❌ 获取最近密码失败:', error)
        return []
      }
    })

    // 计算属性：其他密码
    const otherPasswords = computed(() => {
      try {
        const recent = passwordStore.recentPasswords
        const recentArray = Array.isArray(recent) ? recent : []
        return filteredPasswords.value.filter(password =>
          !password.isFavorite &&
          !recentArray.some(recentPassword => recentPassword.id === password.id)
        ).slice(0, 5)
      } catch (error) {
        console.error('❌ 获取其他密码失败:', error)
        return []
      }
    })

    // 加载状态
    const refreshing = ref(false)

    /**
     * 页面显示时更新数据
     */
    onShow(async () => {
      try {
        // 获取系统信息 - 使用新的 API
        const windowInfo = wx.getWindowInfo()
        statusBarHeight.value = windowInfo.statusBarHeight || 44

        // 每次显示页面时都重新初始化密码 store，确保显示最新数据
        // 这样可以确保从导入页面返回时能看到新导入的密码
        try {
          await passwordStore.initialize()
        } catch (initError) {
          console.error('❌ 密码存储初始化失败:', initError)
          // 如果初始化失败，可能是存储系统未初始化，尝试重新初始化
          console.log('🔄 尝试重新初始化存储系统...')
          // 这里可以添加重新初始化的逻辑
        }

        // 设置 tabBar 选中状态
        if (typeof getApp().globalData.tabBar !== 'undefined') {
          getApp().globalData.tabBar.setSelected(0)
        }

        console.log('🏠 页面显示，密码数量:', passwordStore.passwords.length)
      } catch (error) {
        console.error('❌ 页面显示更新失败:', error)
      }
    })

    /**
     * 处理搜索输入
     */
    const onSearchInput = (event: any) => {
      const query = event.detail.value
      searchQuery.value = query
      console.log('🔍 搜索输入:', query)
    }

    /**
     * 处理搜索确认
     */
    const onSearchConfirm = (event: any) => {
      const query = event.detail.value
      console.log('🔍 搜索确认:', query)
    }

    /**
     * 处理分类点击
     */
    const onCategoryTap = (event: any) => {
      const category = event.currentTarget.dataset.category
      console.log('🏷️ 选择分类:', category)
      selectedCategory.value = category
    }

    /**
     * 处理密码点击 - 跳转到密码详情页面
     */
    const onPasswordTap = (event: any) => {
      const password = event.currentTarget.dataset.password
      console.log('🔑 查看密码详情:', password.title)

      if (!password || !password.id) {
        wx.showToast({
          title: '密码数据错误',
          icon: 'error'
        })
        return
      }

      // 跳转到密码详情页面，传递密码ID
      wx.navigateTo({
        url: `/pages/password-detail/password-detail?id=${password.id}`
      })
    }

    /**
     * 处理密码复制
     */
    const onPasswordCopy = async (event: any) => {
      const password = event.currentTarget.dataset.password
      console.log('📋 复制密码:', password.title)

      try {
        // 复制到剪贴板
        await wx.setClipboardData({
          data: password.password
        })

        wx.showToast({
          title: '密码已复制',
          icon: 'success',
          duration: 1500
        })

      } catch (error) {
        console.error('❌ 复制失败:', error)
        wx.showToast({
          title: '复制失败',
          icon: 'error'
        })
      }
    }

    /**
     * 处理下拉刷新
     */
    const onRefresh = async () => {
      console.log('🔄 下拉刷新')
      refreshing.value = true

      try {
        // 重新初始化密码数据
        await passwordStore.initialize()
        console.log('✅ 数据刷新完成，密码数量:', passwordStore.passwords.length)

        wx.showToast({
          title: '刷新完成',
          icon: 'success',
          duration: 1000
        })
      } catch (error) {
        console.error('❌ 数据刷新失败:', error)
        wx.showToast({
          title: '刷新失败',
          icon: 'error',
          duration: 1000
        })
      } finally {
        refreshing.value = false
      }
    }

    /**
     * 处理加载更多
     */
    const onLoadMore = () => {
      console.log('📄 加载更多')
      // 这里可以实现分页加载逻辑
    }

    /**
     * 处理添加密码点击
     */
    const onAddPasswordTap = () => {
      console.log('➕ 添加密码')
      wx.navigateTo({
        url: '/pages/add-password/add-password',
        success: () => {
          console.log('✅ 跳转到添加密码页面成功')
        },
        fail: (error) => {
          console.error('❌ 跳转到添加密码页面失败:', error)
          wx.showToast({
            title: '页面跳转失败',
            icon: 'error'
          })
        }
      })
    }

    /**
     * 检查密码是否已过期
     */
    const isPasswordExpired = (expiresAt: string): boolean => {
      if (!expiresAt) return false
      return new Date(expiresAt).getTime() < Date.now()
    }

    /**
     * 检查密码是否即将过期（30天内）
     */
    const isPasswordExpiringSoon = (expiresAt: string): boolean => {
      if (!expiresAt) return false
      const expiryDate = new Date(expiresAt)
      const now = new Date()
      const diffMs = expiryDate.getTime() - now.getTime()
      const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
      return diffDays >= 0 && diffDays <= 30
    }

    // 返回响应式数据和方法
    return {
      // FontAwesome 图标测试
      heartIcon: faHeart,
      eyeIcon: faEye,
      barChartIcon: faBarChart,
      bellIcon: faBell,

      // 响应式数据
      statusBarHeight,
      searchQuery,
      categories,
      selectedCategory,
      favoritePasswords,
      recentPasswords,
      otherPasswords,
      refreshing,

      // 方法
      onSearchInput,
      onSearchConfirm,
      onCategoryTap,
      onPasswordTap,
      onPasswordCopy,
      onRefresh,
      onLoadMore,
      onAddPasswordTap,
      isPasswordExpired,
      isPasswordExpiringSoon
    }
  }
})
