<!-- VaultKeeper 主页/密码列表页面
  使用 Tailwind CSS 重构，严格按照原型设计实现 -->
<view class="min-h-screen bg-gradient-to-br from-dark-primary to-dark-secondary">
  <!-- 固定搜索栏 - 移出 scroll-view，固定在顶部 -->
  <view class="search-fixed-container {{isScrolled ? 'search-fixed-container--scrolled' : ''}} px-5 pb-3" style="padding-top: {{statusBarHeight + 10}}px;">
    <view class="bg-dark-input/70 border border-white/5 rounded-xl p-3 flex items-center search-bar">
      <text class="fas fa-search text-gray-400 mr-3"></text>
      <input type="text" placeholder="搜索密码..." value="{{searchQuery}}" bind:input="onSearchInput" bind:confirm="onSearchConfirm" class="flex-1 bg-transparent text-white outline-none" />
    </view>
  </view>
  <!-- 主要内容 - 与原型保持一致，调整上内边距以适应固定搜索栏 -->
  <scroll-view class="h-screen pb-40" scroll-y="{{true}}" refresher-enabled="{{true}}" refresher-triggered="{{refreshing}}" bind:refresherrefresh="onRefresh" bind:scrolltolower="onLoadMore" scroll-with-animation="{{true}}" style="padding-top: {{statusBarHeight + 80}}px;">
    <!-- 分类筛选 - 与原型保持一致 -->
    <view class="scrolling-wrapper mb-4">
      <view wx:for="{{categories}}" wx:key="id" class="category-pill {{selectedCategory === item.id ? 'active' : ''}}" bind:tap="onCategoryTap" data-category="{{item.id}}">
        {{item.name}}
      </view>
    </view>
    <!-- 常用密码 - 与原型保持一致 -->
    <view wx:if="{{favoritePasswords.length > 0 && !searchQuery}}" class="mb-6">
      <view class="flex justify-between items-center px-5 mb-3">
        <text class="text-lg font-semibold text-white">常用密码</text>
        <text class="text-sm text-gray-400">查看全部</text>
      </view>
      <glass-card wx:for="{{favoritePasswords}}" wx:key="id" clickable="{{true}}" custom-class="mx-5 mb-3" padding="16rpx" bind:tap="onPasswordTap" data-password="{{item}}">
        <view class="flex items-center">
          <view class="w-10 h-10 rounded-xl flex items-center justify-center mr-3.5 text-lg" style="background-color: {{item.iconColor || '#3b82f6'}}20; color: {{item.iconColor || '#3b82f6'}};">
            <text class="{{item.iconType || 'fab'}} fa-{{item.icon || 'google'}}"></text>
          </view>
          <view class="flex-1">
            <view class="font-medium text-white">{{item.title}}</view>
            <view class="text-gray-400 text-sm">{{item.username}}</view>
          </view>
          <view class="p-2 rounded-lg bg-white/10 text-gray-300 hover:bg-white/20 transition-colors duration-200" bind:tap="onPasswordCopy" data-password="{{item}}" catch:tap="true">
            <text class="far fa-copy"></text>
          </view>
        </view>
      </glass-card>
    </view>
    <!-- 最近添加 - 与原型保持一致 -->
    <view wx:if="{{recentPasswords.length > 0 && !searchQuery}}" class="mb-6">
      <view class="flex justify-between items-center px-5 mb-3">
        <text class="text-lg font-semibold text-white">最近添加</text>
        <text class="text-sm text-gray-400">查看全部</text>
      </view>
      <glass-card wx:for="{{recentPasswords}}" wx:key="id" clickable="{{true}}" custom-class="mx-5 mb-3" padding="16rpx" bind:tap="onPasswordTap" data-password="{{item}}">
        <view class="flex items-center">
          <view class="w-10 h-10 rounded-xl flex items-center justify-center mr-3.5 text-lg" style="background-color: {{item.iconColor || '#10b981'}}20; color: {{item.iconColor || '#10b981'}};">
            <text class="{{item.iconType || 'fas'}} fa-{{item.icon || 'university'}}"></text>
          </view>
          <view class="flex-1">
            <view class="font-medium text-white">{{item.title}}</view>
            <view class="text-gray-400 text-sm">{{item.username}}</view>
          </view>
          <view class="p-2 rounded-lg bg-white/10 text-gray-300 hover:bg-white/20 transition-colors duration-200" bind:tap="onPasswordCopy" data-password="{{item}}" catch:tap="true">
            <text class="far fa-copy"></text>
          </view>
        </view>
      </glass-card>
    </view>
    <!-- 其他密码条目 - 与原型保持一致 -->
    <glass-card wx:for="{{otherPasswords}}" wx:key="id" clickable="{{true}}" custom-class="mx-5 mb-3" padding="16rpx" bind:tap="onPasswordTap" data-password="{{item}}">
      <view class="flex items-center">
        <view class="w-10 h-10 rounded-xl flex items-center justify-center mr-3.5 text-lg" style="background-color: {{item.iconColor || '#8b5cf6'}}20; color: {{item.iconColor || '#8b5cf6'}};">
          <text class="{{item.iconType || 'fas'}} fa-{{item.icon || 'shopping-cart'}}"></text>
        </view>
        <view class="flex-1">
          <view class="font-medium text-white">{{item.title}}</view>
          <view class="text-gray-400 text-sm">{{item.username}}</view>
        </view>
        <view class="p-2 rounded-lg bg-white/10 text-gray-300 hover:bg-white/20 transition-colors duration-200" bind:tap="onPasswordCopy" data-password="{{item}}" catch:tap="true">
          <text class="far fa-copy"></text>
        </view>
      </view>
    </glass-card>
  </scroll-view>
  <!-- 浮动添加按钮 - 与原型保持一致 -->
  <floating-button icon="plus" bind:tap="onAddPasswordTap" />
</view>