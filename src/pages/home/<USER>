/**
 * VaultKeeper 主页/密码列表样式
 * 严格按照原型设计实现搜索栏固定效果
 */

/* 固定搜索栏容器 */
.search-fixed-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 50;
  transition: box-shadow 0.3s ease;

  /* 渐变背景，底部透明实现平滑过渡 */
  background: linear-gradient(to bottom,
      rgba(26, 28, 37, 1) 0%,
      rgba(26, 28, 37, 0.95) 80%,
      rgba(26, 28, 37, 0) 100%);

  /* 添加模糊效果增强玻璃效果 */
  -webkit-backdrop-filter: blur(12px);
  backdrop-filter: blur(12px);
}

/* 滚动时添加阴影效果 */
.search-fixed-container--scrolled {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.25);
}

/* 搜索栏输入框动画效果 */
.search-bar {
  transition: all 0.3s ease;
}

.search-bar input {
  transition: all 0.2s ease;
}

.search-bar input:focus {
  color: white;
}

/* 在滚动时轻微改变搜索栏的透明度，增强视觉反馈 */
.search-fixed-container--scrolled .search-bar {
  background-color: rgba(40, 44, 58, 0.85);
  border-color: rgba(255, 255, 255, 0.1);
}

/* 分类筛选样式 - 完全按照原型设计 */
.scrolling-wrapper {
  display: flex;
  overflow-x: auto;
  padding: 20rpx 40rpx;
  -webkit-overflow-scrolling: touch;
}

.category-pill {
  display: inline-block;
  padding: 12rpx 24rpx;
  border-radius: 60rpx;
  font-size: 24rpx;
  font-weight: 500;
  background: rgba(255, 255, 255, 0.1);
  margin-right: 16rpx;
  white-space: nowrap;
  color: rgba(255, 255, 255, 0.7);
  transition: all 0.3s ease;
}

.category-pill.active {
  background: var(--accent-color);
  color: white;
}

/* 隐藏滚动条 */
.scrolling-wrapper::-webkit-scrollbar {
  display: none;
}

/* 浮动按钮样式已移至组件中 */