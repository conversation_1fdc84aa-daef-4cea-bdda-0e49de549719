/**
 * Vault<PERSON><PERSON><PERSON> KeePass 导入页面样式
 * 现代化的导入流程界面设计
 */

.import-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #1a1c25 0%, #0f1118 100%);
  padding: 40rpx 32rpx;
}

/* ==================== 页面头部 ==================== */
.import-header {
  text-align: center;
  margin-bottom: 60rpx;
}

.import-icon {
  font-size: 120rpx;
  margin-bottom: 24rpx;
}

.import-title {
  display: block;
  font-size: 48rpx;
  font-weight: 600;
  color: #e0e0e0;
  margin-bottom: 16rpx;
}

.import-subtitle {
  display: block;
  font-size: 28rpx;
  color: #888888;
}

/* ==================== 导入步骤 ==================== */
.import-steps {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 60rpx;
  padding: 0 40rpx;
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.step-number {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  color: #888888;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
  transition: all 0.3s ease;
}

.step-item.active .step-number {
  background: #6d4aff;
  color: white;
}

.step-item.completed .step-number {
  background: #4ade80;
  color: white;
}

.step-text {
  font-size: 24rpx;
  color: #888888;
  transition: all 0.3s ease;
}

.step-item.active .step-text {
  color: #e0e0e0;
}

.step-line {
  width: 80rpx;
  height: 4rpx;
  background: rgba(255, 255, 255, 0.1);
  margin: 0 20rpx;
  margin-top: -46rpx;
  transition: all 0.3s ease;
}

.step-line.completed {
  background: #4ade80;
}

/* ==================== 导入卡片 ==================== */
.import-card {
  margin-bottom: 40rpx;
}

.card-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.card-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #e0e0e0;
  margin-bottom: 12rpx;
}

.card-subtitle {
  display: block;
  font-size: 26rpx;
  color: #888888;
}

.card-actions {
  display: flex;
  gap: 16rpx;
  margin-top: 40rpx;
}

/* ==================== 文件选择 ==================== */
.file-selector {
  margin-bottom: 40rpx;
}

.file-drop-zone {
  border: 2rpx dashed rgba(255, 255, 255, 0.2);
  border-radius: 24rpx;
  padding: 80rpx 40rpx;
  text-align: center;
  transition: all 0.3s ease;
}

.file-drop-zone:active {
  border-color: #6d4aff;
  background: rgba(109, 74, 255, 0.05);
}

.file-icon {
  display: block;
  font-size: 80rpx;
  margin-bottom: 24rpx;
}

.file-text {
  display: block;
  font-size: 32rpx;
  color: #e0e0e0;
  margin-bottom: 12rpx;
}

.file-hint {
  display: block;
  font-size: 24rpx;
  color: #888888;
}

.file-selected {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 24rpx;
  padding: 32rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.file-info {
  flex: 1;
}

.file-name {
  display: block;
  font-size: 28rpx;
  color: #e0e0e0;
  margin-bottom: 8rpx;
}

.file-size {
  display: block;
  font-size: 24rpx;
  color: #888888;
}

.file-change {
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 16rpx;
  padding: 16rpx 24rpx;
  color: #e0e0e0;
  font-size: 24rpx;
}

/* ==================== 密码表单 ==================== */
.password-form {
  margin-bottom: 40rpx;
}

.keyfile-section {
  margin-top: 40rpx;
}

.keyfile-header {
  margin-bottom: 24rpx;
}

.keyfile-title {
  display: block;
  font-size: 28rpx;
  color: #e0e0e0;
  margin-bottom: 8rpx;
}

.keyfile-subtitle {
  display: block;
  font-size: 24rpx;
  color: #888888;
}

.keyfile-selector {
  border: 1rpx dashed rgba(255, 255, 255, 0.2);
  border-radius: 16rpx;
  padding: 32rpx;
  text-align: center;
  transition: all 0.3s ease;
}

.keyfile-selector:active {
  border-color: #6d4aff;
  background: rgba(109, 74, 255, 0.05);
}

.keyfile-icon {
  display: block;
  font-size: 48rpx;
  margin-bottom: 16rpx;
}

.keyfile-text {
  font-size: 26rpx;
  color: #e0e0e0;
}

.keyfile-selected {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16rpx;
  padding: 24rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.keyfile-info {
  flex: 1;
}

.keyfile-name {
  font-size: 26rpx;
  color: #e0e0e0;
}

.keyfile-remove {
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 12rpx;
  padding: 12rpx 20rpx;
  color: #ff6b6b;
  font-size: 22rpx;
}

/* ==================== 导入进度 ==================== */
.import-progress {
  text-align: center;
  padding: 60rpx 40rpx;
}

.progress-icon {
  font-size: 100rpx;
  margin-bottom: 32rpx;
}

.spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.progress-text {
  font-size: 28rpx;
  color: #888888;
}

/* ==================== 导入结果 ==================== */
.import-result {
  text-align: center;
  padding: 40rpx;
}

.result-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
}

.result-icon.success {
  color: #4ade80;
}

.result-icon.error {
  color: #ff6b6b;
}

.result-stats {
  display: flex;
  justify-content: space-around;
  margin: 40rpx 0;
  padding: 32rpx;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20rpx;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 48rpx;
  font-weight: 600;
  color: #6d4aff;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #888888;
}

.result-errors {
  margin-top: 32rpx;
  padding: 24rpx;
  background: rgba(255, 107, 107, 0.1);
  border-radius: 16rpx;
  text-align: left;
}

.errors-title {
  display: block;
  font-size: 26rpx;
  color: #ff6b6b;
  font-weight: 600;
  margin-bottom: 16rpx;
}

.error-item {
  font-size: 24rpx;
  color: #ff9999;
  margin-bottom: 8rpx;
}

/* ==================== 导入选项 ==================== */
.import-options {
  margin-bottom: 40rpx;
}

.options-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 0;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
}

.options-title {
  font-size: 28rpx;
  color: #e0e0e0;
  font-weight: 500;
}

.options-toggle {
  font-size: 24rpx;
  color: #888888;
}

.options-content {
  padding-top: 24rpx;
}

.option-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
}

.option-label {
  font-size: 26rpx;
  color: #e0e0e0;
}

/* ==================== 帮助信息 ==================== */
.import-help {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20rpx;
  padding: 32rpx;
  margin-top: 40rpx;
}

.help-title {
  display: block;
  font-size: 28rpx;
  color: #e0e0e0;
  font-weight: 600;
  margin-bottom: 20rpx;
}

.help-item {
  display: block;
  font-size: 24rpx;
  color: #888888;
  margin-bottom: 12rpx;
  line-height: 1.5;
}
