<!--
  V<PERSON><PERSON><PERSON><PERSON> KeePass 导入页面
  支持导入 .kdbx 格式的 KeePass 数据库文件
-->

<view class="import-page">
  <!-- 页面头部 -->
  <view class="import-header">
    <view class="import-icon">
      <text>📥</text>
    </view>
    <text class="import-title">导入 KeePass 数据库</text>
    <text class="import-subtitle">支持 KeePass 2.x (.kdbx) 格式文件</text>
  </view>

  <!-- 导入步骤 -->
  <view class="import-steps">
    <view class="step-item {{currentStep >= 1 ? 'active' : ''}} {{currentStep > 1 ? 'completed' : ''}}">
      <view class="step-number">1</view>
      <text class="step-text">选择文件</text>
    </view>
    <view class="step-line {{currentStep > 1 ? 'completed' : ''}}"></view>
    <view class="step-item {{currentStep >= 2 ? 'active' : ''}} {{currentStep > 2 ? 'completed' : ''}}">
      <view class="step-number">2</view>
      <text class="step-text">输入密码</text>
    </view>
    <view class="step-line {{currentStep > 2 ? 'completed' : ''}}"></view>
    <view class="step-item {{currentStep >= 3 ? 'active' : ''}} {{currentStep > 3 ? 'completed' : ''}}">
      <view class="step-number">3</view>
      <text class="step-text">导入数据</text>
    </view>
  </view>

  <!-- 步骤1: 选择文件 -->
  <glass-card wx:if="{{currentStep === 1}}" class="import-card">
    <view class="card-header">
      <text class="card-title">选择 KeePass 文件</text>
      <text class="card-subtitle">请选择要导入的 .kdbx 文件</text>
    </view>

    <view class="file-selector">
      <view wx:if="{{!selectedFile}}" class="file-drop-zone" bind:tap="onSelectFile">
        <text class="file-icon iconfont icon-folder"></text>
        <text class="file-text">点击选择文件</text>
        <text class="file-hint">支持 .kdbx 格式</text>
      </view>

      <view wx:else class="file-selected">
        <view class="file-info">
          <text class="file-name">{{selectedFile.name}}</text>
          <text class="file-size">{{selectedFile.sizeText}}</text>
        </view>
        <button class="file-change" bind:tap="onSelectFile">重新选择</button>
      </view>
    </view>

    <view class="card-actions">
      <custom-button
        variant="primary"
        size="lg"
        block="{{true}}"
        text="下一步"
        disabled="{{!selectedFile}}"
        bind:tap="onNextStep"
      />
    </view>
  </glass-card>

  <!-- 步骤2: 输入密码 -->
  <glass-card wx:if="{{currentStep === 2}}" class="import-card">
    <view class="card-header">
      <text class="card-title">输入数据库密码</text>
      <text class="card-subtitle">请输入 KeePass 数据库的主密码</text>
    </view>

    <view class="password-form">
      <form-input
        label="数据库密码"
        type="password"
        value="{{dbPassword}}"
        placeholder="请输入 KeePass 数据库密码"
        required="{{true}}"
        clearable="{{false}}"
        bindinput="onPasswordInput"
        error-text="{{passwordError}}"
        left-icon="lock"
      />

      <view class="keyfile-section">
        <view class="keyfile-header">
          <text class="keyfile-title">密钥文件（可选）</text>
          <text class="keyfile-subtitle">如果数据库使用了密钥文件，请选择</text>
        </view>

        <view wx:if="{{!selectedKeyFile}}" class="keyfile-selector" bind:tap="onSelectKeyFile">
          <text class="keyfile-icon iconfont icon-key"></text>
          <text class="keyfile-text">选择密钥文件</text>
        </view>

        <view wx:else class="keyfile-selected">
          <view class="keyfile-info">
            <text class="keyfile-name">{{selectedKeyFile.name}}</text>
          </view>
          <button class="keyfile-remove" bind:tap="onRemoveKeyFile">移除</button>
        </view>
      </view>
    </view>

    <view class="card-actions">
      <custom-button
        variant="ghost"
        size="lg"
        text="上一步"
        bind:tap="onPrevStep"
        style="margin-right: 16rpx;"
      />
      <custom-button
        variant="primary"
        size="lg"
        text="开始导入"
        disabled="{{!dbPassword}}"
        loading="{{importing}}"
        loading-text="导入中..."
        bind:tap="onStartImport"
        style="flex: 1;"
      />
    </view>
  </glass-card>

  <!-- 步骤3: 导入进度和结果 -->
  <glass-card wx:if="{{currentStep === 3}}" class="import-card">
    <view class="card-header">
      <text class="card-title">{{importing ? '正在导入...' : '导入完成'}}</text>
      <text class="card-subtitle">{{importing ? '请稍候，正在处理您的数据' : importResult.message}}</text>
    </view>

    <!-- 导入进度 -->
    <view wx:if="{{importing}}" class="import-progress">
      <view class="progress-icon">
        <text class="spinner iconfont icon-loader"></text>
      </view>
      <text class="progress-text">正在解析数据库文件...</text>
    </view>

    <!-- 导入结果 -->
    <view wx:else class="import-result">
      <view class="result-icon {{importResult.success ? 'success' : 'error'}}">
        <text class="iconfont {{importResult.success ? 'icon-check-circle' : 'icon-x-circle'}}"></text>
      </view>

      <view wx:if="{{importResult.success}}" class="result-stats">
        <view class="stat-item">
          <text class="stat-number">{{importResult.totalCount}}</text>
          <text class="stat-label">总条目</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{importResult.importedCount}}</text>
          <text class="stat-label">已导入</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{importResult.skippedCount}}</text>
          <text class="stat-label">已跳过</text>
        </view>
      </view>

      <view wx:if="{{importResult.errors.length > 0}}" class="result-errors">
        <text class="errors-title">错误信息：</text>
        <view wx:for="{{importResult.errors}}" wx:key="index" class="error-item">
          <text>• {{item}}</text>
        </view>
      </view>
    </view>

    <view class="card-actions">
      <custom-button
        wx:if="{{!importing && !importResult.success}}"
        variant="ghost"
        size="lg"
        text="重试"
        bind:tap="onRetry"
        style="margin-right: 16rpx;"
      />
      <custom-button
        wx:if="{{!importing}}"
        variant="primary"
        size="lg"
        text="{{importResult.success ? '完成' : '返回'}}"
        bind:tap="onFinish"
        style="flex: 1;"
      />
    </view>
  </glass-card>

  <!-- 导入选项（高级） -->
  <glass-card wx:if="{{currentStep === 2}}" class="import-options">
    <view class="options-header" bind:tap="onToggleOptions">
      <text class="options-title">高级选项</text>
      <text class="options-toggle">{{showOptions ? '🔽' : '▶️'}}</text>
    </view>

    <view wx:if="{{showOptions}}" class="options-content">
      <view class="option-item">
        <text class="option-label">跳过重复项</text>
        <switch checked="{{importOptions.skipDuplicates}}" bind:change="onOptionChange" data-option="skipDuplicates" />
      </view>
      <view class="option-item">
        <text class="option-label">保持文件夹结构</text>
        <switch checked="{{importOptions.preserveStructure}}" bind:change="onOptionChange" data-option="preserveStructure" />
      </view>
      <view class="option-item">
        <text class="option-label">合并分类</text>
        <switch checked="{{importOptions.mergeCategories}}" bind:change="onOptionChange" data-option="mergeCategories" />
      </view>
    </view>
  </glass-card>

  <!-- 帮助信息 -->
  <view class="import-help">
    <text class="help-title">💡 导入说明</text>
    <text class="help-item">• 支持 KeePass 2.x 格式的 .kdbx 文件</text>
    <text class="help-item">• 支持密码和密钥文件双重验证</text>
    <text class="help-item">• 导入的密码将加密存储在本地</text>
    <text class="help-item">• 重复的密码条目将被自动跳过</text>
  </view>
</view>
