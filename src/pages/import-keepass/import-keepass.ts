/**
 * VaultKeeper KeePass 导入页面
 * 支持导入 .kdbx 格式的 KeePass 数据库文件
 */

import { definePage, ref, onLoad, onShow } from '@vue-mini/core'
import { usePasswordStore } from '../../store/password'
import { KeePassImporter, KeePassImportResult, KeePassImportOptions } from '@/utils/keepass'

// 文件信息接口
interface FileInfo {
  name: string
  size: number
  sizeText: string
}

// KDBX DATA IFNO
interface KdbxPass {
  file: ArrayBuffer | string
  keyFile: ArrayBuffer | string
  password: string
}

definePage({
  setup() {
    const passwordStore = usePasswordStore()

    // inject password for test
    const kdbxPass: KdbxPass = {
      file: '',
      keyFile: '',
      password: 'xeeback1794848ut',
    }

    console.log('📥 KeePass 导入页面初始化')

    // 响应式数据
    const currentStep = ref(1) // 当前步骤：1-选择文件，2-输入密码，3-导入结果
    const selectedFile = ref<FileInfo | null>(null)
    const selectedKeyFile = ref<FileInfo | null>(null)
    const dbPassword = ref('')
    const passwordError = ref('')
    const importing = ref(false)
    const showOptions = ref(false)

    // 导入选项
    const importOptions = ref<KeePassImportOptions>({
      skipDuplicates: true,
      mergeCategories: true,
      preserveStructure: false
    })

    // 导入结果
    const importResult = ref<KeePassImportResult>({
      success: false,
      message: '',
      passwords: [],
      totalCount: 0,
      importedCount: 0,
      skippedCount: 0,
      errors: []
    })

    /**
     * 页面加载时初始化
     */
    onLoad(async (options: any) => {
      try {
        console.log('📱 初始化 KeePass 导入页面', options)
        console.log('✅ KeePass 导入页面初始化完成')
      } catch (error) {
        console.error('❌ 页面初始化失败:', error)
        wx.showToast({
          title: '初始化失败',
          icon: 'error'
        })
      }
    })

    /**
     * 页面显示时更新数据
     */
    onShow(async () => {
      try {
        console.log('👁️ KeePass 导入页面显示')
      } catch (error) {
        console.error('❌ 页面显示更新失败:', error)
      }
    })

    /**
     * 选择文件
     */
    const onSelectFile = () => {
      wx.chooseMessageFile({
        count: 1,
        type: 'file',
        extension: ['kdbx'],
        success: (res) => {
          const file = res.tempFiles[0]

          if (!file.name.toLowerCase().endsWith('.kdbx')) {
            wx.showToast({
              title: '请选择 .kdbx 文件',
              icon: 'error'
            })
            return
          }

          // 检查文件大小（限制为 10MB）
          if (file.size > 10 * 1024 * 1024) {
            wx.showToast({
              title: '文件过大，请选择小于 10MB 的文件',
              icon: 'error'
            })
            return
          }

          // 读取文件数据
          const fileSystemManager = wx.getFileSystemManager()
          try {
            const data = fileSystemManager.readFileSync(file.path)

            selectedFile.value = {
              name: file.name,
              size: file.size,
              sizeText: formatFileSize(file.size),
            }

            kdbxPass.file = data

            console.log('📁 文件选择成功:', data instanceof ArrayBuffer, selectedFile.value.name)

          } catch (error) {
            console.error('❌ 文件读取失败:', error)
            wx.showToast({
              title: '文件读取失败',
              icon: 'error'
            })
          }
        },
        fail: (error) => {
          console.error('❌ 文件选择失败:', error)
          if (error.errMsg !== 'chooseMessageFile:fail cancel') {
            wx.showToast({
              title: '文件选择失败',
              icon: 'error'
            })
          }
        }
      })
    }

    /**
     * 选择密钥文件
     */
    const onSelectKeyFile = () => {
      wx.chooseMessageFile({
        count: 1,
        type: 'file',
        success: (res) => {
          const file = res.tempFiles[0]

          // 检查文件大小（密钥文件通常很小）
          if (file.size > 1024 * 1024) {
            wx.showToast({
              title: '密钥文件过大',
              icon: 'error'
            })
            return
          }

          // 读取密钥文件数据
          const fileSystemManager = wx.getFileSystemManager()
          try {
            const data = fileSystemManager.readFileSync(file.path)

            selectedKeyFile.value = {
              name: file.name,
              size: file.size,
              sizeText: formatFileSize(file.size),
            }

            kdbxPass.keyFile = data
            console.log('🔑 密钥文件选择成功:', selectedKeyFile.value.name)

          } catch (error) {
            console.error('❌ 密钥文件读取失败:', error)
            wx.showToast({
              title: '密钥文件读取失败',
              icon: 'error'
            })
          }
        },
        fail: (error) => {
          console.error('❌ 密钥文件选择失败:', error)
          if (error.errMsg !== 'chooseMessageFile:fail cancel') {
            wx.showToast({
              title: '密钥文件选择失败',
              icon: 'error'
            })
          }
        }
      })
    }

    /**
     * 移除密钥文件
     */
    const onRemoveKeyFile = () => {
      selectedKeyFile.value = null
      console.log('🗑️ 密钥文件已移除')
    }

    /**
     * 格式化文件大小
     */
    const formatFileSize = (bytes: number): string => {
      if (bytes === 0) return '0 B'

      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))

      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }

    /**
     * 下一步
     */
    const onNextStep = () => {
      if (currentStep.value < 3) {
        currentStep.value++
        console.log('➡️ 进入步骤:', currentStep.value)
      }
    }

    /**
     * 上一步
     */
    const onPrevStep = () => {
      if (currentStep.value > 1) {
        currentStep.value--
        console.log('⬅️ 返回步骤:', currentStep.value)
      }
    }

    /**
     * 处理密码输入
     */
    const onPasswordInput = (event: any) => {
      const value = event.detail.value
      dbPassword.value = value

      // 清除错误信息
      if (passwordError.value) {
        passwordError.value = ''
      }
    }

    /**
     * 切换高级选项
     */
    const onToggleOptions = () => {
      showOptions.value = !showOptions.value
    }

    /**
     * 处理选项变化
     */
    const onOptionChange = (event: any) => {
      const option = event.currentTarget.dataset.option
      const checked = event.detail.value

      if (importOptions.value && option in importOptions.value) {
        (importOptions.value as any)[option] = checked
        console.log('⚙️ 选项变化:', option, checked)
      }
    }

    /**
     * 开始导入
     */
    const onStartImport = async () => {
      if (!selectedFile.value || !dbPassword.value) {
        wx.showToast({
          title: '请完善导入信息',
          icon: 'error'
        })
        return
      }

      try {
        importing.value = true
        currentStep.value = 3
        passwordError.value = ''

        console.log('🚀 开始导入 KeePass 数据库...')

        // 确保密码存储已经初始化
        console.log('🔄 初始化密码存储...')
        await passwordStore.initialize()

        // 执行导入
        const result = await KeePassImporter.importDatabase(
          // selectedFile.value!,
          // dbPassword.value,
          // selectedKeyFile.value || null,
          kdbxPass.file!,
          kdbxPass.password!,
          kdbxPass.keyFile,
          importOptions.value
        )

        importResult.value = result

        if (result.success && result.passwords.length > 0) {
          // 使用批量添加方法将导入的密码添加到密码存储
          console.log(`📥 开始批量添加 ${result.passwords.length} 个密码到存储...`)

          try {
            const batchResult = await passwordStore.addPasswordsBatch(result.passwords)

            // 更新导入结果
            result.importedCount = batchResult.success
            result.skippedCount += batchResult.failed
            result.errors.push(...batchResult.errors)

            console.log('✅ 密码批量导入成功，共导入', result.importedCount, '个密码')

            // 触觉反馈
            if (wx.vibrateShort) {
              wx.vibrateShort({ type: 'success' })
            }
          } catch (batchError: any) {
            console.error('❌ 批量添加密码失败:', batchError)
            result.success = false
            result.message = `批量保存失败: ${batchError?.message || '未知错误'}`
            result.errors.push(`批量保存失败: ${batchError?.message || '未知错误'}`)
          }
        } else if (!result.success) {
          console.error('❌ 导入失败:', result.message)

          // 如果是密码错误，返回到密码输入步骤
          if (result.message.includes('密码') || result.message.includes('密钥')) {
            passwordError.value = result.message
            currentStep.value = 2
          }
        }

      } catch (error: any) {
        console.error('❌ 导入过程出错:', error)

        const errorMessage = error?.message || '未知错误'

        importResult.value = {
          success: false,
          message: `导入失败: ${errorMessage}`,
          passwords: [],
          totalCount: 0,
          importedCount: 0,
          skippedCount: 0,
          errors: [errorMessage]
        }

        // 如果是认证错误，返回到密码输入步骤
        if (errorMessage.includes('密码') || errorMessage.includes('认证')) {
          passwordError.value = errorMessage
          currentStep.value = 2
        }

      } finally {
        importing.value = false
      }
    }

    /**
     * 重试导入
     */
    const onRetry = () => {
      // 重置状态
      importResult.value = {
        success: false,
        message: '',
        passwords: [],
        totalCount: 0,
        importedCount: 0,
        skippedCount: 0,
        errors: []
      }

      // 返回到密码输入步骤
      currentStep.value = 2
      passwordError.value = ''

      console.log('🔄 重试导入')
    }

    /**
     * 完成导入
     */
    const onFinish = async () => {
      if (importResult.value.success) {
        // 导入成功，刷新密码数据并返回主页
        try {
          // 重新初始化密码存储以确保数据同步
          await passwordStore.initialize()

          wx.switchTab({
            url: '/pages/home/<USER>',
            success: () => {
              // 显示详细的导入成功信息
              const message = `🎉 导入完成！\n成功导入 ${importResult.value.importedCount} 个密码${importResult.value.skippedCount > 0 ? `，跳过 ${importResult.value.skippedCount} 个` : ''}`

              wx.showModal({
                title: 'KeePass 导入成功',
                content: message,
                showCancel: false,
                confirmText: '查看密码',
                success: () => {
                  console.log('✅ 用户确认查看导入的密码')
                }
              })
            }
          })
        } catch (error) {
          console.error('❌ 刷新密码数据失败:', error)
          // 即使刷新失败也要跳转，数据已经保存了
          wx.switchTab({
            url: '/pages/home/<USER>',
            success: () => {
              wx.showToast({
                title: `成功导入 ${importResult.value.importedCount} 个密码`,
                icon: 'success',
                duration: 2000
              })
            }
          })
        }
      } else {
        // 导入失败，返回上一页
        wx.navigateBack({
          fail: () => {
            // 如果无法返回，跳转到主页
            wx.switchTab({
              url: '/pages/home/<USER>'
            })
          }
        })
      }
    }

    return {
      // 响应式数据
      currentStep,
      selectedFile,
      selectedKeyFile,
      dbPassword,
      passwordError,
      importing,
      showOptions,
      importOptions,
      importResult,

      // 方法
      onSelectFile,
      onSelectKeyFile,
      onRemoveKeyFile,
      formatFileSize,
      onNextStep,
      onPrevStep,
      onPasswordInput,
      onToggleOptions,
      onOptionChange,
      onStartImport,
      onRetry,
      onFinish
    }
  }
})
