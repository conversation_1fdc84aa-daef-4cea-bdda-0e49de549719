# @vue-mini 迁移示例

展示如何将传统小程序代码转换为 @vue-mini 框架的正确写法。

## 页面文件迁移

### 迁移前（传统写法）

```typescript
import { definePage } from '@vue-mini/core'

definePage({
  data: {
    count: 0,
    loading: false,
    list: []
  },

  setup() {
    const onLoad = () => {
      this.setData({ loading: true })
      this.loadData()
    }

    const loadData = async () => {
      try {
        const result = await api.getData()
        this.setData({ 
          list: result.data,
          loading: false 
        })
      } catch (error) {
        this.setData({ loading: false })
      }
    }

    const onItemTap = (event) => {
      const item = event.currentTarget.dataset.item
      this.setData({ count: this.data.count + 1 })
    }

    return {
      onLoad,
      loadData,
      onItemTap
    }
  }
})
```

### 迁移后（正确写法）

```typescript
import { definePage, ref, onLoad } from '@vue-mini/core'

definePage({
  setup() {
    // 响应式数据
    const count = ref(0)
    const loading = ref(false)
    const list = ref([])

    // 页面加载
    onLoad(() => {
      loading.value = true
      loadData()
    })

    // 加载数据
    const loadData = async () => {
      try {
        const result = await api.getData()
        list.value = result.data
        loading.value = false
      } catch (error) {
        loading.value = false
      }
    }

    // 点击事件
    const onItemTap = (event: any) => {
      const item = event.currentTarget.dataset.item
      count.value = count.value + 1
    }

    // 返回数据和方法
    return {
      count,
      loading,
      list,
      loadData,
      onItemTap
    }
  }
})
```

## 关键变化总结

### 1. 数据定义
- **迁移前**: 使用 `data` 对象
- **迁移后**: 使用 `ref()` 创建响应式数据

### 2. 数据更新
- **迁移前**: 使用 `this.setData()`
- **迁移后**: 直接修改 `ref.value`

### 3. 生命周期
- **迁移前**: 在 setup 中定义函数，然后返回
- **迁移后**: 直接使用 `onLoad()`, `onShow()` 等钩子

### 4. 数据访问
- **迁移前**: 使用 `this.data.propertyName`
- **迁移后**: 直接使用 `propertyName.value`

### 5. 返回值
- **迁移前**: 只返回方法
- **迁移后**: 返回所有需要在模板中使用的数据和方法

## 组件文件迁移

### 迁移前

```typescript
import { defineComponent } from '@vue-mini/core'

defineComponent({
  properties: {
    title: String,
    count: Number
  },

  data: {
    localCount: 0
  },

  setup(props) {
    const onReady = () => {
      this.setData({ localCount: props.count })
    }

    const increment = () => {
      this.setData({ 
        localCount: this.data.localCount + 1 
      })
      this.triggerEvent('change', this.data.localCount)
    }

    return {
      onReady,
      increment
    }
  }
})
```

### 迁移后

```typescript
import { defineComponent, ref, onReady } from '@vue-mini/core'

defineComponent({
  properties: {
    title: String,
    count: {
      type: Number,
      value: 0
    }
  },

  emits: {
    change: (value: number) => true
  },

  setup(props, { emit }) {
    // 响应式数据
    const localCount = ref(0)

    // 组件就绪
    onReady(() => {
      localCount.value = props.count
    })

    // 增加计数
    const increment = () => {
      localCount.value = localCount.value + 1
      emit('change', localCount.value)
    }

    // 返回数据和方法
    return {
      localCount,
      increment
    }
  }
})
```

## 迁移检查清单

### 页面文件
- [ ] 移除 `data` 对象
- [ ] 将所有数据转换为 `ref()`
- [ ] 替换所有 `this.setData()` 为 `ref.value`
- [ ] 使用生命周期钩子 (`onLoad`, `onShow` 等)
- [ ] 在 return 中包含所有数据和方法
- [ ] 更新数据访问方式 (`this.data.x` → `x.value`)

### 组件文件
- [ ] 移除 `data` 对象
- [ ] 将所有数据转换为 `ref()`
- [ ] 定义 `emits` 对象
- [ ] 使用 `emit()` 替换 `triggerEvent()`
- [ ] 使用组件生命周期钩子
- [ ] 在 return 中包含所有数据和方法

### 类型安全
- [ ] 为复杂属性使用 `PropType`
- [ ] 为事件定义正确的类型
- [ ] 为查询参数定义类型转换
- [ ] 添加必要的 TypeScript 类型注解

这个迁移指南确保了代码完全符合 @vue-mini 框架的最佳实践。
