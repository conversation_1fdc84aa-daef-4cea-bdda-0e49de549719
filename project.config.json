{"description": "keeword 项目配置文件", "miniprogramRoot": "dist/", "compileType": "miniprogram", "simulatorType": "wechat", "condition": {}, "srcMiniprogramRoot": "dist/", "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "setting": {"es6": true, "enhance": true, "postcss": false, "minified": false, "nodeModules": false, "autoAudits": false, "uploadWithSourceMap": false, "uglifyFileName": false, "useIsolateContext": false, "userConfirmedUseIsolateContext": false, "userConfirmedBundleSwitch": false, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "packNpmManually": true, "packNpmRelationList": [{"packageJsonPath": "./package.json", "miniprogramNpmDistDir": "./dist"}], "minifyWXSS": true, "disableUseStrict": false, "minifyWXML": true, "showES6CompileOption": false, "useCompilerPlugins": false, "ignoreUploadUnusedFiles": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}}, "simulatorPluginLibVersion": {}, "packOptions": {"ignore": [], "include": []}, "appid": "wxea5e2a0bae139336"}