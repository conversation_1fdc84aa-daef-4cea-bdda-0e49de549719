# VaultKeeper 密码管理器

基于 @52css/mp-vue3 框架开发的微信小程序密码管理器。

## 项目特性

- 🔐 **安全密码管理** - 本地加密存储，保护您的密码安全
- 🎨 **玻璃拟态设计** - 现代化的 UI 设计，完美还原原型
- 🔧 **密码生成器** - 生成强密码，支持多种配置选项
- 📊 **安全分析** - 智能分析密码强度和安全风险
- ⚙️ **完整设置** - 丰富的配置选项和个性化设置
- 🔄 **状态管理** - 基于 Pinia 的响应式状态管理
- 📱 **响应式设计** - 适配不同屏幕尺寸

## 技术栈

- **框架**: @52css/mp-vue3 (基于 Vue 3 的小程序框架)
- **语言**: TypeScript
- **状态管理**: Pinia
- **样式**: WXSS + CSS 变量
- **构建工具**: 微信开发者工具

## 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 构建 npm 包

在微信开发者工具中：
1. 点击 `工具` -> `构建 npm`
2. 等待构建完成

### 3. 运行项目

在微信开发者工具中打开项目目录，即可预览和调试。

## 项目结构

```
├── app.ts                 # 应用入口文件
├── app.json              # 应用配置
├── app.wxss              # 全局样式
├── pages/                # 页面目录
│   ├── login/           # 登录页面
│   ├── home/            # 主页/密码列表
│   ├── password-detail/ # 密码详情
│   ├── add-password/    # 添加/编辑密码
│   ├── password-generator/ # 密码生成器
│   ├── security-analysis/  # 安全分析
│   └── settings/        # 设置页面
├── components/          # 组件目录
│   ├── common/         # 通用组件
│   └── business/       # 业务组件
├── store/              # 状态管理
├── utils/              # 工具函数
└── styles/             # 样式文件
```

## 核心功能

### 密码管理
- 添加、编辑、删除密码
- 密码搜索和分类
- 收藏和标签管理
- 密码强度检测

### 安全功能
- 本地加密存储
- 主密码保护
- 自动锁定
- 安全分析报告

### 用户体验
- 玻璃拟态设计
- 流畅动画效果
- 触觉反馈
- 无障碍支持

## 开发说明

### 框架特性

本项目使用 @52css/mp-vue3 框架，具有以下特点：

- **Vue 3 组合式 API** - 使用现代化的 Vue 3 语法
- **TypeScript 支持** - 完整的类型安全
- **Pinia 状态管理** - 响应式状态管理
- **小程序原生兼容** - 完美兼容微信小程序

### 开发规范

- 使用 TypeScript 进行开发
- 遵循 Vue 3 组合式 API 规范
- 组件使用 defineComponent 定义
- 页面使用 definePage 定义
- 状态使用 defineStore 定义

## 部署

1. 在微信开发者工具中构建项目
2. 上传代码到微信小程序后台
3. 提交审核并发布

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！
