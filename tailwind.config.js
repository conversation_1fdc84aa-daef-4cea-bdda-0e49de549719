/** @type {import('tailwindcss').Config} */
export default {
  content: [
    './src/**/*.{wxml,ts,js,scss,css}',
    './src/**/*.vue',
    // 支持 weapp-tailwindcss 的特殊格式
    './src/**/*.wxs',
  ],
  theme: {
    extend: {
      colors: {
        // 基于原型设计的主题色彩
        primary: {
          50: '#f0edff',
          100: '#e4deff',
          200: '#ccc0ff',
          300: '#a894ff',
          400: '#8c66ff',
          500: '#6d4aff', // --accent-color
          600: '#5a3ad6',
          700: '#4a2eb3',
          800: '#3d2591',
          900: '#342176',
        },
        glass: {
          bg: 'rgba(23, 25, 35, 0.85)',
          border: 'rgba(255, 255, 255, 0.08)',
        },
        dark: {
          primary: '#1a1c25',
          secondary: '#0f1118',
          card: 'rgba(30, 33, 45, 0.95)',
          input: 'rgba(40, 44, 58, 0.7)',
        },
        text: {
          primary: '#e0e0e0',
          secondary: '#aaaaaa',
          muted: '#888888',
        },
        status: {
          success: '#2ed573',
          warning: '#ffa502',
          error: '#ff4757',
          info: '#3742fa',
        }
      },
      spacing: {
        // 基于 rpx 的间距系统，适配小程序
        'xs': '8rpx',
        'sm': '16rpx',
        'md': '24rpx',
        'lg': '32rpx',
        'xl': '48rpx',
        '2xl': '64rpx',
        '3xl': '96rpx',
        '4xl': '128rpx',
        // 添加更多常用尺寸
        '15': '60rpx',  // 30px * 2
        '20': '80rpx',  // 40px * 2
        '24': '96rpx',  // 48px * 2
        '32': '128rpx', // 64px * 2
      },
      borderRadius: {
        'sm': '12rpx',
        'md': '16rpx',
        'lg': '24rpx',
        'xl': '32rpx',
        '2xl': '40rpx',
      },
      fontSize: {
        'xs': '20rpx',
        'sm': '24rpx',
        'base': '28rpx',
        'lg': '32rpx',
        'xl': '36rpx',
        '2xl': '48rpx',
        '3xl': '60rpx',
        '4xl': '72rpx',
        '5xl': '96rpx',
      },
      boxShadow: {
        'glass': '0 16rpx 64rpx rgba(0, 0, 0, 0.3)',
        'glass-sm': '0 8rpx 32rpx rgba(0, 0, 0, 0.15)',
        'glass-lg': '0 24rpx 80rpx rgba(0, 0, 0, 0.4)',
        'primary': '0 16rpx 32rpx rgba(109, 74, 255, 0.3)',
      },
      backdropBlur: {
        'xs': '4px',
        'sm': '8px',
        'md': '12px',
        'lg': '16px',
        'xl': '20px',
      },
      animation: {
        'spin-slow': 'spin 2s linear infinite',
        'pulse-slow': 'pulse 1.5s ease-in-out infinite',
        'scale-in': 'scaleIn 0.2s ease-out',
        'fade-in': 'fadeIn 0.3s ease-out',
      },
      keyframes: {
        scaleIn: {
          '0%': { transform: 'scale(0.95)', opacity: '0' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
      },
      transitionDuration: {
        'fast': '150ms',
        'normal': '200ms',
        'slow': '300ms',
      },
    },
  },
  plugins: [],
  // 小程序特定配置
  corePlugins: {
    // 禁用一些在小程序中不支持的功能
    preflight: false,
  },
}
