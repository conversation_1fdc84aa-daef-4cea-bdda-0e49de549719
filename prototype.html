<!DOCTYPE html><html lang="zh"><head><meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: https://cdnjs.cloudflare.com https://cdn.jsdelivr.net https://code.jquery.com https://unpkg.com https://d3js.org https://threejs.org https://cdn.plot.ly https://stackpath.bootstrapcdn.com https://maps.googleapis.com https://cdn.tailwindcss.com https://ajax.googleapis.com https://kit.fontawesome.com https://cdn.datatables.net https://maxcdn.bootstrapcdn.com https://code.highcharts.com https://tako-static-assets-production.s3.amazonaws.com https://www.youtube.com https://fonts.googleapis.com https://fonts.gstatic.com https://pfst.cf2.poecdn.net https://puc.poecdn.net https://i.imgur.com https://wikimedia.org https://*.icons8.com https://*.giphy.com https://picsum.photos https://images.unsplash.com; frame-src 'self' https://www.youtube.com https://trytako.com; child-src 'self'; manifest-src 'self'; worker-src 'self'; upgrade-insecure-requests; block-all-mixed-content;">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VaultKeeper - 密码管理器</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        :root {
            --glass-bg: rgba(23, 25, 35, 0.85);
            --glass-border: rgba(255, 255, 255, 0.08);
            --accent-color: #6d4aff;
            --secondary-accent: #8c66ff;
        }
        
        body {
            background: linear-gradient(135deg, #1a1c25 0%, #0f1118 100%);
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            color: #e0e0e0;
            min-height: 100vh;
        }
        
        .device-container {
            width: 390px;
            height: 844px;
            overflow: hidden;
            position: relative;
            border-radius: 40px;
            border: 10px solid #111;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
            background-color: #000;
        }
        
        .glass-card {
            background: var(--glass-bg);
            backdrop-filter: blur(12px);
            border: 1px solid var(--glass-border);
            border-radius: 16px;
        }
        
        .status-bar {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 44px;
            padding: 0 22px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            z-index: 50;
        }
        
        .tab-bar {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 84px;
            background: rgba(20, 22, 32, 0.8);
            backdrop-filter: blur(20px);
            border-top: 1px solid rgba(255, 255, 255, 0.05);
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding-bottom: 20px;
            z-index: 50;
        }
        
        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            font-size: 10px;
            color: #888;
            transition: all 0.2s ease;
        }
        
        .tab-item.active {
            color: var(--accent-color);
        }
        
        .tab-item i {
            font-size: 22px;
            margin-bottom: 4px;
        }
        
        .screen-container {
            position: absolute;
            top: 44px;
            left: 0;
            right: 0;
            bottom: 84px;
            overflow-y: auto;
            overflow-x: hidden;
            display: flex;
            flex-direction: column;
        }
        
        .app-frames {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            justify-content: center;
            padding: 20px;
        }
        
        .app-frame {
            width: 390px;
            height: 844px;
            margin-bottom: 50px;
            position: relative;
            border-radius: 40px;
            overflow: hidden;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
            border: 10px solid #111;
            background-color: #000;
        }
        
        .app-frame-title {
            position: absolute;
            bottom: -40px;
            left: 0;
            right: 0;
            text-align: center;
            font-weight: 600;
            color: #fff;
        }
        
        .app-container {
            position: relative;
            height: 100%;
            width: 100%;
            background: linear-gradient(135deg, #1a1c25 0%, #0f1118 100%);
            overflow: hidden;
        }
        
        .floating-button {
            position: absolute;
            bottom: 30px;
            right: 20px;
            width: 60px;
            height: 60px;
            border-radius: 30px;
            background: var(--accent-color);
            display: flex;
            justify-content: center;
            align-items: center;
            box-shadow: 0 8px 16px rgba(109, 74, 255, 0.3);
            z-index: 40;
        }
        
        .search-bar {
            background: rgba(40, 44, 58, 0.7);
            border-radius: 12px;
            padding: 10px 16px;
            margin: 10px 20px;
            display: flex;
            align-items: center;
            border: 1px solid rgba(255, 255, 255, 0.05);
        }
        
        .search-bar input {
            background: transparent;
            border: none;
            color: #e0e0e0;
            outline: none;
            width: 100%;
            margin-left: 10px;
        }
        
        .password-item {
            padding: 16px;
            margin: 10px 20px;
            display: flex;
            align-items: center;
            transition: all 0.2s ease;
        }
        
        .password-item:active {
            transform: scale(0.98);
        }
        
        .avatar {
            width: 42px;
            height: 42px;
            border-radius: 12px;
            background-color: rgba(255, 255, 255, 0.1);
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 14px;
            font-size: 18px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            color: #aaa;
        }
        
        .form-input {
            width: 100%;
            padding: 14px 16px;
            background: rgba(40, 44, 58, 0.7);
            border: 1px solid rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            color: #e0e0e0;
            font-size: 16px;
            outline: none;
            transition: border-color 0.2s ease;
        }
        
        .form-input:focus {
            border-color: var(--accent-color);
        }
        
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 14px 24px;
            border-radius: 12px;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.2s ease;
            cursor: pointer;
        }
        
        .btn-primary {
            background: var(--accent-color);
            color: white;
        }
        
        .btn-primary:active {
            background: var(--secondary-accent);
            transform: scale(0.98);
        }
        
        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: #e0e0e0;
        }
        
        .btn-secondary:active {
            background: rgba(255, 255, 255, 0.15);
            transform: scale(0.98);
        }
        
        .category-pill {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 30px;
            font-size: 12px;
            font-weight: 500;
            background: rgba(255, 255, 255, 0.1);
            margin-right: 8px;
            white-space: nowrap;
        }
        
        .category-pill.active {
            background: var(--accent-color);
            color: white;
        }
        
        .strength-meter {
            height: 4px;
            border-radius: 2px;
            background: rgba(255, 255, 255, 0.1);
            margin-top: 8px;
            overflow: hidden;
        }
        
        .strength-meter-fill {
            height: 100%;
            border-radius: 2px;
            transition: width 0.3s ease;
        }
        
        .strength-weak {
            background: #ff4757;
            width: 30%;
        }
        
        .strength-medium {
            background: #ffa502;
            width: 60%;
        }
        
        .strength-strong {
            background: #2ed573;
            width: 100%;
        }
        
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 28px;
        }
        
        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 34px;
            transition: .4s;
        }
        
        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 20px;
            width: 20px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            border-radius: 50%;
            transition: .4s;
        }
        
        input:checked + .toggle-slider {
            background-color: var(--accent-color);
        }
        
        input:checked + .toggle-slider:before {
            transform: translateX(22px);
        }
        
        .modal {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 100;
        }
        
        .modal-content {
            width: 90%;
            max-width: 340px;
            padding: 24px;
            border-radius: 20px;
            background: rgba(30, 33, 45, 0.95);
            backdrop-filter: blur(12px);
            border: 1px solid rgba(255, 255, 255, 0.08);
        }
        
        /* Hide scrollbars */
        ::-webkit-scrollbar {
            display: none;
        }
        
        .scrolling-wrapper {
            display: flex;
            overflow-x: auto;
            padding: 10px 20px;
            -webkit-overflow-scrolling: touch;
        }
        
        .copy-btn {
            padding: 8px;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            color: #e0e0e0;
            margin-left: auto;
        }
        
        .security-score {
            width: 120px;
            height: 120px;
            border-radius: 60px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 32px;
            font-weight: bold;
            position: relative;
            margin: 0 auto;
            color: white;
        }
        
        .security-score::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 60px;
            border: 8px solid rgba(255, 255, 255, 0.1);
        }
        
        .security-score::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 60px;
            border: 8px solid var(--accent-color);
            border-right-color: transparent;
            transform: rotate(45deg);
        }
        
        .notification-badge {
            background-color: #ff4757;
            color: white;
            border-radius: 10px;
            padding: 2px 6px;
            font-size: 10px;
            position: absolute;
            top: -2px;
            right: -2px;
        }
    </style>
</head>
<body>
    <div class="flex justify-center p-6">
        <div class="app-frames">
            <!-- Login Screen -->
            <div class="app-frame">
                <div class="app-container">
                    <div class="status-bar">
                        <div>9:41</div>
                        <div>
                            <i class="fas fa-signal"></i>
                            <i class="fas fa-wifi ml-1"></i>
                            <i class="fas fa-battery-full ml-1"></i>
                        </div>
                    </div>
                    
                    <div class="screen-container flex justify-center items-center p-6">
                        <div class="text-center mb-12">
                            <div class="mb-4">
                                <div class="w-24 h-24 rounded-xl bg-opacity-10 bg-white mx-auto flex items-center justify-center">
                                    <i class="fas fa-shield-alt text-5xl text-purple-500"></i>
                                </div>
                            </div>
                            <h1 class="text-2xl font-bold mb-2">VaultKeeper</h1>
                            <p class="text-gray-400 text-sm">安全管理您的所有密码</p>
                        </div>
                        
                        <div class="glass-card w-full p-6 mb-6">
                            <div class="form-group">
                                <label class="form-label">主密码</label>
                                <input type="password" class="form-input" placeholder="输入您的主密码">
                            </div>
                            
                            <div class="mb-6">
                                <button class="btn btn-primary w-full">
                                    解锁
                                </button>
                            </div>
                            
                            <div class="flex justify-center">
                                <button class="text-sm text-gray-400 hover:text-white">
                                    使用生物识别
                                    <i class="fas fa-fingerprint ml-2"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="app-frame-title">登录界面</div>
            </div>

            <!-- Home/Password List Screen -->
            <div class="app-frame">
                <div class="app-container">
                    <div class="status-bar">
                        <div>9:41</div>
                        <div>
                            <i class="fas fa-signal"></i>
                            <i class="fas fa-wifi ml-1"></i>
                            <i class="fas fa-battery-full ml-1"></i>
                        </div>
                    </div>
                    
                    <div class="screen-container">
                        <!-- <div class="p-4 flex justify-between items-center">
                            <div>
                                <h1 class="text-2xl font-bold">密码库</h1>
                                <p class="text-gray-400 text-sm">共28个密码</p>
                            </div>
                            <div class="w-10 h-10 rounded-full overflow-hidden">
                                <div class="w-full h-full bg-gradient-to-br from-purple-500 to-blue-500 flex items-center justify-center">
                                    <span class="text-white font-medium">KL</span>
                                </div>
                            </div>
                        </div> -->
                        
                        <div class="search-bar mb-4">
                            <i class="fas fa-search text-gray-400"></i>
                            <input type="text" placeholder="搜索密码...">
                        </div>
                        
                        <div class="scrolling-wrapper mb-4">
                            <div class="category-pill active">所有</div>
                            <div class="category-pill">网站</div>
                            <div class="category-pill">应用</div>
                            <div class="category-pill">银行卡</div>
                            <div class="category-pill">身份证</div>
                            <div class="category-pill">安全笔记</div>
                            <div class="category-pill">其他</div>
                        </div>
                        
                        <div class="px-4 mb-2 flex justify-between items-center">
                            <h2 class="font-semibold">常用密码</h2>
                            <button class="text-sm text-gray-400">查看全部</button>
                        </div>
                        
                        <div class="password-item glass-card">
                            <div class="avatar bg-blue-600 bg-opacity-20 text-blue-400">
                                <i class="fab fa-google"></i>
                            </div>
                            <div class="flex-1">
                                <div class="font-medium">Google</div>
                                <div class="text-gray-400 text-sm"><EMAIL></div>
                            </div>
                            <div class="copy-btn">
                                <i class="far fa-copy"></i>
                            </div>
                        </div>
                        
                        <div class="password-item glass-card">
                            <div class="avatar bg-gray-600 bg-opacity-20 text-gray-300">
                                <i class="fab fa-github"></i>
                            </div>
                            <div class="flex-1">
                                <div class="font-medium">GitHub</div>
                                <div class="text-gray-400 text-sm">johndoe</div>
                            </div>
                            <div class="copy-btn">
                                <i class="far fa-copy"></i>
                            </div>
                        </div>
                        
                        <div class="px-4 my-2 flex justify-between items-center">
                            <h2 class="font-semibold">最近添加</h2>
                            <button class="text-sm text-gray-400">查看全部</button>
                        </div>
                        
                        <div class="password-item glass-card">
                            <div class="avatar bg-green-600 bg-opacity-20 text-green-400">
                                <i class="fas fa-university"></i>
                            </div>
                            <div class="flex-1">
                                <div class="font-medium">招商银行</div>
                                <div class="text-gray-400 text-sm">尾号3652</div>
                            </div>
                            <div class="copy-btn">
                                <i class="far fa-copy"></i>
                            </div>
                        </div>
                        
                        <div class="password-item glass-card">
                            <div class="avatar bg-red-600 bg-opacity-20 text-red-400">
                                <i class="fab fa-youtube"></i>
                            </div>
                            <div class="flex-1">
                                <div class="font-medium">YouTube</div>
                                <div class="text-gray-400 text-sm"><EMAIL></div>
                            </div>
                            <div class="copy-btn">
                                <i class="far fa-copy"></i>
                            </div>
                        </div>
                        
                        <div class="password-item glass-card">
                            <div class="avatar bg-purple-600 bg-opacity-20 text-purple-400">
                                <i class="fas fa-shopping-cart"></i>
                            </div>
                            <div class="flex-1">
                                <div class="font-medium">淘宝</div>
                                <div class="text-gray-400 text-sm">johndoe_123</div>
                            </div>
                            <div class="copy-btn">
                                <i class="far fa-copy"></i>
                            </div>
                        </div>
                        
                        <div class="floating-button">
                            <i class="fas fa-plus text-white text-xl"></i>
                        </div>
                    </div>
                    
                    <div class="tab-bar">
                        <div class="tab-item active">
                            <i class="fas fa-home"></i>
                            <span>密码库</span>
                        </div>
                        <div class="tab-item">
                            <i class="fas fa-key"></i>
                            <span>生成器</span>
                        </div>
                        <div class="tab-item">
                            <i class="fas fa-shield-alt"></i>
                            <span>安全</span>
                        </div>
                        <div class="tab-item">
                            <i class="fas fa-cog"></i>
                            <span>设置</span>
                        </div>
                    </div>
                </div>
                <div class="app-frame-title">主界面/密码列表</div>
            </div>

            <!-- Password Details Screen -->
            <div class="app-frame">
                <div class="app-container">
                    <div class="status-bar">
                        <div>9:41</div>
                        <div>
                            <i class="fas fa-signal"></i>
                            <i class="fas fa-wifi ml-1"></i>
                            <i class="fas fa-battery-full ml-1"></i>
                        </div>
                    </div>
                    
                    <div class="screen-container">
                        <div class="p-4 flex items-center">
                            <button class="w-10 h-10 rounded-full flex items-center justify-center bg-opacity-10 bg-white">
                                <i class="fas fa-arrow-left"></i>
                            </button>
                            <h1 class="text-xl font-bold ml-4">密码详情</h1>
                            <div class="ml-auto flex">
                                <button class="w-10 h-10 rounded-full flex items-center justify-center bg-opacity-10 bg-white mr-2">
                                    <i class="fas fa-pen"></i>
                                </button>
                                <button class="w-10 h-10 rounded-full flex items-center justify-center bg-opacity-10 bg-white">
                                    <i class="fas fa-trash-alt text-red-500"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="flex flex-col items-center my-6">
                            <div class="w-20 h-20 rounded-2xl bg-blue-600 bg-opacity-20 flex items-center justify-center mb-3">
                                <i class="fab fa-google text-blue-400 text-4xl"></i>
                            </div>
                            <h2 class="text-xl font-semibold">Google</h2>
                            <p class="text-gray-400">网站账号</p>
                        </div>
                        
                        <div class="px-4">
                            <div class="glass-card p-4 mb-4">
                                <div class="text-gray-400 text-sm mb-1">用户名</div>
                                <div class="flex items-center">
                                    <div class="flex-1 font-medium"><EMAIL></div>
                                    <button class="copy-btn">
                                        <i class="far fa-copy"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="glass-card p-4 mb-4">
                                <div class="text-gray-400 text-sm mb-1">密码</div>
                                <div class="flex items-center">
                                    <div class="flex-1 font-medium">••••••••••••</div>
                                    <button class="copy-btn mr-2">
                                        <i class="far fa-eye"></i>
                                    </button>
                                    <button class="copy-btn">
                                        <i class="far fa-copy"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="glass-card p-4 mb-4">
                                <div class="text-gray-400 text-sm mb-1">网站</div>
                                <div class="flex items-center">
                                    <div class="flex-1 font-medium">https://accounts.google.com</div>
                                    <button class="copy-btn">
                                        <i class="fas fa-external-link-alt"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="glass-card p-4 mb-4">
                                <div class="text-gray-400 text-sm mb-2">双因素认证 (TOTP)</div>
                                <div class="flex items-center mb-3">
                                    <div class="flex-1 font-medium text-2xl tracking-wider">123 456</div>
                                    <div class="w-8 h-8 rounded-full border-2 border-purple-500 flex items-center justify-center">
                                        <div class="w-6 h-6 rounded-full bg-purple-500" style="clip-path: polygon(50% 50%, 50% 0, 100% 0, 100% 100%, 0 100%, 0 0, 50% 0);"></div>
                                    </div>
                                </div>
                                <div class="flex items-center">
                                    <div class="flex-1">
                                        <div class="w-full bg-gray-700 bg-opacity-30 h-1 rounded-full">
                                            <div class="bg-purple-500 h-1 rounded-full" style="width: 65%;"></div>
                                        </div>
                                    </div>
                                    <button class="copy-btn ml-2">
                                        <i class="far fa-copy"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="glass-card p-4 mb-4">
                                <div class="text-gray-400 text-sm mb-1">备注</div>
                                <div class="text-gray-300">
                                    这是我的主要Google账号，用于工作和个人使用。备份邮箱设置为****************。
                                </div>
                            </div>
                            
                            <div class="glass-card p-4 mb-4">
                                <div class="text-gray-400 text-sm mb-3">密码健康度</div>
                                <div class="flex items-center">
                                    <div class="w-10 h-10 rounded-full bg-yellow-500 bg-opacity-20 flex items-center justify-center mr-3">
                                        <i class="fas fa-exclamation-triangle text-yellow-500"></i>
                                    </div>
                                    <div class="flex-1">
                                        <div class="font-medium">中等强度</div>
                                        <div class="text-gray-400 text-sm">该密码已被使用超过1年</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="text-gray-400 text-sm mb-4">
                                最后更新：2023年8月12日
                            </div>
                        </div>
                    </div>
                    
                    <div class="tab-bar">
                        <div class="tab-item active">
                            <i class="fas fa-home"></i>
                            <span>密码库</span>
                        </div>
                        <div class="tab-item">
                            <i class="fas fa-key"></i>
                            <span>生成器</span>
                        </div>
                        <div class="tab-item">
                            <i class="fas fa-shield-alt"></i>
                            <span>安全</span>
                        </div>
                        <div class="tab-item">
                            <i class="fas fa-cog"></i>
                            <span>设置</span>
                        </div>
                    </div>
                </div>
                <div class="app-frame-title">密码详情</div>
            </div>

            <!-- Password Generator Screen -->
            <div class="app-frame">
                <div class="app-container">
                    <div class="status-bar">
                        <div>9:41</div>
                        <div>
                            <i class="fas fa-signal"></i>
                            <i class="fas fa-wifi ml-1"></i>
                            <i class="fas fa-battery-full ml-1"></i>
                        </div>
                    </div>
                    
                    <div class="screen-container">
                        <div class="p-4">
                            <h1 class="text-2xl font-bold">密码生成器</h1>
                            <p class="text-gray-400 text-sm">创建安全且强大的密码</p>
                        </div>
                        
                        <div class="glass-card mx-4 p-6 mb-6">
                            <div class="text-center mb-6">
                                <div class="text-xl font-mono tracking-wide break-all">
                                    XkP7@nL9#tQr2$vZ
                                </div>
                                <div class="strength-meter mt-4">
                                    <div class="strength-meter-fill strength-strong"></div>
                                </div>
                                <div class="text-green-500 text-sm mt-2 font-medium">
                                    强度：极强
                                </div>
                            </div>
                            
                            <div class="flex space-x-3">
                                <button class="flex-1 btn btn-secondary">
                                    <i class="fas fa-redo mr-2"></i>
                                    重新生成
                                </button>
                                <button class="flex-1 btn btn-primary">
                                    <i class="far fa-copy mr-2"></i>
                                    复制
                                </button>
                            </div>
                        </div>
                        
                        <div class="px-4 mb-2">
                            <h2 class="font-semibold">密码设置</h2>
                        </div>
                        
                        <div class="glass-card mx-4 p-4 mb-4">
                            <div class="flex justify-between items-center mb-4">
                                <div>
                                    <div class="font-medium">密码长度</div>
                                    <div class="text-gray-400 text-sm">16个字符</div>
                                </div>
                                <div class="flex items-center">
                                    <button class="w-8 h-8 rounded-full bg-opacity-10 bg-white flex items-center justify-center">
                                        <i class="fas fa-minus"></i>
                                    </button>
                                    <span class="mx-3">16</span>
                                    <button class="w-8 h-8 rounded-full bg-opacity-10 bg-white flex items-center justify-center">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="w-full bg-gray-700 bg-opacity-30 h-2 rounded-full mb-6">
                                <div class="bg-purple-500 h-2 rounded-full" style="width: 60%;"></div>
                            </div>
                            
                            <div class="space-y-4">
                                <div class="flex justify-between items-center">
                                    <div class="font-medium">大写字母 (A-Z)</div>
                                    <label class="toggle-switch">
                                        <input type="checkbox" checked="">
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                                
                                <div class="flex justify-between items-center">
                                    <div class="font-medium">小写字母 (a-z)</div>
                                    <label class="toggle-switch">
                                        <input type="checkbox" checked="">
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                                
                                <div class="flex justify-between items-center">
                                    <div class="font-medium">数字 (0-9)</div>
                                    <label class="toggle-switch">
                                        <input type="checkbox" checked="">
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                                
                                <div class="flex justify-between items-center">
                                    <div class="font-medium">特殊符号 (!@#$%)</div>
                                    <label class="toggle-switch">
                                        <input type="checkbox" checked="">
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                                
                                <div class="flex justify-between items-center">
                                    <div class="font-medium">避免类似字符 (1, l, I)</div>
                                    <label class="toggle-switch">
                                        <input type="checkbox">
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="glass-card mx-4 p-4 mb-4">
                            <div class="flex justify-between items-center">
                                <div class="font-medium">高级选项</div>
                                <i class="fas fa-chevron-down text-gray-400"></i>
                            </div>
                        </div>
                        
                        <div class="mx-4 mb-6">
                            <button class="btn btn-primary w-full">
                                <i class="fas fa-save mr-2"></i>
                                保存为新密码
                            </button>
                        </div>
                        
                        <div class="px-4 mb-2">
                            <h2 class="font-semibold">生成历史</h2>
                        </div>
                        
                        <div class="glass-card mx-4 p-4 mb-2">
                            <div class="font-mono mb-2 break-all">uJ5*dK@pL8mN#rT3</div>
                            <div class="text-gray-400 text-xs">今天 14:32</div>
                        </div>
                        
                        <div class="glass-card mx-4 p-4 mb-2">
                            <div class="font-mono mb-2 break-all">7aBc$9dEf!2gHi@5</div>
                            <div class="text-gray-400 text-xs">今天 11:15</div>
                        </div>
                    </div>
                    
                    <div class="tab-bar">
                        <div class="tab-item">
                            <i class="fas fa-home"></i>
                            <span>密码库</span>
                        </div>
                        <div class="tab-item active">
                            <i class="fas fa-key"></i>
                            <span>生成器</span>
                        </div>
                        <div class="tab-item">
                            <i class="fas fa-shield-alt"></i>
                            <span>安全</span>
                        </div>
                        <div class="tab-item">
                            <i class="fas fa-cog"></i>
                            <span>设置</span>
                        </div>
                    </div>
                </div>
                <div class="app-frame-title">密码生成器</div>
            </div>

            <!-- Security Analysis Screen -->
            <div class="app-frame">
                <div class="app-container">
                    <div class="status-bar">
                        <div>9:41</div>
                        <div>
                            <i class="fas fa-signal"></i>
                            <i class="fas fa-wifi ml-1"></i>
                            <i class="fas fa-battery-full ml-1"></i>
                        </div>
                    </div>
                    
                    <div class="screen-container">
                        <div class="p-4">
                            <h1 class="text-2xl font-bold">安全评分</h1>
                            <p class="text-gray-400 text-sm">分析您的密码健康状况</p>
                        </div>
                        
                        <div class="glass-card mx-4 p-6 mb-6">
                            <div class="text-center mb-6">
                                <div class="security-score bg-gradient-to-br from-yellow-500 to-orange-600 mb-3">
                                    78
                                </div>
                                <div class="font-medium text-lg">良好</div>
                                <div class="text-gray-400 text-sm">
                                    您的密码安全状况优于63%的用户
                                </div>
                            </div>
                            
                            <div class="space-y-3">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 rounded-full bg-red-500 bg-opacity-20 flex items-center justify-center mr-3">
                                        <i class="fas fa-exclamation-circle text-red-500"></i>
                                    </div>
                                    <div class="flex-1">
                                        <div class="font-medium">3个弱密码</div>
                                        <div class="text-gray-400 text-sm">这些密码容易被破解</div>
                                    </div>
                                    <i class="fas fa-chevron-right text-gray-500"></i>
                                </div>
                                
                                <div class="flex items-center">
                                    <div class="w-8 h-8 rounded-full bg-yellow-500 bg-opacity-20 flex items-center justify-center mr-3">
                                        <i class="fas fa-clone text-yellow-500"></i>
                                    </div>
                                    <div class="flex-1">
                                        <div class="font-medium">5个重复使用的密码</div>
                                        <div class="text-gray-400 text-sm">在多个网站使用相同密码</div>
                                    </div>
                                    <i class="fas fa-chevron-right text-gray-500"></i>
                                </div>
                                
                                <div class="flex items-center">
                                    <div class="w-8 h-8 rounded-full bg-green-500 bg-opacity-20 flex items-center justify-center mr-3">
                                        <i class="fas fa-shield-alt text-green-500"></i>
                                    </div>
                                    <div class="flex-1">
                                        <div class="font-medium">20个强密码</div>
                                        <div class="text-gray-400 text-sm">这些密码足够安全</div>
                                    </div>
                                    <i class="fas fa-chevron-right text-gray-500"></i>
                                </div>
                            </div>
                        </div>
                        
                        <div class="px-4 mb-2">
                            <h2 class="font-semibold">需要注意</h2>
                        </div>
                        
                        <div class="glass-card mx-4 p-4 mb-3">
                            <div class="flex items-center">
                                <div class="w-10 h-10 rounded-xl bg-red-600 bg-opacity-20 flex items-center justify-center mr-3">
                                    <i class="fab fa-twitter text-red-400"></i>
                                </div>
                                <div class="flex-1">
                                    <div class="font-medium">Twitter</div>
                                    <div class="text-gray-400 text-sm">弱密码</div>
                                </div>
                                <button class="btn btn-secondary text-sm py-2 px-3">
                                    修改
                                </button>
                            </div>
                        </div>
                        
                        <div class="glass-card mx-4 p-4 mb-3">
                            <div class="flex items-center">
                                <div class="w-10 h-10 rounded-xl bg-red-600 bg-opacity-20 flex items-center justify-center mr-3">
                                    <i class="fas fa-shopping-bag text-red-400"></i>
                                </div>
                                <div class="flex-1">
                                    <div class="font-medium">亚马逊</div>
                                    <div class="text-gray-400 text-sm">数据泄露风险</div>
                                </div>
                                <button class="btn btn-secondary text-sm py-2 px-3">
                                    修改
                                </button>
                            </div>
                        </div>
                        
                        <div class="glass-card mx-4 p-4 mb-3">
                            <div class="flex items-center">
                                <div class="w-10 h-10 rounded-xl bg-yellow-600 bg-opacity-20 flex items-center justify-center mr-3">
                                    <i class="fab fa-facebook text-yellow-400"></i>
                                </div>
                                <div class="flex-1">
                                    <div class="font-medium">Facebook</div>
                                    <div class="text-gray-400 text-sm">重复使用的密码</div>
                                </div>
                                <button class="btn btn-secondary text-sm py-2 px-3">
                                    修改
                                </button>
                            </div>
                        </div>
                        
                        <div class="px-4 mt-6 mb-2">
                            <h2 class="font-semibold">安全提示</h2>
                        </div>
                        
                        <div class="glass-card mx-4 p-4 mb-3">
                            <div class="flex items-center">
                                <div class="w-10 h-10 rounded-full bg-purple-500 bg-opacity-20 flex items-center justify-center mr-3">
                                    <i class="fas fa-fingerprint text-purple-400"></i>
                                </div>
                                <div class="flex-1">
                                    <div class="font-medium">启用两步验证</div>
                                    <div class="text-gray-400 text-sm">为重要账号增加安全层级</div>
                                </div>
                                <i class="fas fa-chevron-right text-gray-500"></i>
                            </div>
                        </div>
                        
                        <div class="glass-card mx-4 p-4 mb-3">
                            <div class="flex items-center">
                                <div class="w-10 h-10 rounded-full bg-blue-500 bg-opacity-20 flex items-center justify-center mr-3">
                                    <i class="fas fa-sync-alt text-blue-400"></i>
                                </div>
                                <div class="flex-1">
                                    <div class="font-medium">定期更换密码</div>
                                    <div class="text-gray-400 text-sm">建议每90天更新一次重要密码</div>
                                </div>
                                <i class="fas fa-chevron-right text-gray-500"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="tab-bar">
                        <div class="tab-item">
                            <i class="fas fa-home"></i>
                            <span>密码库</span>
                        </div>
                        <div class="tab-item">
                            <i class="fas fa-key"></i>
                            <span>生成器</span>
                        </div>
                        <div class="tab-item active">
                            <i class="fas fa-shield-alt"></i>
                            <span>安全</span>
                        </div>
                        <div class="tab-item">
                            <i class="fas fa-cog"></i>
                            <span>设置</span>
                        </div>
                    </div>
                </div>
                <div class="app-frame-title">安全分析</div>
            </div>

            <!-- Add/Edit Password Screen -->
            <div class="app-frame">
                <div class="app-container">
                    <div class="status-bar">
                        <div>9:41</div>
                        <div>
                            <i class="fas fa-signal"></i>
                            <i class="fas fa-wifi ml-1"></i>
                            <i class="fas fa-battery-full ml-1"></i>
                        </div>
                    </div>
                    
                    <div class="screen-container">
                        <div class="p-4 flex items-center">
                            <button class="w-10 h-10 rounded-full flex items-center justify-center bg-opacity-10 bg-white">
                                <i class="fas fa-arrow-left"></i>
                            </button>
                            <h1 class="text-xl font-bold ml-4">添加密码</h1>
                            <button class="ml-auto btn btn-primary py-2 px-4">
                                保存
                            </button>
                        </div>
                        
                        <div class="px-4 pb-6">
                            <div class="glass-card p-6 mb-6">
                                <div class="flex justify-center mb-6">
                                    <div class="w-20 h-20 rounded-2xl bg-gray-600 bg-opacity-20 flex items-center justify-center">
                                        <i class="fas fa-globe text-gray-400 text-3xl"></i>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">类型</label>
                                    <select class="form-input">
                                        <option>网站</option>
                                        <option>应用</option>
                                        <option>银行卡</option>
                                        <option>身份证</option>
                                        <option>安全笔记</option>
                                        <option>其他</option>
                                    </select>
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">名称</label>
                                    <input type="text" class="form-input" placeholder="例如：Gmail, 淘宝...">
                                </div>
                            </div>
                            
                            <div class="glass-card p-6 mb-6">
                                <div class="form-group">
                                    <label class="form-label">用户名/邮箱</label>
                                    <input type="text" class="form-input" placeholder="输入账号用户名或邮箱...">
                                </div>
                                
                                <div class="form-group">
                                    <div class="flex justify-between mb-2">
                                        <label class="form-label">密码</label>
                                        <button class="text-purple-400 text-sm font-medium">
                                            生成
                                        </button>
                                    </div>
                                    <div class="relative">
                                        <input type="password" class="form-input pr-12" value="MySecurePass123">
                                        <button class="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400">
                                            <i class="far fa-eye"></i>
                                        </button>
                                    </div>
                                    <div class="mt-2">
                                        <div class="strength-meter">
                                            <div class="strength-meter-fill strength-medium"></div>
                                        </div>
                                        <div class="text-yellow-500 text-xs mt-1">
                                            中等强度 - 建议添加特殊字符
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">网址</label>
                                    <input type="text" class="form-input" placeholder="https://...">
                                </div>
                            </div>
                            
                            <div class="glass-card p-6 mb-6">
                                <div class="flex justify-between items-center mb-4">
                                    <div class="font-medium">双因素认证 (TOTP)</div>
                                    <label class="toggle-switch">
                                        <input type="checkbox">
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                                
                                <div class="flex justify-between items-center mb-4">
                                    <div class="font-medium">添加到收藏</div>
                                    <label class="toggle-switch">
                                        <input type="checkbox">
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">备注</label>
                                    <textarea class="form-input" rows="3" placeholder="添加备注信息..."></textarea>
                                </div>
                            </div>
                            
                            <div class="glass-card p-6 mb-6">
                                <div class="flex justify-between items-center">
                                    <div class="font-medium">自定义字段</div>
                                    <button class="w-8 h-8 rounded-full bg-opacity-10 bg-white flex items-center justify-center">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="tab-bar">
                        <div class="tab-item active">
                            <i class="fas fa-home"></i>
                            <span>密码库</span>
                        </div>
                        <div class="tab-item">
                            <i class="fas fa-key"></i>
                            <span>生成器</span>
                        </div>
                        <div class="tab-item">
                            <i class="fas fa-shield-alt"></i>
                            <span>安全</span>
                        </div>
                        <div class="tab-item">
                            <i class="fas fa-cog"></i>
                            <span>设置</span>
                        </div>
                    </div>
                </div>
                <div class="app-frame-title">添加/编辑密码</div>
            </div>

            <!-- Settings Screen -->
            <div class="app-frame">
                <div class="app-container">
                    <div class="status-bar">
                        <div>9:41</div>
                        <div>
                            <i class="fas fa-signal"></i>
                            <i class="fas fa-wifi ml-1"></i>
                            <i class="fas fa-battery-full ml-1"></i>
                        </div>
                    </div>
                    
                    <div class="screen-container">
                        <div class="p-4">
                            <h1 class="text-2xl font-bold">设置</h1>
                            <p class="text-gray-400 text-sm">管理您的账户和偏好设置</p>
                        </div>
                        
                        <div class="glass-card mx-4 p-4 mb-6">
                            <div class="flex items-center">
                                <div class="w-12 h-12 rounded-full overflow-hidden mr-4">
                                    <div class="w-full h-full bg-gradient-to-br from-purple-500 to-blue-500 flex items-center justify-center">
                                        <span class="text-white font-medium">KL</span>
                                    </div>
                                </div>
                                <div class="flex-1">
                                    <div class="font-semibold">Kevin Lin</div>
                                    <div class="text-gray-400 text-sm"><EMAIL></div>
                                </div>
                                <button class="w-8 h-8 rounded-full bg-opacity-10 bg-white flex items-center justify-center">
                                    <i class="fas fa-pen text-sm"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="px-4 mb-2">
                            <h2 class="font-semibold text-gray-400 text-sm">账户</h2>
                        </div>
                        
                        <div class="glass-card mx-4 divide-y divide-gray-700 divide-opacity-30 mb-6">
                            <div class="p-4 flex items-center">
                                <div class="w-8 h-8 rounded-full bg-purple-500 bg-opacity-20 flex items-center justify-center mr-3">
                                    <i class="fas fa-key text-purple-400"></i>
                                </div>
                                <div class="flex-1">
                                    <div class="font-medium">更改主密码</div>
                                </div>
                                <i class="fas fa-chevron-right text-gray-500"></i>
                            </div>
                            
                            <div class="p-4 flex items-center">
                                <div class="w-8 h-8 rounded-full bg-blue-500 bg-opacity-20 flex items-center justify-center mr-3">
                                    <i class="fas fa-fingerprint text-blue-400"></i>
                                </div>
                                <div class="flex-1">
                                    <div class="font-medium">生物识别</div>
                                </div>
                                <label class="toggle-switch">
                                    <input type="checkbox" checked="">
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                            
                            <div class="p-4 flex items-center">
                                <div class="w-8 h-8 rounded-full bg-green-500 bg-opacity-20 flex items-center justify-center mr-3">
                                    <i class="fas fa-cloud text-green-400"></i>
                                </div>
                                <div class="flex-1">
                                    <div class="font-medium">云同步</div>
                                    <div class="text-gray-400 text-sm">上次同步：今天 16:42</div>
                                </div>
                                <label class="toggle-switch">
                                    <input type="checkbox" checked="">
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                        
                        <div class="px-4 mb-2">
                            <h2 class="font-semibold text-gray-400 text-sm">安全</h2>
                        </div>
                        
                        <div class="glass-card mx-4 divide-y divide-gray-700 divide-opacity-30 mb-6">
                            <div class="p-4 flex items-center">
                                <div class="w-8 h-8 rounded-full bg-yellow-500 bg-opacity-20 flex items-center justify-center mr-3">
                                    <i class="fas fa-lock text-yellow-400"></i>
                                </div>
                                <div class="flex-1">
                                    <div class="font-medium">自动锁定</div>
                                    <div class="text-gray-400 text-sm">5分钟不活动后</div>
                                </div>
                                <i class="fas fa-chevron-right text-gray-500"></i>
                            </div>
                            
                            <div class="p-4 flex items-center">
                                <div class="w-8 h-8 rounded-full bg-red-500 bg-opacity-20 flex items-center justify-center mr-3">
                                    <i class="fas fa-shield-alt text-red-400"></i>
                                </div>
                                <div class="flex-1">
                                    <div class="font-medium">安全审计</div>
                                    <div class="text-gray-400 text-sm">每周扫描</div>
                                </div>
                                <i class="fas fa-chevron-right text-gray-500"></i>
                            </div>
                            
                            <div class="p-4 flex items-center">
                                <div class="w-8 h-8 rounded-full bg-orange-500 bg-opacity-20 flex items-center justify-center mr-3">
                                    <i class="fas fa-bell text-orange-400"></i>
                                </div>
                                <div class="flex-1">
                                    <div class="font-medium">安全通知</div>
                                </div>
                                <label class="toggle-switch">
                                    <input type="checkbox" checked="">
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                        
                        <div class="px-4 mb-2">
                            <h2 class="font-semibold text-gray-400 text-sm">导入 / 导出</h2>
                        </div>
                        
                        <div class="glass-card mx-4 divide-y divide-gray-700 divide-opacity-30 mb-6">
                            <div class="p-4 flex items-center">
                                <div class="w-8 h-8 rounded-full bg-indigo-500 bg-opacity-20 flex items-center justify-center mr-3">
                                    <i class="fas fa-file-import text-indigo-400"></i>
                                </div>
                                <div class="flex-1">
                                    <div class="font-medium">导入</div>
                                    <div class="text-gray-400 text-sm">支持KeePass等多种格式</div>
                                </div>
                                <i class="fas fa-chevron-right text-gray-500"></i>
                            </div>
                            
                            <div class="p-4 flex items-center">
                                <div class="w-8 h-8 rounded-full bg-purple-500 bg-opacity-20 flex items-center justify-center mr-3">
                                    <i class="fas fa-file-export text-purple-400"></i>
                                </div>
                                <div class="flex-1">
                                    <div class="font-medium">导出</div>
                                    <div class="text-gray-400 text-sm">加密备份您的密码库</div>
                                </div>
                                <i class="fas fa-chevron-right text-gray-500"></i>
                            </div>
                        </div>
                        
                        <div class="px-4 mb-2">
                            <h2 class="font-semibold text-gray-400 text-sm">其他</h2>
                        </div>
                        
                        <div class="glass-card mx-4 divide-y divide-gray-700 divide-opacity-30 mb-6">
                            <div class="p-4 flex items-center">
                                <div class="w-8 h-8 rounded-full bg-blue-500 bg-opacity-20 flex items-center justify-center mr-3">
                                    <i class="fas fa-palette text-blue-400"></i>
                                </div>
                                <div class="flex-1">
                                    <div class="font-medium">主题设置</div>
                                    <div class="text-gray-400 text-sm">暗黑模式</div>
                                </div>
                                <i class="fas fa-chevron-right text-gray-500"></i>
                            </div>
                            
                            <div class="p-4 flex items-center">
                                <div class="w-8 h-8 rounded-full bg-green-500 bg-opacity-20 flex items-center justify-center mr-3">
                                    <i class="fas fa-question-circle text-green-400"></i>
                                </div>
                                <div class="flex-1">
                                    <div class="font-medium">帮助与支持</div>
                                </div>
                                <i class="fas fa-chevron-right text-gray-500"></i>
                            </div>
                            
                            <div class="p-4 flex items-center">
                                <div class="w-8 h-8 rounded-full bg-gray-500 bg-opacity-20 flex items-center justify-center mr-3">
                                    <i class="fas fa-info-circle text-gray-400"></i>
                                </div>
                                <div class="flex-1">
                                    <div class="font-medium">关于</div>
                                    <div class="text-gray-400 text-sm">版本 1.2.3</div>
                                </div>
                                <i class="fas fa-chevron-right text-gray-500"></i>
                            </div>
                        </div>
                        
                        <div class="p-4 text-center">
                            <button class="text-red-500 font-medium">
                                退出登录
                            </button>
                        </div>
                    </div>
                    
                    <div class="tab-bar">
                        <div class="tab-item">
                            <i class="fas fa-home"></i>
                            <span>密码库</span>
                        </div>
                        <div class="tab-item">
                            <i class="fas fa-key"></i>
                            <span>生成器</span>
                        </div>
                        <div class="tab-item">
                            <i class="fas fa-shield-alt"></i>
                            <span>安全</span>
                        </div>
                        <div class="tab-item active">
                            <i class="fas fa-cog"></i>
                            <span>设置</span>
                        </div>
                    </div>
                </div>
                <div class="app-frame-title">设置</div>
            </div>
        </div>
    </div>

    <!-- Index.html to integrate all screens -->
    <script>
        // 此处可以添加交互效果和功能逻辑
        // 例如，切换选项卡、显示/隐藏密码、滑动操作等
    </script>


</body></html>