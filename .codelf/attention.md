## Development Guidelines

### Framework and Language
> 项目已完成从HTML原型到微信小程序的迁移，使用 @vue-mini 框架开发。

**当前技术栈 (微信小程序):**
- @vue-mini/core: Vue 3 组合式API的小程序框架
- @vue-mini/pinia: 状态管理库
- TypeScript: 类型安全的开发体验
- weapp-vite: 现代化构建工具，支持热重载
- weapp-tailwindcss: 小程序适配的 Tailwind CSS v4
- WXML/WXSS: 小程序原生模板和样式
- 重要注意事项:
	* 使用 Vue 3 组合式API (ref, computed, watch)
	* 玻璃拟态效果通过 CSS 变量和 backdrop-filter 实现
	* 响应式单位使用 rpx，通过 weapp-tailwindcss 自动转换
	* 移动端优先设计，适配不同屏幕尺寸

**框架特性和优势:**
- Vue 3 组合式API: 更好的逻辑复用和类型推导
- 现代化构建: weapp-vite 提供快速的开发体验
- 样式系统: Tailwind CSS v4 + 自定义 CSS 变量
- 状态管理: Pinia 提供响应式状态管理
- 类型安全: 完整的 TypeScript 支持

### Code Abstraction and Reusability
> 在开发过程中，优先考虑代码抽象和复用性，确保模块化和组件化功能。在重新发明轮子之前，尝试寻找现有解决方案。
> 当前原型阶段的可复用组件和未来小程序开发的组件规划。

**模块化设计原则:**
- 单一职责: 每个模块只负责一个功能
- 高内聚，低耦合: 相关功能集中，减少模块间依赖
- 稳定接口: 对外暴露稳定接口，内部实现可变

**当前原型中的可复用样式组件:**
```
prototype.html 中的关键样式类:
- .glass-card           // 玻璃拟态卡片组件
- .btn, .btn-primary    // 按钮组件系列
- .form-input, .form-group // 表单组件
- .password-item        // 密码条目组件
- .tab-bar, .tab-item   // 底部导航组件
- .modal, .modal-content // 模态框组件
- .toggle-switch        // 开关组件
- .strength-meter       // 密码强度指示器
```

**已实现的小程序组件架构:**
```
src/components/
- common/              // 通用组件
    - glass-card/      // 玻璃拟态卡片 (已完成)
    - custom-button/   // 自定义按钮 (已完成)
    - form-input/      // 表单输入框 (已完成)
    - toggle-switch/   // 开关组件 (已完成)
    - modal/           // 模态框组件 (已完成)
    - fa-icon/         // FontAwesome 图标组件 (已完成)
    - floating-button/ // 浮动按钮 (已完成)
- business/            // 业务组件
    - password-item/   // 密码条目 (已完成)
    - strength-meter/  // 密码强度指示器 (已完成)
    - search-bar/      // 搜索栏 (已完成)
src/utils/             // 工具函数
- crypto.ts           // 加密相关工具 (已完成)
- storage.ts          // 安全存储封装 (已完成)
- validator.ts        // 数据验证 (已完成)
- keepass.ts          // KeePass 导入工具 (已完成)
```

### Coding Standards and Tools
**当前原型阶段工具:**
- 浏览器开发者工具 // HTML/CSS调试和性能分析
- VS Code + Live Server // 本地开发和实时预览
- Tailwind CSS IntelliSense // CSS类名自动补全

**未来小程序开发工具:**
- 微信开发者工具 // 小程序开发、调试、预览
- ESLint // JavaScript代码检查和规范
- Prettier // 代码格式化
- miniprogram-ci // 小程序CI/CD工具

**命名和结构约定:**
- 语义化命名: 变量/函数名应清楚表达其用途
- 一致的命名风格:
  * JavaScript使用camelCase
  * CSS类名使用kebab-case
  * 小程序页面和组件使用kebab-case
  * 常量使用UPPER_SNAKE_CASE
- 目录结构遵循功能职责划分
- 文件命名规范:
  * 页面文件: login.wxml, password-list.wxml
  * 组件文件: glass-card.wxml, password-item.wxml
  * 工具文件: crypto-utils.js, storage-helper.js

### Frontend-Backend Collaboration Standards
**API设计和文档规范:**
- RESTful设计原则
	* 使用HTTP方法 (GET, POST, PUT, DELETE) 表示操作
	* 统一的URL命名规范: /api/v1/passwords, /api/v1/auth
	* 状态码规范: 200成功, 401未授权, 403禁止, 404未找到, 500服务器错误
- 及时的接口文档更新
	* 使用Swagger/OpenAPI文档化API端点、参数和响应
	* 包含请求示例和响应示例
	* 错误码和错误信息说明
- 统一的错误处理规范
	* 标准错误响应格式: {code, message, data}
	* 国际化错误信息支持

**数据流管理:**
- 清晰的前端状态管理
	* 小程序使用Page/Component的data进行状态管理
	* 全局状态使用globalData或状态管理库
	* 敏感数据(密码)仅在内存中临时存储
- 前后端数据验证
	* 前端验证用户体验，后端验证安全保障
	* 统一的验证规则和错误提示
	* 密码强度、邮箱格式等通用验证
- 标准化异步操作处理
	* 统一的API调用封装
	* 加载状态和错误状态处理
	* 请求重试和超时机制

### Performance and Security
**性能优化重点:**
- 资源加载优化
	* 小程序分包加载，按需引入组件
	* 图片懒加载和压缩优化
	* CDN资源使用（Tailwind CSS, Font Awesome）
- 渲染性能优化
	* 长列表使用虚拟滚动或分页加载
	* 避免频繁的setData操作
	* 合理使用小程序的生命周期钩子
- 合理使用缓存
	* 密码数据加密后本地存储
	* API响应缓存策略
	* 静态资源缓存

**安全措施 (密码管理器核心要求):**
- 输入验证和过滤
	* 严格验证用户输入，防止XSS攻击
	* 密码强度验证和安全提示
	* 防止SQL注入和代码注入
- 敏感信息保护
	* 主密码使用强加密算法(AES-256)
	* 密码数据端到端加密
	* 生物识别认证集成
	* 会话超时和自动锁定
- 访问控制机制
	* 基于角色的访问控制
	* API接口权限验证
	* 设备绑定和异常登录检测
	* 数据备份和恢复的安全性